{".class": "MypyFile", "_fullname": "IPython.core.completer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AnyCompletion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "name": "AnyCompletion", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}}, "AnyMatcherCompletion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.core.completer.AnyMatcherCompletion", "line": 624, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._JediCompletionLike"}, "IPython.core.completer.SimpleCompletion"], "uses_pep604_syntax": false}}}, "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef", "module_public": false}, "Completer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["traitlets.config.configurable.Configurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.Completer", "name": "Completer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.Completer", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "_ATTR_MATCH_RE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer._ATTR_MATCH_RE", "name": "_ATTR_MATCH_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "namespace", "global_namespace", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer.__init__", "name": "__init__", "type": null}}, "_attr_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "include_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer._attr_matches", "name": "_attr_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "include_prefix"], "arg_types": ["IPython.core.completer.Completer", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_attr_matches of Completer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evaluate_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer._evaluate_expr", "name": "_evaluate_expr", "type": null}}, "_trim_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer._trim_expr", "name": "_trim_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["IPython.core.completer.Completer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_trim_expr of Completer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attr_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer.attr_matches", "name": "attr_matches", "type": null}}, "auto_close_dict_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer.auto_close_dict_keys", "name": "auto_close_dict_keys", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "backslash_combining_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer.backslash_combining_completions", "name": "backslash_combining_completions", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer.complete", "name": "complete", "type": null}}, "custom_matchers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completer.custom_matchers", "name": "custom_matchers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer.debug", "name": "debug", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "evaluation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer.evaluation", "name": "evaluation", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Enum"}}}, "global_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completer.global_matches", "name": "global_matches", "type": null}}, "global_namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completer.global_namespace", "name": "global_namespace", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "greedy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer.greedy", "name": "greedy", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "jedi_compute_type_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer.jedi_compute_type_timeout", "name": "jedi_compute_type_timeout", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "matches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completer.matches", "name": "matches", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completer.namespace", "name": "namespace", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "use_jedi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.Completer.use_jedi", "name": "use_jedi", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "use_main_ns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completer.use_main_ns", "name": "use_main_ns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.Completer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.Completer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Completion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.Completion", "name": "Completion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.Completion", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completion.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["IPython.core.completer.Completion", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Completion", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completion.__hash__", "name": "__hash__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "start", "end", "text", "type", "_origin", "signature"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "start", "end", "text", "type", "_origin", "signature"], "arg_types": ["IPython.core.completer.Completion", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Completion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.Completion.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "IPython.core.completer.Completion.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completion._origin", "name": "_origin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "end": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completion.end", "name": "end", "type": "builtins.int"}}, "signature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completion.signature", "name": "signature", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completion.start", "name": "start", "type": "builtins.int"}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completion.text", "name": "text", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.Completion.type", "name": "type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.Completion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.Completion", "values": [], "variance": 0}, "slots": ["_origin", "end", "signature", "start", "text", "type"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompletionContext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.CompletionContext", "name": "CompletionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.CompletionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 641, "name": "token", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 644, "name": "full_text", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 647, "name": "cursor_position", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 650, "name": "cursor_line", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 656, "name": "limit", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.CompletionContext", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "IPython.core.completer.CompletionContext.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "token", "full_text", "cursor_position", "cursor_line", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.CompletionContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "token", "full_text", "cursor_position", "cursor_line", "limit"], "arg_types": ["IPython.core.completer.CompletionContext", "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CompletionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "IPython.core.completer.CompletionContext.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full_text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cursor_position"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cursor_line"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "limit"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["token", "full_text", "cursor_position", "cursor_line", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.completer.CompletionContext.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["token", "full_text", "cursor_position", "cursor_line", "limit"], "arg_types": ["builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CompletionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "IPython.core.completer.CompletionContext.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["token", "full_text", "cursor_position", "cursor_line", "limit"], "arg_types": ["builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CompletionContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "cursor_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.core.completer.CompletionContext.cursor_line", "name": "cursor_line", "type": "builtins.int"}}, "cursor_position": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.core.completer.CompletionContext.cursor_position", "name": "cursor_position", "type": "builtins.int"}}, "full_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.core.completer.CompletionContext.full_text", "name": "full_text", "type": "builtins.str"}}, "limit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.core.completer.CompletionContext.limit", "name": "limit", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "line_with_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.completer.CompletionContext.line_with_cursor", "name": "line_with_cursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "line_with_cursor of CompletionContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.CompletionContext.line_with_cursor", "name": "line_with_cursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "line_with_cursor of CompletionContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "text_until_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.completer.CompletionContext.text_until_cursor", "name": "text_until_cursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text_until_cursor of CompletionContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.CompletionContext.text_until_cursor", "name": "text_until_cursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text_until_cursor of CompletionContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.core.completer.CompletionContext.token", "name": "token", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.CompletionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.CompletionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompletionSplitter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.CompletionSplitter", "name": "CompletionSplitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.CompletionSplitter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.CompletionSplitter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "delims"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.CompletionSplitter.__init__", "name": "__init__", "type": null}}, "_delim_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.CompletionSplitter._delim_expr", "name": "_delim_expr", "type": {".class": "NoneType"}}}, "_delim_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.CompletionSplitter._delim_re", "name": "_delim_re", "type": {".class": "NoneType"}}}, "_delims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.CompletionSplitter._delims", "name": "_delims", "type": "builtins.str"}}, "delims": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "IPython.core.completer.CompletionSplitter.delims", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "IPython.core.completer.CompletionSplitter.delims", "name": "delims", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.CompletionSplitter.delims", "name": "delims", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.CompletionSplitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delims of CompletionSplitter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delims"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.CompletionSplitter.delims", "name": "delims", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "delims", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.CompletionSplitter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delims", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "split_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "line", "cursor_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.CompletionSplitter.split_line", "name": "split_line", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.CompletionSplitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.CompletionSplitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Configurable": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.configurable.Configurable", "kind": "Gdef", "module_public": false}, "DELIMS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.DELIMS", "name": "DELIMS", "type": "builtins.str"}}, "DICT_MATCHER_REGEX": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.DICT_MATCHER_REGEX", "name": "DICT_MATCHER_REGEX", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "DictTrait": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Dict", "kind": "Gdef", "module_public": false}, "ESC_MAGIC": {".class": "SymbolTableNode", "cross_ref": "IPython.core.inputtransformer2.ESC_MAGIC", "kind": "Gdef", "module_public": false}, "Enum": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Enum", "kind": "Gdef", "module_public": false}, "EvaluationContext": {".class": "SymbolTableNode", "cross_ref": "IPython.core.guarded_eval.EvaluationContext", "kind": "Gdef", "module_public": false}, "GENERATING_DOCUMENTATION": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.docs.GENERATING_DOCUMENTATION", "kind": "Gdef", "module_public": false}, "GREEDY_DELIMS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.GREEDY_DELIMS", "name": "GREEDY_DELIMS", "type": "builtins.str"}}, "IPCompleter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.completer.Completer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.IPCompleter", "name": "IPCompleter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.IPCompleter", "IPython.core.completer.Completer", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__funcParamsRegex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.__funcParamsRegex", "name": "__funcParamsRegex", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "shell", "namespace", "global_namespace", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.__init__", "name": "__init__", "type": null}}, "_arrange_and_extract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "results", "skip_matchers", "abort_if_offset_changes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._arrange_and_extract", "name": "_arrange_and_extract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "results", "skip_matchers", "abort_if_offset_changes"], "arg_types": ["IPython.core.completer.IPCompleter", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher<PERSON>ult"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arrange_and_extract of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_backslash_combining_matchers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter._backslash_combining_matchers", "name": "_backslash_combining_matchers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_clean_glob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._clean_glob", "name": "_clean_glob", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clean_glob of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_clean_glob_win32": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._clean_glob_win32", "name": "_clean_glob_win32", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clean_glob_win32 of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "cursor_line", "cursor_pos", "line_buffer", "text", "full_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._complete", "name": "_complete", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "cursor_line", "cursor_pos", "line_buffer", "text", "full_text"], "arg_types": ["IPython.core.completer.IPCompleter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_complete of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._CompleteResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "full_text", "offset", "_timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._completions", "name": "_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "full_text", "offset", "_timeout"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_completions of IPCompleter", "ret_type": {".class": "Instance", "args": ["IPython.core.completer.Completion"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deduplicate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.completer.IPCompleter._deduplicate", "name": "_deduplicate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matches"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._deduplicate", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deduplicate of IPCompleter", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._deduplicate", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._deduplicate", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter._deduplicate", "name": "_deduplicate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matches"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._deduplicate", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deduplicate of IPCompleter", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._deduplicate", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._deduplicate", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}]}}}}, "_default_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._default_arguments", "name": "_default_arguments", "type": null}}, "_default_arguments_from_docstring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._default_arguments_from_docstring", "name": "_default_arguments_from_docstring", "type": null}}, "_get_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.completer.IPCompleter._get_keys", "name": "_get_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_keys of IPCompleter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter._get_keys", "name": "_get_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_keys of IPCompleter", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_greedy_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter._greedy_changed", "name": "_greedy_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter._greedy_changed", "name": "_greedy_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_jedi_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter._jedi_matcher", "name": "_jedi_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_jedi_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._JediMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter._jedi_matcher", "name": "_jedi_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_jedi_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor_column", "cursor_line", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter._jedi_matches", "name": "_jedi_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cursor_column", "cursor_line", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_jedi_matches of IPCompleter", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._JediCompletionLike"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_limit_to_all_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter._limit_to_all_changed", "name": "_limit_to_all_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter._limit_to_all_changed", "name": "_limit_to_all_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_sort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.completer.IPCompleter._sort", "name": "_sort", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matches"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._sort", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sort of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._sort", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter._sort", "name": "_sort", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matches"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._sort", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sort of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.AnyCompletion", "id": -1, "name": "AnyCompletion", "namespace": "IPython.core.completer.IPCompleter._sort", "upper_bound": "builtins.object", "values": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.AnyMatcherCompletion"}, "IPython.core.completer.Completion"], "variance": 0}]}}}}, "_unicode_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter._unicode_names", "name": "_unicode_names", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "all_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.all_completions", "name": "all_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_completions of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clean_glob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.clean_glob", "name": "clean_glob", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "text", "line_buffer", "cursor_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.complete", "name": "complete", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "text", "line_buffer", "cursor_pos"], "arg_types": ["IPython.core.completer.IPCompleter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "complete of IPCompleter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.completions", "name": "completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "text", "offset"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "completions of IPCompleter", "ret_type": {".class": "Instance", "args": ["IPython.core.completer.Completion"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "custom_completer_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.custom_completer_matcher", "name": "custom_completer_matcher", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.custom_completer_matcher", "name": "custom_completer_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "custom_completers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.custom_completers", "name": "custom_completers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dict_key_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.dict_key_matcher", "name": "dict_key_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dict_key_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.dict_key_matcher", "name": "dict_key_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "dict_key_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.dict_key_matches", "name": "dict_key_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dict_key_matches of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dict_keys_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.dict_keys_only", "name": "dict_keys_only", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "disable_matchers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.disable_matchers", "name": "disable_matchers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "dispatch_custom_completer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.dispatch_custom_completer", "name": "dispatch_custom_completer", "type": null}}, "docstring_kwd_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.docstring_kwd_re", "name": "docstring_kwd_re", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "docstring_sig_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.docstring_sig_re", "name": "docstring_sig_re", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dumb_terminal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.dumb_terminal", "name": "dumb_terminal", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "file_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.file_matcher", "name": "file_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.file_matcher", "name": "file_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "file_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.file_matches", "name": "file_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "file_matches of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fwd_unicode_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.fwd_unicode_match", "name": "fwd_unicode_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fwd_unicode_match of IPCompleter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fwd_unicode_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.fwd_unicode_matcher", "name": "fwd_unicode_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fwd_unicode_matcher of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.fwd_unicode_matcher", "name": "fwd_unicode_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "glob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.glob", "name": "glob", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "latex_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.latex_matches", "name": "latex_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "latex_matches of IPCompleter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "latex_name_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.latex_name_matcher", "name": "latex_name_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "latex_name_matcher of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.latex_name_matcher", "name": "latex_name_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "limit_to__all__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.limit_to__all__", "name": "limit_to__all__", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "line_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.line_buffer", "name": "line_buffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "magic_arg_matchers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.magic_arg_matchers", "name": "magic_arg_matchers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "magic_color_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.magic_color_matcher", "name": "magic_color_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magic_color_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.magic_color_matcher", "name": "magic_color_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "magic_color_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.magic_color_matches", "name": "magic_color_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magic_color_matches of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "magic_config_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.magic_config_matcher", "name": "magic_config_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magic_config_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.magic_config_matcher", "name": "magic_config_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "magic_config_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.magic_config_matches", "name": "magic_config_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magic_config_matches of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "magic_escape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.magic_escape", "name": "magic_escape", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "magic_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.magic_matcher", "name": "magic_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magic_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.magic_matcher", "name": "magic_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "magic_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.magic_matches", "name": "magic_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "magic_matches of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matchers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.completer.IPCompleter.matchers", "name": "matchers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.IPCompleter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matchers of IPCompleter", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.matchers", "name": "matchers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.IPCompleter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matchers of IPCompleter", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "merge_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.merge_completions", "name": "merge_completions", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "omit__names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.omit__names", "name": "omit__names", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Enum"}}}, "profile_completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.profile_completions", "name": "profile_completions", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "profiler_output_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.profiler_output_dir", "name": "profiler_output_dir", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "python_func_kw_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.python_func_kw_matcher", "name": "python_func_kw_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "python_func_kw_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.python_func_kw_matcher", "name": "python_func_kw_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "python_func_kw_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.IPCompleter.python_func_kw_matches", "name": "python_func_kw_matches", "type": null}}, "python_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.python_matcher", "name": "python_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "python_matcher of IPCompleter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.python_matcher", "name": "python_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "python_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.python_matches", "name": "python_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer.IPCompleter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "python_matches of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.python_matches", "name": "python_matches", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "shell": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.shell", "name": "shell", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "space_name_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.space_name_re", "name": "space_name_re", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "splitter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.splitter", "name": "splitter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "suppress_competing_matchers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.IPCompleter.suppress_competing_matchers", "name": "suppress_competing_matchers", "type": "traitlets.traitlets.Union"}}, "text_until_cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.IPCompleter.text_until_cursor", "name": "text_until_cursor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "unicode_name_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.IPCompleter.unicode_name_matcher", "name": "unicode_name_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.IPCompleter", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_name_matcher of IPCompleter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.unicode_name_matcher", "name": "unicode_name_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "unicode_name_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.completer.IPCompleter.unicode_name_matches", "name": "unicode_name_matches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_name_matches of IPCompleter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.unicode_name_matches", "name": "unicode_name_matches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_name_matches of IPCompleter", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unicode_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.completer.IPCompleter.unicode_names", "name": "unicode_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.IPCompleter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_names of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.IPCompleter.unicode_names", "name": "unicode_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.completer.IPCompleter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unicode_names of IPCompleter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.IPCompleter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.IPCompleter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InspectColors": {".class": "SymbolTableNode", "cross_ref": "IPython.core.oinspect.InspectColors", "kind": "Gdef", "module_public": false}, "Int": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Int", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "JEDI_INSTALLED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.JEDI_INSTALLED", "name": "JEDI_INSTALLED", "type": "builtins.bool"}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "ListTrait": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "MATCHES_LIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.MATCHES_LIMIT", "name": "MATCHES_LIMIT", "type": "builtins.int"}}, "Matcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.core.completer.Matcher", "line": 707, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.MatcherAPIv1"}, "IPython.core.completer.MatcherAPIv2"], "uses_pep604_syntax": false}}}, "MatcherAPIv1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.core.completer.MatcherAPIv1", "line": 690, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["IPython.core.completer._MatcherAPIv1Base", "IPython.core.completer._MatcherAPIv1Total"], "uses_pep604_syntax": false}}}, "MatcherAPIv2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2], ["__qualname__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.MatcherAPIv2", "name": "MatcherAPIv2", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "IPython.core.completer.MatcherAPIv2", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.MatcherAPIv2", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "IPython.core.completer.MatcherAPIv2.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["IPython.core.completer.MatcherAPIv2", "IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of MatcherAPIv2", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher<PERSON>ult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "IPython.core.completer.MatcherAPIv2.__qualname__", "name": "__qualname__", "type": "builtins.str"}}, "matcher_api_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.completer.MatcherAPIv2.matcher_api_version", "name": "matcher_api_version", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.MatcherAPIv2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.MatcherAPIv2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatcherResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.core.completer.Matcher<PERSON>ult", "line": 668, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._JediMatcherResult"}], "uses_pep604_syntax": false}}}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing.NotRequired", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PROTECTABLES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.PROTECTABLES", "name": "PROTECTABLES", "type": "builtins.str"}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef", "module_public": false}, "ProvisionalCompleterWarning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.FutureWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.ProvisionalCompleterWarning", "name": "ProvisionalCompleterWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.ProvisionalCompleterWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.ProvisionalCompleterWarning", "builtins.FutureWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.ProvisionalCompleterWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.ProvisionalCompleterWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "SimpleCompletion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.SimpleCompletion", "name": "SimpleCompletion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.SimpleCompletion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.SimpleCompletion", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "text", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.SimpleCompletion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "text", "type"], "arg_types": ["IPython.core.completer.SimpleCompletion", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleCompletion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.SimpleCompletion.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "IPython.core.completer.SimpleCompletion.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.SimpleCompletion.text", "name": "text", "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer.SimpleCompletion.type", "name": "type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer.SimpleCompletion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer.SimpleCompletion", "values": [], "variance": 0}, "slots": ["text", "type"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleMatcherResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer.SimpleMatcherResult", "name": "SimpleMatcherResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer.SimpleMatcherResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer.SimpleMatcherResult", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["matched_fragment", "builtins.str"], ["suppress", {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "uses_pep604_syntax": false}], ["do_not_suppress", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], ["ordered", "builtins.bool"], ["completions", {".class": "UnionType", "items": [{".class": "Instance", "args": ["IPython.core.completer.SimpleCompletion"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["IPython.core.completer.SimpleCompletion"], "extra_attrs": null, "type_ref": "typing.Iterator"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["completions"]}}}, "SimpleNamespace": {".class": "SymbolTableNode", "cross_ref": "types.SimpleNamespace", "kind": "Gdef", "module_public": false}, "Sized": {".class": "SymbolTableNode", "cross_ref": "typing.Sized", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TryNext": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.TryNext", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Unicode", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnionTrait": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Union", "kind": "Gdef", "module_public": false}, "_CompleteResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.core.completer._CompleteResult", "line": 1721, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher<PERSON>ult"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_DictKeyState": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Flag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer._DictKeyState", "name": "_DictKeyState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "IPython.core.completer._DictKeyState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer._DictKeyState", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "BASELINE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer._DictKeyState.BASELINE", "name": "BASELINE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "END_OF_ITEM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer._DictKeyState.END_OF_ITEM", "name": "END_OF_ITEM", "type": "enum.auto"}}, "END_OF_TUPLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer._DictKeyState.END_OF_TUPLE", "name": "END_OF_TUPLE", "type": "enum.auto"}}, "IN_TUPLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer._DictKeyState.IN_TUPLE", "name": "IN_TUPLE", "type": "enum.auto"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer._DictKeyState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer._DictKeyState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FakeJediCompletion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer._FakeJediCompletion", "name": "_FakeJediCompletion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer._FakeJediCompletion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer._FakeJediCompletion", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._FakeJediCompletion.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._FakeJediCompletion.__repr__", "name": "__repr__", "type": null}}, "_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer._FakeJediCompletion._origin", "name": "_origin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer._FakeJediCompletion.complete", "name": "complete", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer._FakeJediCompletion.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name_with_symbols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer._FakeJediCompletion.name_with_symbols", "name": "name_with_symbols", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "signature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer._FakeJediCompletion.signature", "name": "signature", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer._FakeJediCompletion.text", "name": "text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.completer._FakeJediCompletion.type", "name": "type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer._FakeJediCompletion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer._FakeJediCompletion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_IC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.core.completer._IC", "line": 806, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["IPython.core.completer.Completion"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "_INT_FORMATS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer._INT_FORMATS", "name": "_INT_FORMATS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.int", "typing.SupportsIndex"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_JediCompletionLike": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.core.completer._JediCompletionLike", "line": 487, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "IPython.core.completer.jedi", "source_any": null, "type_of_any": 3}, "IPython.core.completer._FakeJediCompletion"], "uses_pep604_syntax": false}}}, "_JediMatcherResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer._JediMatcherResult", "name": "_JediMatcherResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer._JediMatcherResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer._JediMatcherResult", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["matched_fragment", "builtins.str"], ["suppress", {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "uses_pep604_syntax": false}], ["do_not_suppress", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], ["ordered", "builtins.bool"], ["completions", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._JediCompletionLike"}], "extra_attrs": null, "type_ref": "typing.Iterator"}]], "readonly_keys": [], "required_keys": ["completions"]}}}, "_MatcherAPIv1Base": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2], ["__qualname__", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer._MatcherAPIv1Base", "name": "_MatcherAPIv1Base", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "IPython.core.completer._MatcherAPIv1Base", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer._MatcherAPIv1Base", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "IPython.core.completer._MatcherAPIv1Base.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer._MatcherAPIv1Base", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _MatcherAPIv1Base", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__qualname__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "IPython.core.completer._MatcherAPIv1Base.__qualname__", "name": "__qualname__", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer._MatcherAPIv1Base.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer._MatcherAPIv1Base", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MatcherAPIv1Total": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2], ["__qualname__", 1], ["matcher_api_version", 1]], "alt_promote": null, "bases": ["IPython.core.completer._MatcherAPIv1Base"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer._MatcherAPIv1Total", "name": "_MatcherAPIv1Total", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "IPython.core.completer._MatcherAPIv1Total", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer._MatcherAPIv1Total", "IPython.core.completer._MatcherAPIv1Base", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "IPython.core.completer._MatcherAPIv1Total.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["IPython.core.completer._MatcherAPIv1Total", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _MatcherAPIv1Total", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matcher_api_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "IPython.core.completer._MatcherAPIv1Total.matcher_api_version", "name": "matcher_api_version", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.completer._MatcherAPIv1Total.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.completer._MatcherAPIv1Total", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MatcherResultBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.completer._MatcherResultBase", "name": "_MatcherResultBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.completer._MatcherResultBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.completer", "mro": ["IPython.core.completer._MatcherResultBase", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["matched_fragment", "builtins.str"], ["suppress", {".class": "UnionType", "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "uses_pep604_syntax": false}], ["do_not_suppress", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], ["ordered", "builtins.bool"]], "readonly_keys": [], "required_keys": []}}}, "_UNICODE_RANGES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer._UNICODE_RANGES", "name": "_UNICODE_RANGES", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_UNKNOWN_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer._UNKNOWN_TYPE", "name": "_UNKNOWN_TYPE", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completer.__file__", "name": "__file__", "type": "builtins.str"}}, "__main__": {".class": "SymbolTableNode", "cross_ref": "__main__", "kind": "Gdef", "module_public": false}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completer.__package__", "name": "__package__", "type": "builtins.str"}}, "__skip_doctest__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.__skip_doctest__", "name": "__skip_doctest__", "type": "builtins.bool"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.completer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_convert_matcher_v1_result_to_v2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["matches", "type", "fragment", "suppress_if_matches"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._convert_matcher_v1_result_to_v2", "name": "_convert_matcher_v1_result_to_v2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["matches", "type", "fragment", "suppress_if_matches"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_matcher_v1_result_to_v2", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.SimpleMatcherResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_deduplicate_completions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["text", "completions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._deduplicate_completions", "name": "_deduplicate_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["text", "completions"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._IC"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deduplicate_completions", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._IC"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_formatparamchildren": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parameter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._formatparamchildren", "name": "_formatparamchildren", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parameter"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_formatparamchildren", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_matcher_api_version": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["matcher"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._get_matcher_api_version", "name": "_get_matcher_api_version", "type": null}}, "_get_matcher_id": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["matcher"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._get_matcher_id", "name": "_get_matcher_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matcher"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_matcher_id", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_matcher_priority": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["matcher"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._get_matcher_priority", "name": "_get_matcher_priority", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matcher"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_matcher_priority", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_iterator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._is_iterator", "name": "_is_iterator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_iterator", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_matcher_v1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["matcher"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._is_matcher_v1", "name": "_is_matcher_v1", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matcher"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_matcher_v1", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.MatcherAPIv1"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_matcher_v2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["matcher"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._is_matcher_v2", "name": "_is_matcher_v2", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["matcher"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_matcher_v2", "ret_type": "builtins.bool", "type_guard": "IPython.core.completer.MatcherAPIv2", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_sizable": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._is_sizable", "name": "_is_sizable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_sizable", "ret_type": "builtins.bool", "type_guard": "typing.Sized", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_signature": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["completion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._make_signature", "name": "_make_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["completion"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_signature", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_number_in_dict_key_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._match_number_in_dict_key_prefix", "name": "_match_number_in_dict_key_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["prefix"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_number_in_dict_key_prefix", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_tokens": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["c"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._parse_tokens", "name": "_parse_tokens", "type": null}}, "_safe_isinstance": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2], "arg_names": ["obj", "module", "class_name", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._safe_isinstance", "name": "_safe_isinstance", "type": null}}, "_unicode_name_compute": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ranges"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer._unicode_name_compute", "name": "_unicode_name_compute", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ranges"], "arg_types": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unicode_name_compute", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "arg_split": {".class": "SymbolTableNode", "cross_ref": "IPython.utils._process_win32.arg_split", "kind": "Gdef", "module_public": false}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef", "module_public": false}, "back_latex_name_matcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.back_latex_name_matcher", "name": "back_latex_name_matcher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["context"], "arg_types": ["IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "back_latex_name_matcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.completer.back_latex_name_matcher", "name": "back_latex_name_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "back_latex_name_matches": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.back_latex_name_matches", "name": "back_latex_name_matches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "back_latex_name_matches", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "back_unicode_name_matcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.back_unicode_name_matcher", "name": "back_unicode_name_matcher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["context"], "arg_types": ["IPython.core.completer.CompletionContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "back_unicode_name_matcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.completer.back_unicode_name_matcher", "name": "back_unicode_name_matcher", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "back_unicode_name_matches": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.back_unicode_name_matches", "name": "back_unicode_name_matches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "back_unicode_name_matches", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "builtin_mod": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "completion_matcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["priority", "identifier", "api_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.completion_matcher", "name": "completion_matcher", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["priority", "identifier", "api_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "completion_matcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "completions_sorting_key": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["word"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.completions_sorting_key", "name": "completions_sorting_key", "type": null}}, "compress_user": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["path", "tilde_expand", "tilde_val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.compress_user", "name": "compress_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["path", "tilde_expand", "tilde_val"], "arg_types": ["builtins.str", "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compress_user", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "context_matcher": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.context_matcher", "name": "context_matcher", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": {".class": "ExtraAttrs", "attrs": {"__mypy_partial": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["priority", "identifier", "api_version"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "completion_matcher", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "immutable": [], "mod_name": null}, "type_ref": "functools.partial"}}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef", "module_public": false}, "cursor_to_position": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["text", "line", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.cursor_to_position", "name": "cursor_to_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["text", "line", "column"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cursor_to_position", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef", "module_public": false}, "dir2": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.dir2.dir2", "kind": "Gdef", "module_public": false}, "ensure_dir_exists": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.ensure_dir_exists", "kind": "Gdef", "module_public": false}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_public": false}, "expand_user": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.expand_user", "name": "expand_user", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_user", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bool", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generics": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.generics", "kind": "Gdef", "module_public": false}, "get__all__entries": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.get__all__entries", "name": "get__all__entries", "type": null}}, "get_real_method": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.dir2.get_real_method", "kind": "Gdef", "module_public": false}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef", "module_public": false}, "guarded_eval": {".class": "SymbolTableNode", "cross_ref": "IPython.core.guarded_eval.guarded_eval", "kind": "Gdef", "module_public": false}, "has_any_completions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.has_any_completions", "name": "has_any_completions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["result"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer.Matcher<PERSON>ult"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_any_completions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_open_quotes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.has_open_quotes", "name": "has_open_quotes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_open_quotes", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef", "module_public": false}, "jedi": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.core.completer.jedi", "name": "jedi", "type": {".class": "AnyType", "missing_import_name": "IPython.core.completer.jedi", "source_any": null, "type_of_any": 3}}}, "keyword": {".class": "SymbolTableNode", "cross_ref": "keyword", "kind": "Gdef", "module_public": false}, "latex_symbols": {".class": "SymbolTableNode", "cross_ref": "IPython.core.latex_symbols.latex_symbols", "kind": "Gdef", "module_public": false}, "literal_eval": {".class": "SymbolTableNode", "cross_ref": "ast.literal_eval", "kind": "Gdef", "module_public": false}, "match_dict_keys": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["keys", "prefix", "delims", "extra_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.match_dict_keys", "name": "match_dict_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["keys", "prefix", "delims", "extra_prefix"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_dict_keys", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.str", "IPython.core.completer._DictKeyState"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "not_found": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.completer.not_found", "name": "not_found", "type": "builtins.object"}}, "observe": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.observe", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "position_to_cursor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["text", "offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.position_to_cursor", "name": "position_to_cursor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["text", "offset"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "position_to_cursor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "protect_filename": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["s", "protectables"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.protect_filename", "name": "protect_filename", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["s", "protectables"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "protect_filename", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "provisionalcompleter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["action"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.completer.provisionalcompleter", "name": "provisionalcompleter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.completer.provisionalcompleter", "name": "provisionalcompleter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "rectify_completions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["text", "completions", "_debug"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.completer.rectify_completions", "name": "rectify_completions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["text", "completions", "_debug"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._IC"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rectify_completions", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.core.completer._IC"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reverse_latex_symbol": {".class": "SymbolTableNode", "cross_ref": "IPython.core.latex_symbols.reverse_latex_symbol", "kind": "Gdef", "module_public": false}, "skip_doctest": {".class": "SymbolTableNode", "cross_ref": "IPython.testing.skipdoctest.skip_doctest", "kind": "Gdef", "module_public": false}, "sphinx_options": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.decorators.sphinx_options", "kind": "Gdef", "module_public": false}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "tokenize": {".class": "SymbolTableNode", "cross_ref": "tokenize", "kind": "Gdef", "module_public": false}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef", "module_public": false}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\completer.py"}