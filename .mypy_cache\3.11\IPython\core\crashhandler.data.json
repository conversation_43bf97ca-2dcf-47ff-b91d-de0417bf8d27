{".class": "MypyFile", "_fullname": "IPython.core.crashhandler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Application": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.application.Application", "kind": "Gdef"}, "CrashHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.crashhandler.CrashHandler", "name": "CrashHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.crashhandler.CrashHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.crashhandler", "mro": ["IPython.core.crashhandler.CrashHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "etype", "evalue", "etb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.crashhandler.CrashHandler.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "etype", "evalue", "etb"], "arg_types": ["IPython.core.crashhandler.CrashHandler", {".class": "TypeType", "item": "builtins.BaseException"}, "builtins.BaseException", "types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of CrashHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "contact_name", "contact_email", "bug_tracker", "show_crash_traceback", "call_pdb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.crashhandler.CrashHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "contact_name", "contact_email", "bug_tracker", "show_crash_traceback", "call_pdb"], "arg_types": ["IPython.core.crashhandler.CrashHandler", "traitlets.config.application.Application", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CrashHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.crashhandler.CrashHandler.app", "name": "app", "type": "traitlets.config.application.Application"}}, "call_pdb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.crashhandler.CrashHandler.call_pdb", "name": "call_pdb", "type": "builtins.bool"}}, "crash_report_fname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.crashhandler.CrashHandler.crash_report_fname", "name": "crash_report_fname", "type": "builtins.str"}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.core.crashhandler.CrashHandler.info", "name": "info", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "make_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.crashhandler.CrashHandler.make_report", "name": "make_report", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "traceback"], "arg_types": ["IPython.core.crashhandler.CrashHandler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_report of CrashHandler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.crashhandler.CrashHandler.message_template", "name": "message_template", "type": "builtins.str"}}, "section_sep": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.crashhandler.CrashHandler.section_sep", "name": "section_sep", "type": "builtins.str"}}, "show_crash_traceback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.crashhandler.CrashHandler.show_crash_traceback", "name": "show_crash_traceback", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.crashhandler.CrashHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.crashhandler.CrashHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.crashhandler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.crashhandler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.crashhandler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.crashhandler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.crashhandler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.crashhandler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_default_message_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.crashhandler._default_message_template", "name": "_default_message_template", "type": "builtins.str"}}, "_lite_message_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.crashhandler._lite_message_template", "name": "_lite_message_template", "type": "builtins.str"}}, "author_email": {".class": "SymbolTableNode", "cross_ref": "IPython.core.release.author_email", "kind": "Gdef"}, "builtin_mod": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef"}, "crash_handler_lite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["etype", "evalue", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.crashhandler.crash_handler_lite", "name": "crash_handler_lite", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["etype", "evalue", "tb"], "arg_types": [{".class": "TypeType", "item": "builtins.BaseException"}, "builtins.BaseException", "types.TracebackType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "crash_handler_lite", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pformat": {".class": "SymbolTableNode", "cross_ref": "pprint.pformat", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "sys_info": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.sysinfo.sys_info", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "ultratb": {".class": "SymbolTableNode", "cross_ref": "IPython.core.ultratb", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "IPython.core.release.__version__", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\crashhandler.py"}