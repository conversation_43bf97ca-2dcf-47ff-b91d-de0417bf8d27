{"data_mtime": 1753781401, "dep_lines": [29, 30, 31, 32, 29, 22, 23, 24, 25, 27, 36, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 10, 10, 5, 5, 10, 5, 10, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.ultratb", "IPython.core.application", "IPython.core.release", "IPython.utils.sysinfo", "IPython.core", "sys", "traceback", "pprint", "pathlib", "builtins", "typing", "types", "warnings", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "traitlets.utils.warnings", "functools", "re", "IPython.utils", "IPython.utils.colorable", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "traitlets", "traitlets.config", "traitlets.config.application", "traitlets.config.configurable", "traitlets.traitlets", "typing_extensions"], "hash": "0f547e337ffa335b101a57029dad4e9484c5cff0", "id": "IPython.core.crashhandler", "ignore_all": true, "interface_hash": "fca70f17b62c54149fcd671a1d13538facadda6d", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\crashhandler.py", "plugin_data": null, "size": 8921, "suppressed": [], "version_id": "1.15.0"}