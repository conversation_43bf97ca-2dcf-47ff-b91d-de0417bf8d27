{"data_mtime": 1753781401, "dep_lines": [135, 136, 136, 136, 142, 268, 136, 124, 126, 127, 128, 129, 130, 131, 132, 134, 138, 150, 568, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 25, 20, 20, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.excolors", "IPython.utils.PyColorize", "IPython.utils.coloransi", "IPython.utils.py3compat", "IPython.core.interactiveshell", "IPython.terminal.interactiveshell", "IPython.utils", "__future__", "inspect", "linecache", "os", "re", "sys", "contextlib", "functools", "IPython", "typing", "pdb", "reprlib", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "copy", "itertools", "html", "traitlets.utils.warnings", "types", "traceback", "IPython.core.getipython", "IPython.terminal", "IPython.utils.colorable", "_frozen_importlib", "_typeshed", "abc", "bdb", "cmd", "enum", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing_extensions"], "hash": "7273a62f6c392270178bbd6a2fc38aa828d54c30", "id": "IPython.core.debugger", "ignore_all": true, "interface_hash": "269a48deb2b850fbf5476cbdf216d99d24325e72", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\debugger.py", "plugin_data": null, "size": 39002, "suppressed": [], "version_id": "1.15.0"}