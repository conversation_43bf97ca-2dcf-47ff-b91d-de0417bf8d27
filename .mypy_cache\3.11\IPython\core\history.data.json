{".class": "MypyFile", "_fullname": "IPython.core.history", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Any", "kind": "Gdef"}, "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Dict", "kind": "Gdef"}, "DummyDB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.history.DummyDB", "name": "DummyDB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.history.DummyDB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.history", "mro": ["IPython.core.history.DummyDB", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.DummyDB.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.DummyDB.__exit__", "name": "__exit__", "type": null}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.DummyDB.commit", "name": "commit", "type": null}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.DummyDB.execute", "name": "execute", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.history.DummyDB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.history.DummyDB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HistoryAccessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.history.HistoryAccessorBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.history.HistoryAccessor", "name": "HistoryAccessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessor", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.history", "mro": ["IPython.core.history.HistoryAccessor", "IPython.core.history.HistoryAccessorBase", "traitlets.config.configurable.LoggingConfigurable", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "profile", "hist_file", "traits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessor.__init__", "name": "__init__", "type": null}}, "_corrupt_db_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryAccessor._corrupt_db_counter", "name": "_corrupt_db_counter", "type": "builtins.int"}}, "_corrupt_db_limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryAccessor._corrupt_db_limit", "name": "_corrupt_db_limit", "type": "builtins.int"}}, "_db_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor._db_changed", "name": "_db_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor._db_changed", "name": "_db_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_default_connection_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor._default_connection_options", "name": "_default_connection_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor._default_connection_options", "name": "_default_connection_options", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_get_hist_file_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessor._get_hist_file_name", "name": "_get_hist_file_name", "type": null}}, "_run_sql": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "sql", "params", "raw", "output", "latest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessor._run_sql", "name": "_run_sql", "type": null}}, "connection_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryAccessor.connection_options", "name": "connection_options", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "db": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryAccessor.db", "name": "db", "type": "traitlets.traitlets.Any"}}, "enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryAccessor.enabled", "name": "enabled", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "get_last_session_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor.get_last_session_id", "name": "get_last_session_id", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor.get_last_session_id", "name": "get_last_session_id", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "session", "start", "stop", "raw", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor.get_range", "name": "get_range", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor.get_range", "name": "get_range", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get_range_by_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "rangestr", "raw", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessor.get_range_by_str", "name": "get_range_by_str", "type": null}}, "get_session_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor.get_session_info", "name": "get_session_info", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor.get_session_info", "name": "get_session_info", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get_tail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "n", "raw", "output", "include_latest"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor.get_tail", "name": "get_tail", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor.get_tail", "name": "get_tail", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "hist_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryAccessor.hist_file", "name": "hist_file", "type": "traitlets.traitlets.Union"}}, "init_db": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor.init_db", "name": "init_db", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor.init_db", "name": "init_db", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pattern", "raw", "search_raw", "output", "n", "unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryAccessor.search", "name": "search", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryAccessor.search", "name": "search", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "writeout_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessor.writeout_cache", "name": "writeout_cache", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.history.HistoryAccessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.history.HistoryAccessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HistoryAccessorBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["traitlets.config.configurable.LoggingConfigurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.history.HistoryAccessorBase", "name": "HistoryAccessorBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessorBase", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.history", "mro": ["IPython.core.history.HistoryAccessorBase", "traitlets.config.configurable.LoggingConfigurable", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "get_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "session", "start", "stop", "raw", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessorBase.get_range", "name": "get_range", "type": null}}, "get_range_by_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "rangestr", "raw", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessorBase.get_range_by_str", "name": "get_range_by_str", "type": null}}, "get_tail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "n", "raw", "output", "include_latest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessorBase.get_tail", "name": "get_tail", "type": null}}, "search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pattern", "raw", "search_raw", "output", "n", "unique"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryAccessorBase.search", "name": "search", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.history.HistoryAccessorBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.history.HistoryAccessorBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HistoryManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.history.HistoryAccessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.history.HistoryManager", "name": "HistoryManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.history", "mro": ["IPython.core.history.HistoryManager", "IPython.core.history.HistoryAccessor", "IPython.core.history.HistoryAccessorBase", "traitlets.config.configurable.LoggingConfigurable", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "shell", "config", "traits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.__init__", "name": "__init__", "type": null}}, "_dir_hist_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryManager._dir_hist_default", "name": "_dir_hist_default", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryManager._dir_hist_default", "name": "_dir_hist_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_exit_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager._exit_re", "name": "_exit_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_get_hist_file_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager._get_hist_file_name", "name": "_get_hist_file_name", "type": null}}, "_get_range_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "start", "stop", "raw", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager._get_range_session", "name": "_get_range_session", "type": null}}, "_i": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager._i", "name": "_i", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "_i00": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager._i00", "name": "_i00", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "_ii": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager._ii", "name": "_ii", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "_iii": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager._iii", "name": "_iii", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "_writeout_input_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager._writeout_input_cache", "name": "_writeout_input_cache", "type": null}}, "_writeout_output_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager._writeout_output_cache", "name": "_writeout_output_cache", "type": null}}, "db_cache_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.db_cache_size", "name": "db_cache_size", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "db_input_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.db_input_cache", "name": "db_input_cache", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "db_input_cache_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.history.HistoryManager.db_input_cache_lock", "name": "db_input_cache_lock", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "db_log_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.db_log_output", "name": "db_log_output", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "db_output_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.db_output_cache", "name": "db_output_cache", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "db_output_cache_lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.history.HistoryManager.db_output_cache_lock", "name": "db_output_cache_lock", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dir_hist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.dir_hist", "name": "dir_hist", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "end_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.end_session", "name": "end_session", "type": null}}, "get_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "session", "start", "stop", "raw", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.get_range", "name": "get_range", "type": null}}, "get_session_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.get_session_info", "name": "get_session_info", "type": null}}, "get_tail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "n", "raw", "output", "include_latest"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryManager.get_tail", "name": "get_tail", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryManager.get_tail", "name": "get_tail", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "input_hist_parsed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.input_hist_parsed", "name": "input_hist_parsed", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "input_hist_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.input_hist_raw", "name": "input_hist_raw", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "name_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.name_session", "name": "name_session", "type": null}}, "new_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryManager.new_session", "name": "new_session", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryManager.new_session", "name": "new_session", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "output_hist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.output_hist", "name": "output_hist", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "output_hist_reprs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.output_hist_reprs", "name": "output_hist_reprs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "new_session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.reset", "name": "reset", "type": null}}, "save_flag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.save_flag", "name": "save_flag", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["threading.Event", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "save_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.save_thread", "name": "save_thread", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "session_number": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.session_number", "name": "session_number", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "shell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistoryManager.shell", "name": "shell", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "store_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "line_num", "source", "source_raw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.store_inputs", "name": "store_inputs", "type": null}}, "store_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line_num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistoryManager.store_output", "name": "store_output", "type": null}}, "writeout_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistoryManager.writeout_cache", "name": "writeout_cache", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistoryManager.writeout_cache", "name": "writeout_cache", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.history.HistoryManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.history.HistoryManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HistorySavingThread": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["threading.Thread"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.history.HistorySavingThread", "name": "HistorySavingThread", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistorySavingThread", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.history", "mro": ["IPython.core.history.HistorySavingThread", "threading.Thread", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "history_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistorySavingThread.__init__", "name": "__init__", "type": null}}, "daemon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistorySavingThread.daemon", "name": "daemon", "type": "builtins.bool"}}, "db": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.history.HistorySavingThread.db", "name": "db", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistorySavingThread.enabled", "name": "enabled", "type": "builtins.bool"}}, "history_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.history.HistorySavingThread.history_manager", "name": "history_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.HistorySavingThread.run", "name": "run", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.history.HistorySavingThread.run", "name": "run", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.HistorySavingThread.stop", "name": "stop", "type": null}}, "stop_now": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.HistorySavingThread.stop_now", "name": "stop_now", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.history.HistorySavingThread.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.history.HistorySavingThread", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Instance": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Instance", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Integer", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.List", "kind": "Gdef"}, "LoggingConfigurable": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.configurable.LoggingConfigurable", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "TraitError": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.TraitError", "kind": "Gdef"}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Unicode", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Union", "kind": "Gdef"}, "_SAVE_DB_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.history._SAVE_DB_SIZE", "name": "_SAVE_DB_SIZE", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.history.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.history.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.history.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.history.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.history.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.history.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_format_lineno": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["session", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history._format_lineno", "name": "_format_lineno", "type": null}}, "atexit": {".class": "SymbolTableNode", "cross_ref": "atexit", "kind": "Gdef"}, "catch_corrupt_db": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["f", "self", "a", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.catch_corrupt_db", "name": "catch_corrupt_db", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.history.catch_corrupt_db", "name": "catch_corrupt_db", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "decorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.core.history.decorator", "name": "decorator", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}}}, "default": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.default", "kind": "Gdef"}, "extract_hist_ranges": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ranges_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.history.extract_hist_ranges", "name": "extract_hist_ranges", "type": null}}, "locate_profile": {".class": "SymbolTableNode", "cross_ref": "IPython.paths.locate_profile", "kind": "Gdef"}, "observe": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.observe", "kind": "Gdef"}, "only_when_enabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["f", "self", "a", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.history.only_when_enabled", "name": "only_when_enabled", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.history.only_when_enabled", "name": "only_when_enabled", "type": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": {".class": "AnyType", "missing_import_name": "IPython.core.history.decorator", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "range_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.history.range_re", "name": "range_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sqlite3": {".class": "SymbolTableNode", "cross_ref": "sqlite3", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "undoc": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.decorators.undoc", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\history.py"}