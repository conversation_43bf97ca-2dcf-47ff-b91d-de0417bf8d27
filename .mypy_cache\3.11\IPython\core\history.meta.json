{"data_mtime": 1753781401, "dep_lines": [28, 31, 30, 7, 8, 9, 10, 11, 12, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["traitlets.config.configurable", "IPython.utils.decorators", "IPython.paths", "atexit", "datetime", "re", "sqlite3", "threading", "pathlib", "traitlets", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "types", "traceback", "typing", "IPython.utils", "_frozen_importlib", "_sqlite3", "_thread", "_typeshed", "abc", "enum", "traitlets.config", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing_extensions"], "hash": "2d30f2a146763ff828cf4d0e57fb52d8b72a2faa", "id": "IPython.core.history", "ignore_all": true, "interface_hash": "a7ade8e81aafb6413aa213e04687eb66b9877513", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\history.py", "plugin_data": null, "size": 34821, "suppressed": ["decorator"], "version_id": "1.15.0"}