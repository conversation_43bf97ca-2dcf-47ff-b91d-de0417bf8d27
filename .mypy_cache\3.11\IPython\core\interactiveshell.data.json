{".class": "MypyFile", "_fullname": "IPython.core.interactiveshell", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alias": {".class": "SymbolTableNode", "cross_ref": "IPython.core.alias.<PERSON>", "kind": "Gdef"}, "AliasManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.alias.AliasManager", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Any", "kind": "Gdef"}, "AnyType": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef"}, "BuiltinTrap": {".class": "SymbolTableNode", "cross_ref": "IPython.core.builtin_trap.BuiltinTrap", "kind": "Gdef"}, "CachingCompiler": {".class": "SymbolTableNode", "cross_ref": "IPython.core.compilerop.CachingCompiler", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CaselessStrEnum": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.CaselessStrEnum", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Dict", "kind": "Gdef"}, "DictType": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DisplayFormatter": {".class": "SymbolTableNode", "cross_ref": "IPython.core.formatters.DisplayFormatter", "kind": "Gdef"}, "DisplayHook": {".class": "SymbolTableNode", "cross_ref": "IPython.core.displayhook.DisplayHook", "kind": "Gdef"}, "DisplayPublisher": {".class": "SymbolTableNode", "cross_ref": "IPython.core.displaypub.DisplayPublisher", "kind": "Gdef"}, "DisplayTrap": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_trap.DisplayTrap", "kind": "Gdef"}, "DollarFormatter": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.text.DollarFormatter", "kind": "Gdef"}, "DummyMod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.DummyMod", "name": "Dummy<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.DummyMod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.DummyMod", "builtins.object"], "names": {".class": "SymbolTable", "__spec__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.DummyMod.__spec__", "name": "__spec__", "type": {".class": "NoneType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.DummyMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.DummyMod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ESC_MAGIC": {".class": "SymbolTableNode", "cross_ref": "IPython.core.inputtransformer2.ESC_MAGIC", "kind": "Gdef"}, "ESC_MAGIC2": {".class": "SymbolTableNode", "cross_ref": "IPython.core.inputtransformer2.ESC_MAGIC2", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Enum", "kind": "Gdef"}, "EventManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.events.EventManager", "kind": "Gdef"}, "ExecutionInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.ExecutionInfo", "name": "ExecutionInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ExecutionInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.ExecutionInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "raw_cell", "store_history", "silent", "shell_futures", "cell_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ExecutionInfo.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ExecutionInfo.__repr__", "name": "__repr__", "type": null}}, "cell_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionInfo.cell_id", "name": "cell_id", "type": {".class": "NoneType"}}}, "raw_cell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionInfo.raw_cell", "name": "raw_cell", "type": {".class": "NoneType"}}}, "shell_futures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionInfo.shell_futures", "name": "shell_futures", "type": "builtins.bool"}}, "silent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionInfo.silent", "name": "silent", "type": "builtins.bool"}}, "store_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionInfo.store_history", "name": "store_history", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.ExecutionInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.ExecutionInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExecutionResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.ExecutionResult", "name": "ExecutionResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ExecutionResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.ExecutionResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ExecutionResult.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ExecutionResult.__repr__", "name": "__repr__", "type": null}}, "error_before_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionResult.error_before_exec", "name": "error_before_exec", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "error_in_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionResult.error_in_exec", "name": "error_in_exec", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "execution_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionResult.execution_count", "name": "execution_count", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionResult.info", "name": "info", "type": {".class": "NoneType"}}}, "raise_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ExecutionResult.raise_error", "name": "raise_error", "type": null}}, "result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.ExecutionResult.result", "name": "result", "type": {".class": "NoneType"}}}, "success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.interactiveshell.ExecutionResult.success", "name": "success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.ExecutionResult.success", "name": "success", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.interactiveshell.ExecutionResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "success of ExecutionResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.ExecutionResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.ExecutionResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExitAutocall": {".class": "SymbolTableNode", "cross_ref": "IPython.core.autocall.ExitAutocall", "kind": "Gdef"}, "ExtensionManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.extensions.ExtensionManager", "kind": "Gdef"}, "HistoryManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.history.HistoryManager", "kind": "Gdef"}, "IPython": {".class": "SymbolTableNode", "cross_ref": "IPython", "kind": "Gdef"}, "InputRejected": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.InputRejected", "kind": "Gdef"}, "Instance": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Instance", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Integer", "kind": "Gdef"}, "InteractiveShell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["traitlets.config.configurable.SingletonConfigurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.InteractiveShell", "name": "InteractiveShell", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.InteractiveShell", "traitlets.config.configurable.SingletonConfigurable", "traitlets.config.configurable.LoggingConfigurable", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "Completer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.Completer", "name": "Completer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "CustomTB": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.CustomTB", "name": "CustomTB", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "InteractiveTB": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.InteractiveTB", "name": "InteractiveTB", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "SyntaxTB": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.SyntaxTB", "name": "SyntaxTB", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "ipython_dir", "profile_dir", "user_module", "user_ns", "custom_exceptions", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.__init__", "name": "__init__", "type": null}}, "_atexit_once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._atexit_once", "name": "_atexit_once", "type": null}}, "_atexit_once_called": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._atexit_once_called", "name": "_atexit_once_called", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_call_pdb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._call_pdb", "name": "_call_pdb", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_clear_warning_registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._clear_warning_registry", "name": "_clear_warning_registry", "type": null}}, "_default_loop_runner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._default_loop_runner", "name": "_default_loop_runner", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._default_loop_runner", "name": "_default_loop_runner", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_enable_html_pager_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._enable_html_pager_changed", "name": "_enable_html_pager_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._enable_html_pager_changed", "name": "_enable_html_pager_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_exiter_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._exiter_default", "name": "_exiter_default", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._exiter_default", "name": "_exiter_default", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_find_parts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["oname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._find_parts", "name": "_find_parts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["oname"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_parts of InteractiveShell", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._find_parts", "name": "_find_parts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["oname"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_parts of InteractiveShell", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_find_with_lazy_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, "type_", "magic_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._find_with_lazy_load", "name": "_find_with_lazy_load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, "type_", "magic_name"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_with_lazy_load of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_user_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._format_user_obj", "name": "_format_user_obj", "type": null}}, "_get_call_pdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._get_call_pdb", "name": "_get_call_pdb", "type": null}}, "_get_exc_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exc_tuple"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._get_exc_info", "name": "_get_exc_info", "type": null}}, "_getattr_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["obj", "attrname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._getattr_property", "name": "_getattr_property", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._getattr_property", "name": "_getattr_property", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["obj", "attrname"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getattr_property of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_import_runner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proposal"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._import_runner", "name": "_import_runner", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._import_runner", "name": "_import_runner", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_indent_current_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._indent_current_str", "name": "_indent_current_str", "type": null}}, "_inspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "meth", "oname", "namespaces", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._inspect", "name": "_inspect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "meth", "oname", "namespaces", "kw"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell._instance", "name": "_instance", "type": {".class": "NoneType"}}}, "_ipython_dir_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._ipython_dir_changed", "name": "_ipython_dir_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._ipython_dir_changed", "name": "_ipython_dir_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_main_mod_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._main_mod_cache", "name": "_main_mod_cache", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_object_find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "oname", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._object_find", "name": "_object_find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "oname", "namespaces"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_object_find of InteractiveShell", "ret_type": "IPython.core.oinspect.OInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ofind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "oname", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._ofind", "name": "_ofind", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "oname", "namespaces"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ofind of InteractiveShell", "ret_type": "IPython.core.oinspect.OInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_orig_sys_module_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._orig_sys_module_state", "name": "_orig_sys_module_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_orig_sys_modules_main_mod": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._orig_sys_modules_main_mod", "name": "_orig_sys_modules_main_mod", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_orig_sys_modules_main_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._orig_sys_modules_main_name", "name": "_orig_sys_modules_main_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_post_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell._post_execute", "name": "_post_execute", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Dict"}}}, "_run_cell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "raw_cell", "store_history", "silent", "shell_futures", "cell_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._run_cell", "name": "_run_cell", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "raw_cell", "store_history", "silent", "shell_futures", "cell_id"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_cell of InteractiveShell", "ret_type": "IPython.core.interactiveshell.ExecutionResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_call_pdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._set_call_pdb", "name": "_set_call_pdb", "type": null}}, "_showtraceback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "etype", "evalue", "stb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._showtraceback", "name": "_showtraceback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "etype", "evalue", "stb"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_showtraceback of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sphinxify_docstring_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell._sphinxify_docstring_changed", "name": "_sphinxify_docstring_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell._sphinxify_docstring_changed", "name": "_sphinxify_docstring_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "_user_obj_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell._user_obj_error", "name": "_user_obj_error", "type": null}}, "active_eventloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.active_eventloop", "name": "active_eventloop", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "alias_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.alias_manager", "name": "alias_manager", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "all_ns_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.all_ns_refs", "name": "all_ns_refs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.all_ns_refs", "name": "all_ns_refs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_ns_refs of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ask_yes_no": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "prompt", "default", "interrupt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.ask_yes_no", "name": "ask_yes_no", "type": null}}, "ast_node_interactivity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.ast_node_interactivity", "name": "ast_node_interactivity", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Enum"}}}, "ast_transformers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.ast_transformers", "name": "ast_transformers", "type": {".class": "Instance", "args": ["ast.NodeTransformer"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "atexit_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.atexit_operations", "name": "atexit_operations", "type": null}}, "auto_rewrite_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.auto_rewrite_input", "name": "auto_rewrite_input", "type": null}}, "autoawait": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.autoawait", "name": "autoawait", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "autocall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.autocall", "name": "autocall", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Enum"}}}, "autoindent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.autoindent", "name": "autoindent", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "automagic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.automagic", "name": "automagic", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "banner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.banner", "name": "banner", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.banner", "name": "banner", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "banner of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "banner1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.banner1", "name": "banner1", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "banner2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.banner2", "name": "banner2", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "builtin_trap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.builtin_trap", "name": "builtin_trap", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "cache_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.cache_size", "name": "cache_size", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "call_pdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.call_pdb", "name": "call_pdb", "type": "builtins.property"}}, "check_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.check_complete", "name": "check_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_complete of InteractiveShell", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.cleanup", "name": "cleanup", "type": null}}, "clear_main_mod_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.clear_main_mod_cache", "name": "clear_main_mod_cache", "type": null}}, "color_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.color_info", "name": "color_info", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "colors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.colors", "name": "colors", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CaselessStrEnum"}}}, "compile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.compile", "name": "compile", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "compiler_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.compiler_class", "name": "compiler_class", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["IPython.core.compilerop.CachingCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CachingCompiler", "ret_type": "IPython.core.compilerop.CachingCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["IPython.core.compilerop.CachingCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CachingCompiler", "ret_type": "IPython.core.compilerop.CachingCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Type"}}}, "complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "text", "line", "cursor_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.complete", "name": "complete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.complete", "name": "complete", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "configurables": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.configurables", "name": "configurables", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "custom_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.custom_exceptions", "name": "custom_exceptions", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "data_pub": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.data_pub", "name": "data_pub", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "data_pub_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.data_pub_class", "name": "data_pub_class", "type": {".class": "NoneType"}}}, "db": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.db", "name": "db", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.debug", "name": "debug", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "debugger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.debugger", "name": "debugger", "type": null}}, "debugger_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.debugger_cls", "name": "debugger_cls", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 4], "arg_names": ["completekey", "stdin", "stdout", "context", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.core.debugger.InterruptiblePdb"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "IPython.core.debugger.InterruptiblePdb", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_user_namespaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.default_user_namespaces", "name": "default_user_namespaces", "type": "builtins.bool"}}, "define_macro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "themacro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.define_macro", "name": "define_macro", "type": null}}, "del_var": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "varname", "by_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.del_var", "name": "del_var", "type": null}}, "dir_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.dir_stack", "name": "dir_stack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "disable_failing_post_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.disable_failing_post_execute", "name": "disable_failing_post_execute", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "display_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.display_formatter", "name": "display_formatter", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["IPython.core.formatters.DisplayFormatter", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "display_page": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.display_page", "name": "display_page", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "display_pub": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.display_pub", "name": "display_pub", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "display_pub_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.display_pub_class", "name": "display_pub_class", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [1, 2, 4], "arg_names": ["shell", "args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.core.displaypub.DisplayPublisher"], "def_extras": {"first_arg": "self"}, "fallback": "traitlets.traitlets.MetaHasTraits", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DisplayPublisher", "ret_type": "IPython.core.displaypub.DisplayPublisher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 2, 4], "arg_names": ["shell", "args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.core.displaypub.DisplayPublisher"], "def_extras": {"first_arg": "self"}, "fallback": "traitlets.traitlets.MetaHasTraits", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DisplayPublisher", "ret_type": "IPython.core.displaypub.DisplayPublisher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Type"}}}, "display_trap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.display_trap", "name": "display_trap", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "displayhook": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.displayhook", "name": "displayhook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "displayhook_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.displayhook_class", "name": "displayhook_class", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["shell", "cache_size", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.core.displayhook.DisplayHook"], "def_extras": {"first_arg": "self"}, "fallback": "traitlets.traitlets.MetaHasTraits", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DisplayHook", "ret_type": "IPython.core.displayhook.DisplayHook", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 1, 4], "arg_names": ["shell", "cache_size", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.core.displayhook.DisplayHook"], "def_extras": {"first_arg": "self"}, "fallback": "traitlets.traitlets.MetaHasTraits", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DisplayHook", "ret_type": "IPython.core.displayhook.DisplayHook", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Type"}}}, "drop_by_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "variables"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.drop_by_id", "name": "drop_by_id", "type": null}}, "enable_gui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "gui"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.enable_gui", "name": "enable_gui", "type": null}}, "enable_html_pager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.enable_html_pager", "name": "enable_html_pager", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "enable_matplotlib": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "gui"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.enable_matplotlib", "name": "enable_matplotlib", "type": null}}, "enable_pylab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "gui", "import_all", "welcome_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.enable_pylab", "name": "enable_pylab", "type": null}}, "ev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.ev", "name": "ev", "type": null}}, "events": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.events", "name": "events", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.ex", "name": "ex", "type": null}}, "excepthook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "etype", "value", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.excepthook", "name": "excepthook", "type": null}}, "execution_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.execution_count", "name": "execution_count", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "exit_now": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.exit_now", "name": "exit_now", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "exiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.exiter", "name": "exiter", "type": {".class": "Instance", "args": ["IPython.core.autocall.ExitAutocall"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "extension_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.extension_manager", "name": "extension_manager", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "extract_input_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "range_str", "raw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.extract_input_lines", "name": "extract_input_lines", "type": null}}, "filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.filename", "name": "filename", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "find_cell_magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "magic_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.find_cell_magic", "name": "find_cell_magic", "type": null}}, "find_line_magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "magic_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.find_line_magic", "name": "find_line_magic", "type": null}}, "find_magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "magic_name", "magic_kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.find_magic", "name": "find_magic", "type": null}}, "find_user_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "target", "raw", "py_only", "skip_encoding_cookie", "search_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.find_user_code", "name": "find_user_code", "type": null}}, "get_exception_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exc_tuple"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.get_exception_only", "name": "get_exception_only", "type": null}}, "get_ipython": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.get_ipython", "name": "get_ipython", "type": null}}, "get_local_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stack_depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.get_local_scope", "name": "get_local_scope", "type": null}}, "get_path_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["p"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.get_path_links", "name": "get_path_links", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["p"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_path_links of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.get_path_links", "name": "get_path_links", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["p"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_path_links of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "getoutput": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "cmd", "split", "depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.getoutput", "name": "getoutput", "type": null}}, "history_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.history_length", "name": "history_length", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "history_load_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.history_load_length", "name": "history_load_length", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Int"}}}, "history_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.history_manager", "name": "history_manager", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "home_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.home_dir", "name": "home_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hooks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.hooks", "name": "hooks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "indent_current_nsp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.indent_current_nsp", "name": "indent_current_nsp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_alias", "name": "init_alias", "type": null}}, "init_builtins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_builtins", "name": "init_builtins", "type": null}}, "init_completer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_completer", "name": "init_completer", "type": null}}, "init_create_namespaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "user_module", "user_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_create_namespaces", "name": "init_create_namespaces", "type": null}}, "init_data_pub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_data_pub", "name": "init_data_pub", "type": null}}, "init_display_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_display_formatter", "name": "init_display_formatter", "type": null}}, "init_display_pub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_display_pub", "name": "init_display_pub", "type": null}}, "init_displayhook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_displayhook", "name": "init_displayhook", "type": null}}, "init_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_encoding", "name": "init_encoding", "type": null}}, "init_environment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_environment", "name": "init_environment", "type": null}}, "init_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_events", "name": "init_events", "type": null}}, "init_extension_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_extension_manager", "name": "init_extension_manager", "type": null}}, "init_history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_history", "name": "init_history", "type": null}}, "init_hooks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_hooks", "name": "init_hooks", "type": null}}, "init_inspector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "changes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_inspector", "name": "init_inspector", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_inspector", "name": "init_inspector", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "init_instance_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_instance_attrs", "name": "init_instance_attrs", "type": null}}, "init_io": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_io", "name": "init_io", "type": null}}, "init_ipython_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ipython_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_ipython_dir", "name": "init_ipython_dir", "type": null}}, "init_logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_logger", "name": "init_logger", "type": null}}, "init_logstart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_logstart", "name": "init_logstart", "type": null}}, "init_magics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_magics", "name": "init_magics", "type": null}}, "init_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_payload", "name": "init_payload", "type": null}}, "init_pdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_pdb", "name": "init_pdb", "type": null}}, "init_prefilter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_prefilter", "name": "init_prefilter", "type": null}}, "init_profile_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "profile_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_profile_dir", "name": "init_profile_dir", "type": null}}, "init_prompts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_prompts", "name": "init_prompts", "type": null}}, "init_pushd_popd_magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_pushd_popd_magic", "name": "init_pushd_popd_magic", "type": null}}, "init_syntax_highlighting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "changes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_syntax_highlighting", "name": "init_syntax_highlighting", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_syntax_highlighting", "name": "init_syntax_highlighting", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "init_sys_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_sys_modules", "name": "init_sys_modules", "type": null}}, "init_traceback_handlers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "custom_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_traceback_handlers", "name": "init_traceback_handlers", "type": null}}, "init_user_ns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_user_ns", "name": "init_user_ns", "type": null}}, "init_virtualenv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.init_virtualenv", "name": "init_virtualenv", "type": null}}, "input_splitter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.input_splitter", "name": "input_splitter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.input_splitter", "name": "input_splitter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input_splitter of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "input_transformer_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.input_transformer_manager", "name": "input_transformer_manager", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "input_transformers_cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.input_transformers_cleanup", "name": "input_transformers_cleanup", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.input_transformers_cleanup", "name": "input_transformers_cleanup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input_transformers_cleanup of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "input_transformers_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.input_transformers_post", "name": "input_transformers_post", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "inspector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.inspector", "name": "inspector", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "inspector_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.inspector_class", "name": "inspector_class", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["color_table", "code_color_table", "scheme", "str_detail_level", "parent", "config"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.core.oinspect.Inspector"], "def_extras": {"first_arg": "self"}, "fallback": "traitlets.traitlets.MetaHasTraits", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Inspector", "ret_type": "IPython.core.oinspect.Inspector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1], "arg_names": ["color_table", "code_color_table", "scheme", "str_detail_level", "parent", "config"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["IPython.core.oinspect.Inspector"], "def_extras": {"first_arg": "self"}, "fallback": "traitlets.traitlets.MetaHasTraits", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Inspector", "ret_type": "IPython.core.oinspect.Inspector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Type"}}}, "ipython_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.ipython_dir", "name": "ipython_dir", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "last_execution_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.last_execution_result", "name": "last_execution_result", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "last_execution_succeeded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.last_execution_succeeded", "name": "last_execution_succeeded", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "logappend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.logappend", "name": "logappend", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "logfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.logfile", "name": "logfile", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "logstart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.logstart", "name": "logstart", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "loop_runner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.loop_runner", "name": "loop_runner", "type": "traitlets.traitlets.Any"}}, "loop_runner_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.loop_runner_map", "name": "loop_runner_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.object", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.magic", "name": "magic", "type": null}}, "magics_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.magics_manager", "name": "magics_manager", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "meta": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.meta", "name": "meta", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mktempfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "data", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.mktempfile", "name": "mktempfile", "type": null}}, "more": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.more", "name": "more", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "new_main_mod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "filename", "modname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.new_main_mod", "name": "new_main_mod", "type": null}}, "ns_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.ns_table", "name": "ns_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "object_info_string_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.object_info_string_level", "name": "object_info_string_level", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "traitlets.traitlets.Enum"}}}, "object_inspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "oname", "detail_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.object_inspect", "name": "object_inspect", "type": null}}, "object_inspect_mime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "oname", "detail_level", "omit_sections"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.object_inspect_mime", "name": "object_inspect_mime", "type": null}}, "object_inspect_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "oname", "detail_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.object_inspect_text", "name": "object_inspect_text", "type": null}}, "payload_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.payload_manager", "name": "payload_manager", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "pdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.pdb", "name": "pdb", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "prefilter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.prefilter", "name": "prefilter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prefilter_manager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.prefilter_manager", "name": "prefilter_manager", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "prepare_user_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "user_module", "user_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.prepare_user_module", "name": "prepare_user_module", "type": null}}, "profile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.profile", "name": "profile", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.profile", "name": "profile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "profile_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.profile_dir", "name": "profile_dir", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "variables", "interactive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.push", "name": "push", "type": null}}, "pycolorize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.pycolorize", "name": "pycolorize", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pylab_gui_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.pylab_gui_select", "name": "pylab_gui_select", "type": {".class": "NoneType"}}}, "quiet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.quiet", "name": "quiet", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "raw_input_original": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.raw_input_original", "name": "raw_input_original", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "refresh_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.refresh_style", "name": "refresh_style", "type": null}}, "register_magic_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "magic_kind", "magic_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.register_magic_function", "name": "register_magic_function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.register_magic_function", "name": "register_magic_function", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "magic_kind", "magic_name"], "arg_types": ["IPython.core.magic.MagicsManager", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "Parameters", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "func", "magic_kind", "magic_name"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._Wrapped"}}}}, "register_magics": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.register_magics", "name": "register_magics", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "register_post_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.register_post_execute", "name": "register_post_execute", "type": null}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "new_session", "aggressive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.reset", "name": "reset", "type": null}}, "reset_selective": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "regex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.reset_selective", "name": "reset_selective", "type": null}}, "restore_sys_module_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.restore_sys_module_state", "name": "restore_sys_module_state", "type": null}}, "rl_next_input": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.rl_next_input", "name": "rl_next_input", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run_ast_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "nodelist", "cell_name", "interactivity", "compiler", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "IPython.core.interactiveshell.InteractiveShell.run_ast_nodes", "name": "run_ast_nodes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "nodelist", "cell_name", "interactivity", "compiler", "result"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "Instance", "args": ["ast.stmt"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_ast_nodes of InteractiveShell", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_cell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "raw_cell", "store_history", "silent", "shell_futures", "cell_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.run_cell", "name": "run_cell", "type": null}}, "run_cell_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5], "arg_names": ["self", "raw_cell", "store_history", "silent", "shell_futures", "transformed_cell", "preprocessing_exc_tuple", "cell_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "IPython.core.interactiveshell.InteractiveShell.run_cell_async", "name": "run_cell_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5], "arg_names": ["self", "raw_cell", "store_history", "silent", "shell_futures", "transformed_cell", "preprocessing_exc_tuple", "cell_id"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_cell_async of InteractiveShell", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "IPython.core.interactiveshell.ExecutionResult"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_cell_magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "magic_name", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.run_cell_magic", "name": "run_cell_magic", "type": null}}, "run_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "code_obj", "result", "async_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "IPython.core.interactiveshell.InteractiveShell.run_code", "name": "run_code", "type": null}}, "run_line_magic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "magic_name", "line", "_stack_depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.run_line_magic", "name": "run_line_magic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "magic_name", "line", "_stack_depth"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_line_magic of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runcode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.runcode", "name": "runcode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "code_obj", "result", "async_"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_execfile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5], "arg_names": ["self", "fname", "where", "exit_ignore", "raise_exceptions", "shell_futures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.safe_execfile", "name": "safe_execfile", "type": null}}, "safe_execfile_ipy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "fname", "shell_futures", "raise_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.safe_execfile_ipy", "name": "safe_execfile_ipy", "type": null}}, "safe_run_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mod_name", "where"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.safe_run_module", "name": "safe_run_module", "type": null}}, "save_sys_module_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.save_sys_module_state", "name": "save_sys_module_state", "type": null}}, "separate_in": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.separate_in", "name": "separate_in", "type": "IPython.core.interactiveshell.SeparateUnicode"}}, "separate_out": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.separate_out", "name": "separate_out", "type": "IPython.core.interactiveshell.SeparateUnicode"}}, "separate_out2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.separate_out2", "name": "separate_out2", "type": "IPython.core.interactiveshell.SeparateUnicode"}}, "set_autoindent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_autoindent", "name": "set_autoindent", "type": null}}, "set_completer_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_completer_frame", "name": "set_completer_frame", "type": null}}, "set_custom_completer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "completer", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_custom_completer", "name": "set_custom_completer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "completer", "pos"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_custom_completer of InteractiveShell", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_custom_exc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc_tuple", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_custom_exc", "name": "set_custom_exc", "type": null}}, "set_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "hook", "priority", "str_key", "re_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_hook", "name": "set_hook", "type": null}}, "set_next_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "replace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_next_input", "name": "set_next_input", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_next_input", "name": "set_next_input", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "set_trio_runner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.set_trio_runner", "name": "set_trio_runner", "type": null}}, "should_run_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "raw_cell", "transformed_cell", "preprocessing_exc_tuple"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.should_run_async", "name": "should_run_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "raw_cell", "transformed_cell", "preprocessing_exc_tuple"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_run_async of InteractiveShell", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "show_banner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "banner"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.show_banner", "name": "show_banner", "type": null}}, "show_rewritten_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.show_rewritten_input", "name": "show_rewritten_input", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "show_usage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.show_usage", "name": "show_usage", "type": null}}, "show_usage_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.show_usage_error", "name": "show_usage_error", "type": null}}, "showindentationerror": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.showindentationerror", "name": "showindentationer<PERSON>r", "type": null}}, "showsyntaxerror": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "filename", "running_compiled_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.showsyntaxerror", "name": "showsyntaxerror", "type": null}}, "showtraceback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "exc_tuple", "filename", "tb_offset", "exception_only", "running_compiled_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.showtraceback", "name": "showtraceback", "type": null}}, "sphinxify_docstring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.sphinxify_docstring", "name": "sphinxify_docstring", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "starting_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.starting_dir", "name": "starting_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stdin_encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.stdin_encoding", "name": "stdin_encoding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "strdispatchers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.strdispatchers", "name": "strdispatchers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "switch_doctest_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.switch_doctest_mode", "name": "switch_doctest_mode", "type": null}}, "sys_excepthook": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.sys_excepthook", "name": "sys_excepthook", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.system", "name": "system", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cmd"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "system_piped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.system_piped", "name": "system_piped", "type": null}}, "system_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.system_raw", "name": "system_raw", "type": null}}, "tempdirs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.tempdirs", "name": "tempdirs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tempfiles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.tempfiles", "name": "tempfiles", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "transform_ast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.transform_ast", "name": "transform_ast", "type": null}}, "transform_cell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "raw_cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.transform_cell", "name": "transform_cell", "type": null}}, "trio_runner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.trio_runner", "name": "trio_runner", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "user_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expressions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.user_expressions", "name": "user_expressions", "type": null}}, "user_global_ns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.interactiveshell.InteractiveShell.user_global_ns", "name": "user_global_ns", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.user_global_ns", "name": "user_global_ns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.interactiveshell.InteractiveShell"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "user_global_ns of InteractiveShell", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_module": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.user_module", "name": "user_module", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "user_ns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.user_ns", "name": "user_ns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "user_ns_hidden": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.interactiveshell.InteractiveShell.user_ns_hidden", "name": "user_ns_hidden", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "var_expand": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "cmd", "depth", "formatter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShell.var_expand", "name": "var_expand", "type": null}}, "warn_venv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.warn_venv", "name": "warn_venv", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "wildcards_case_sensitive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.wildcards_case_sensitive", "name": "wildcards_case_sensitive", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "xmode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.InteractiveShell.xmode", "name": "xmode", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CaselessStrEnum"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.InteractiveShell.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.InteractiveShell", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InteractiveShellABC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.InteractiveShellABC", "name": "InteractiveShellABC", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.InteractiveShellABC", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.InteractiveShellABC", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.InteractiveShellABC.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.InteractiveShellABC", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InterruptiblePdb": {".class": "SymbolTableNode", "cross_ref": "IPython.core.debugger.InterruptiblePdb", "kind": "Gdef"}, "LSString": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.text.LSString", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.List", "kind": "Gdef"}, "ListType": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "cross_ref": "IPython.core.logger.Logger", "kind": "Gdef"}, "Macro": {".class": "SymbolTableNode", "cross_ref": "IPython.core.macro.Macro", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "OInfo": {".class": "SymbolTableNode", "cross_ref": "IPython.core.oinspect.OInfo", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PayloadManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.payload.PayloadManager", "kind": "Gdef"}, "PickleShareDB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.PickleShareDB", "name": "PickleShareDB", "type": {".class": "AnyType", "missing_import_name": "IPython.core.interactiveshell.PickleShareDB", "source_any": null, "type_of_any": 3}}}, "PrefilterManager": {".class": "SymbolTableNode", "cross_ref": "IPython.core.prefilter.PrefilterManager", "kind": "Gdef"}, "ProfileDir": {".class": "SymbolTableNode", "cross_ref": "IPython.core.profiledir.ProfileDir", "kind": "Gdef"}, "ProvisionalWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.DeprecationWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.ProvisionalWarning", "name": "ProvisionalWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.ProvisionalWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.ProvisionalWarning", "builtins.DeprecationWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.ProvisionalWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.ProvisionalWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyColorize": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.PyColorize", "kind": "Gdef"}, "SList": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.text.SList", "kind": "Gdef"}, "SeparateUnicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.SeparateUnicode", "name": "SeparateUnicode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.SeparateUnicode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.SeparateUnicode", "traitlets.traitlets.Unicode", "traitlets.traitlets.TraitType", "traitlets.traitlets.BaseDescriptor", "builtins.object"], "names": {".class": "SymbolTable", "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.SeparateUnicode.validate", "name": "validate", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.SeparateUnicode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.SeparateUnicode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SingletonConfigurable": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.configurable.SingletonConfigurable", "kind": "Gdef"}, "SpaceInInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.interactiveshell.SpaceInInput", "name": "SpaceInInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.SpaceInInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.interactiveshell", "mro": ["IPython.core.interactiveshell.SpaceInInput", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.interactiveshell.SpaceInInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.interactiveshell.SpaceInInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrDispatch": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.strdispatch.StrDispatch", "kind": "Gdef"}, "Struct": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.ipstruct.Struct", "kind": "Gdef"}, "TemporaryDirectory": {".class": "SymbolTableNode", "cross_ref": "tempfile.TemporaryDirectory", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Type", "kind": "Gdef"}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Unicode", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.UsageError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.interactiveshell.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.interactiveshell.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.interactiveshell.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.interactiveshell.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.interactiveshell.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.interactiveshell.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_assign_nodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell._assign_nodes", "name": "_assign_nodes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["target", "op", "value", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": false}, "ast.operator", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": ["ast.Aug<PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "AugAssign", "ret_type": "ast.Aug<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["target", "annotation", "value", "simple", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": false}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": ["ast.<PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": "ast.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 4], "arg_names": ["target", "annotation", "value", "simple", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": false}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": ["ast.<PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": "ast.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["targets", "value", "type_comment", "kwargs"], "arg_types": [{".class": "Instance", "args": ["ast.expr"], "extra_attrs": null, "type_ref": "builtins.list"}, "ast.expr", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": ["ast.Assign"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Assign", "ret_type": "ast.Assign", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_asyncio_runner": {".class": "SymbolTableNode", "cross_ref": "IPython.core.async_helpers._asyncio_runner", "kind": "Gdef"}, "_curio_runner": {".class": "SymbolTableNode", "cross_ref": "IPython.core.async_helpers._curio_runner", "kind": "Gdef"}, "_modified_open": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["file", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell._modified_open", "name": "_modified_open", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell._modified_open", "name": "_modified_open", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["file", "mode", "buffering", "encoding", "errors", "newline", "closefd", "opener"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.FileDescriptorOrPath"}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.OpenTextMode"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "imprecise_arg_kinds": false, "variables": []}, {".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}, {".class": "Parameters", "arg_kinds": [0, 2, 4], "arg_names": ["file", "args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "imprecise_arg_kinds": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._Wrapped"}}}}, "_pseudo_sync_runner": {".class": "SymbolTableNode", "cross_ref": "IPython.core.async_helpers._pseudo_sync_runner", "kind": "Gdef"}, "_should_be_async": {".class": "SymbolTableNode", "cross_ref": "IPython.core.async_helpers._should_be_async", "kind": "Gdef"}, "_single_targets_nodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell._single_targets_nodes", "name": "_single_targets_nodes", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["target", "op", "value", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": false}, "ast.operator", "ast.expr", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": ["ast.Aug<PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "AugAssign", "ret_type": "ast.Aug<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["target", "annotation", "value", "simple", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": false}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": ["ast.<PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": "ast.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 3, 4], "arg_names": ["target", "annotation", "value", "simple", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["ast.Name", "ast.Attribute", "ast.Subscript"], "uses_pep604_syntax": false}, "ast.expr", {".class": "UnionType", "items": ["ast.expr", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "TypedDictType", "fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "ast._Attributes"}, "items": [["lineno", "builtins.int"], ["col_offset", "builtins.int"], ["end_lineno", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["end_col_offset", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}], "bound_args": ["ast.<PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": "ast.<PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}]}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_trio_runner": {".class": "SymbolTableNode", "cross_ref": "IPython.core.async_helpers._trio_runner", "kind": "Gdef"}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "ask_yes_no": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.io.ask_yes_no", "kind": "Gdef"}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef"}, "atexit": {".class": "SymbolTableNode", "cross_ref": "atexit", "kind": "Gdef"}, "available_events": {".class": "SymbolTableNode", "cross_ref": "IPython.core.events.available_events", "kind": "Gdef"}, "bdb": {".class": "SymbolTableNode", "cross_ref": "bdb", "kind": "Gdef"}, "builtin_mod": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef"}, "dedent_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.interactiveshell.dedent_re", "name": "dedent_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "default": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.default", "kind": "Gdef"}, "default_banner": {".class": "SymbolTableNode", "cross_ref": "IPython.core.usage.default_banner", "kind": "Gdef"}, "display": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.display", "kind": "Gdef"}, "ensure_dir_exists": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.ensure_dir_exists", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "cross_ref": "logging.error", "kind": "Gdef"}, "format_screen": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.text.format_screen", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_home_dir": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.get_home_dir", "kind": "Gdef"}, "get_ipython_dir": {".class": "SymbolTableNode", "cross_ref": "IPython.paths.get_ipython_dir", "kind": "Gdef"}, "get_py_filename": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.get_py_filename", "kind": "Gdef"}, "getoutput": {".class": "SymbolTableNode", "cross_ref": "IPython.utils._process_win32.getoutput", "kind": "Gdef"}, "import_item": {".class": "SymbolTableNode", "cross_ref": "traitlets.utils.importstring.import_item", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.io", "kind": "Gdef"}, "io_open": {".class": "SymbolTableNode", "cross_ref": "_io.open", "kind": "Gdef"}, "is_integer_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.interactiveshell.is_integer_string", "name": "is_integer_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_integer_string", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic", "kind": "Gdef"}, "no_op": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["a", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.no_op", "name": "no_op", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.no_op", "name": "no_op", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "observe": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.observe", "kind": "Gdef"}, "oinspect": {".class": "SymbolTableNode", "cross_ref": "IPython.core.oinspect", "kind": "Gdef"}, "openpy": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.openpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "page": {".class": "SymbolTableNode", "cross_ref": "IPython.core.page", "kind": "Gdef"}, "prefilter": {".class": "SymbolTableNode", "cross_ref": "IPython.core.prefilter", "kind": "Gdef"}, "prepended_to_syspath": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.syspathcontext.prepended_to_syspath", "kind": "Gdef"}, "py3compat": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.py3compat", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "runpy": {".class": "SymbolTableNode", "cross_ref": "runpy", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "skip_doctest": {".class": "SymbolTableNode", "cross_ref": "IPython.testing.skipdoctest.skip_doctest", "kind": "Gdef"}, "softspace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["file", "newvalue"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.interactiveshell.softspace", "name": "softspace", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.softspace", "name": "softspace", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "sphinxify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.interactiveshell.sphinxify", "name": "sphinxify", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sphx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.core.interactiveshell.sphx", "name": "sphx", "type": {".class": "AnyType", "missing_import_name": "IPython.core.interactiveshell.sphx", "source_any": null, "type_of_any": 3}}}, "stmt": {".class": "SymbolTableNode", "cross_ref": "ast.stmt", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "system": {".class": "SymbolTableNode", "cross_ref": "IPython.utils._process_win32.system", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "ultratb": {".class": "SymbolTableNode", "cross_ref": "IPython.core.ultratb", "kind": "Gdef"}, "undoc": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.decorators.undoc", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.validate", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\interactiveshell.py"}