{"data_mtime": 1753781401, "dep_lines": [11, 12, 12, 13, 14, 15, 16, 17, 403, 12, 585, 4, 5, 6, 7, 8, 9, 585, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 5, 10, 10, 5, 10, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["traitlets.utils.importstring", "IPython.core.magic_arguments", "IPython.core.page", "IPython.core.error", "IPython.core.magic", "IPython.utils.text", "IPython.testing.skipdoctest", "IPython.utils.ipstruct", "IPython.core.usage", "IPython.core", "nbformat.v4", "logging", "io", "os", "pprint", "sys", "warnings", "nbformat", "builtins", "operator", "collections", "string", "json", "contextlib", "copy", "itertools", "inspect", "html", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "typing", "IPython.testing", "IPython.utils", "_frozen_importlib", "_typeshed", "abc", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "typing_extensions"], "hash": "6ba276ce40a846e34f319355256bdf03467e7ecd", "id": "IPython.core.magics.basic", "ignore_all": true, "interface_hash": "f70db80ef589258ad0c4188a3e3f8d91c60ee840", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\basic.py", "plugin_data": null, "size": 23207, "suppressed": [], "version_id": "1.15.0"}