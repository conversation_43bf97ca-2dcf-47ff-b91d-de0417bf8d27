{".class": "MypyFile", "_fullname": "IPython.core.magics.code", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CodeMagics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.magic.Magics"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.code.CodeMagics", "name": "CodeMagics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.CodeMagics", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.magics.code", "mro": ["IPython.core.magics.code.CodeMagics", "IPython.core.magic.Magics", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.CodeMagics.__init__", "name": "__init__", "type": null}}, "_edit_macro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mname", "macro"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.CodeMagics._edit_macro", "name": "_edit_macro", "type": null}}, "_find_edit_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["shell", "args", "opts", "last_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.magics.code.CodeMagics._find_edit_target", "name": "_find_edit_target", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.code.CodeMagics._find_edit_target", "name": "_find_edit_target", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["shell", "args", "opts", "last_call"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_edit_target of CodeMagics", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_knowntemps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.code.CodeMagics._knowntemps", "name": "_knowntemps", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "edit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "parameter_s", "last_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.code.CodeMagics.edit", "name": "edit", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.code.CodeMagics.edit", "name": "edit", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.code.CodeMagics.load", "name": "load", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.code.CodeMagics.load", "name": "load", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "loadpy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.code.CodeMagics.loadpy", "name": "loadpy", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.code.CodeMagics.loadpy", "name": "loadpy", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "pastebin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.code.CodeMagics.pastebin", "name": "pastebin", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.code.CodeMagics.pastebin", "name": "pastebin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.code.CodeMagics.save", "name": "save", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.code.CodeMagics.save", "name": "save", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.code.CodeMagics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.code.CodeMagics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InteractivelyDefined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.code.InteractivelyDefined", "name": "InteractivelyDefined", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.InteractivelyDefined", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.magics.code", "mro": ["IPython.core.magics.code.InteractivelyDefined", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.InteractivelyDefined.__init__", "name": "__init__", "type": null}}, "index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.code.InteractivelyDefined.index", "name": "index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.code.InteractivelyDefined.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.code.InteractivelyDefined", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Macro": {".class": "SymbolTableNode", "cross_ref": "IPython.core.macro.Macro", "kind": "Gdef"}, "MacroToEdit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.code.MacroToEdit", "name": "MacroToEdit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.MacroToEdit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.magics.code", "mro": ["IPython.core.magics.code.MacroToEdit", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.code.MacroToEdit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.code.MacroToEdit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Magics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.Magics", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "urllib.request.Request", "kind": "Gdef"}, "StdinNotImplementedError": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.StdinNotImplementedError", "kind": "Gdef"}, "TryNext": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.TryNext", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.UsageError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.code.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.code.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.code.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.code.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.code.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.code.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "cross_ref": "logging.error", "kind": "Gdef"}, "extract_code_ranges": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ranges_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.extract_code_ranges", "name": "extract_code_ranges", "type": null}}, "extract_symbols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["code", "symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.extract_symbols", "name": "extract_symbols", "type": null}}, "find_file": {".class": "SymbolTableNode", "cross_ref": "IPython.core.oinspect.find_file", "kind": "Gdef"}, "find_source_lines": {".class": "SymbolTableNode", "cross_ref": "IPython.core.oinspect.find_source_lines", "kind": "Gdef"}, "get_py_filename": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.get_py_filename", "kind": "Gdef"}, "get_text_list": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.text.get_text_list", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "ipython_input_pat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magics.code.ipython_input_pat", "name": "ipython_input_pat", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "line_magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.line_magic", "kind": "Gdef"}, "magics_class": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.magics_class", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "preserve_keys": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.contexts.preserve_keys", "kind": "Gdef"}, "range_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magics.code.range_re", "name": "range_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "skip_doctest": {".class": "SymbolTableNode", "cross_ref": "IPython.testing.skipdoctest.skip_doctest", "kind": "Gdef"}, "strip_initial_indent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.code.strip_initial_indent", "name": "strip_initial_indent", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "urlencode": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlencode", "kind": "Gdef"}, "urlopen": {".class": "SymbolTableNode", "cross_ref": "urllib.request.urlopen", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "IPython.core.release.version", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\code.py"}