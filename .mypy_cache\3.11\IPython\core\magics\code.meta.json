{"data_mtime": 1753781401, "dep_lines": [28, 29, 30, 31, 32, 33, 34, 35, 38, 23, 24, 16, 17, 18, 19, 20, 21, 22, 25, 36, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.error", "IPython.core.macro", "IPython.core.magic", "IPython.core.oinspect", "IPython.core.release", "IPython.testing.skipdoctest", "IPython.utils.contexts", "IPython.utils.path", "IPython.utils.text", "urllib.request", "urllib.parse", "inspect", "io", "os", "re", "sys", "ast", "itertools", "pathlib", "warnings", "logging", "builtins", "pprint", "operator", "collections", "string", "json", "contextlib", "copy", "html", "traitlets.utils.warnings", "functools", "types", "traceback", "typing", "IPython.testing", "_frozen_importlib", "abc", "enum", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets"], "hash": "99f93a408ac613bd0c79dae96aac0ab7e70ec410", "id": "IPython.core.magics.code", "ignore_all": true, "interface_hash": "d6d9924788126e20dadb7a325f3f24eae3b8f92d", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\code.py", "plugin_data": null, "size": 28144, "suppressed": [], "version_id": "1.15.0"}