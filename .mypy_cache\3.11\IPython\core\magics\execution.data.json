{".class": "MypyFile", "_fullname": "IPython.core.magics.execution", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Assign": {".class": "SymbolTableNode", "cross_ref": "ast.Assign", "kind": "Gdef"}, "Call": {".class": "SymbolTableNode", "cross_ref": "ast.Call", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DisplayHook": {".class": "SymbolTableNode", "cross_ref": "IPython.core.displayhook.DisplayHook", "kind": "Gdef"}, "ExecutionMagics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.magic.Magics"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.execution.ExecutionMagics", "name": "ExecutionMagics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.ExecutionMagics", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.magics.execution", "mro": ["IPython.core.magics.execution.ExecutionMagics", "IPython.core.magic.Magics", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.ExecutionMagics.__init__", "name": "__init__", "type": null}}, "_debug_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "code", "breakpoint", "local_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.ExecutionMagics._debug_exec", "name": "_debug_exec", "type": null}}, "_debug_post_mortem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.ExecutionMagics._debug_post_mortem", "name": "_debug_post_mortem", "type": null}}, "_run_with_debugger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "code", "code_ns", "filename", "bp_line", "bp_file", "local_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.ExecutionMagics._run_with_debugger", "name": "_run_with_debugger", "type": null}}, "_run_with_profiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "code", "opts", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.ExecutionMagics._run_with_profiler", "name": "_run_with_profiler", "type": null}}, "_run_with_timing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["run", "nruns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics._run_with_timing", "name": "_run_with_timing", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics._run_with_timing", "name": "_run_with_timing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["run", "nruns"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_with_timing of ExecutionMagics", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_transformers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.core.magics.execution.ExecutionMagics._transformers", "name": "_transformers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "capture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.capture", "name": "capture", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.capture", "name": "capture", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "code_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "line", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.code_wrap", "name": "code_wrap", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.code_wrap", "name": "code_wrap", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "line", "cell", "local_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.debug", "name": "debug", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.debug", "name": "debug", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "default_runner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.default_runner", "name": "default_runner", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "macro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.macro", "name": "macro", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.macro", "name": "macro", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "pdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.pdb", "name": "pdb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.pdb", "name": "pdb", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "prun": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "parameter_s", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.prun", "name": "prun", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.prun", "name": "prun", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "parameter_s", "runner", "file_finder"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.run", "name": "run", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.run", "name": "run", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "tb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.tb", "name": "tb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.tb", "name": "tb", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "line", "cell", "local_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.time", "name": "time", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.time", "name": "time", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "timeit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "line", "cell", "local_ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.execution.ExecutionMagics.timeit", "name": "timeit", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.ExecutionMagics.timeit", "name": "timeit", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.execution.ExecutionMagics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.execution.ExecutionMagics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Expr": {".class": "SymbolTableNode", "cross_ref": "ast.Expr", "kind": "Gdef"}, "Load": {".class": "SymbolTableNode", "cross_ref": "ast.Load", "kind": "Gdef"}, "Macro": {".class": "SymbolTableNode", "cross_ref": "IPython.core.macro.Macro", "kind": "Gdef"}, "Magics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.Magics", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Name": {".class": "SymbolTableNode", "cross_ref": "ast.Name", "kind": "Gdef"}, "NodeTransformer": {".class": "SymbolTableNode", "cross_ref": "ast.NodeTransformer", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ReplaceCodeTransformer": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magics.ast_mod.ReplaceCodeTransformer", "kind": "Gdef"}, "Restart": {".class": "SymbolTableNode", "cross_ref": "pdb.<PERSON><PERSON>", "kind": "Gdef"}, "Store": {".class": "SymbolTableNode", "cross_ref": "ast.Store", "kind": "Gdef"}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef"}, "Struct": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.ipstruct.Struct", "kind": "Gdef"}, "TimeitResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.execution.TimeitResult", "name": "TimeitResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.magics.execution", "mro": ["IPython.core.magics.execution.TimeitResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "loops", "repeat", "best", "worst", "all_runs", "compile_time", "precision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitResult.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitResult.__str__", "name": "__str__", "type": null}}, "_precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult._precision", "name": "_precision", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_repr_pretty_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "p", "cycle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitResult._repr_pretty_", "name": "_repr_pretty_", "type": null}}, "all_runs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.all_runs", "name": "all_runs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "average": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.magics.execution.TimeitResult.average", "name": "average", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.average", "name": "average", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.magics.execution.TimeitResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "average of TimeitResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "best": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.best", "name": "best", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "compile_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.compile_time", "name": "compile_time", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "loops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.loops", "name": "loops", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "repeat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.repeat", "name": "repeat", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stdev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.core.magics.execution.TimeitResult.stdev", "name": "stdev", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.stdev", "name": "stdev", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.core.magics.execution.TimeitResult"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stdev of TimeitResult", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "timings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.timings", "name": "timings", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "worst": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitResult.worst", "name": "worst", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.execution.TimeitResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.execution.TimeitResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeitTemplateFiller": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ast.NodeTransformer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.execution.TimeitTemplateFiller", "name": "TimeitTemplateFiller", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitTemplateFiller", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.magics.execution", "mro": ["IPython.core.magics.execution.TimeitTemplateFiller", "ast.NodeTransformer", "ast.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ast_setup", "ast_stmt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitTemplateFiller.__init__", "name": "__init__", "type": null}}, "ast_setup": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitTemplateFiller.ast_setup", "name": "ast_setup", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ast_stmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.magics.execution.TimeitTemplateFiller.ast_stmt", "name": "ast_stmt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "visit_For": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitTemplateFiller.visit_For", "name": "visit_For", "type": null}}, "visit_FunctionDef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.TimeitTemplateFiller.visit_FunctionDef", "name": "visit_FunctionDef", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.execution.TimeitTemplateFiller.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.execution.TimeitTemplateFiller", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Timer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["timeit.Timer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.execution.Timer", "name": "Timer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.Timer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.magics.execution", "mro": ["IPython.core.magics.execution.Timer", "timeit.Timer", "builtins.object"], "names": {".class": "SymbolTable", "timeit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.Timer.timeit", "name": "timeit", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.execution.Timer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.execution.Timer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.UsageError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.execution.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.execution.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.execution.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.execution.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.execution.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.execution.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_format_time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["timespan", "precision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution._format_time", "name": "_format_time", "type": null}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef"}, "bdb": {".class": "SymbolTableNode", "cross_ref": "bdb", "kind": "Gdef"}, "builtin_mod": {".class": "SymbolTableNode", "cross_ref": "builtins", "kind": "Gdef"}, "capture_output": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.capture.capture_output", "kind": "Gdef"}, "cell_magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.cell_magic", "kind": "Gdef"}, "clock": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.timing.clock", "kind": "Gdef"}, "clock2": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.timing.clock2", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "dedent": {".class": "SymbolTableNode", "cross_ref": "textwrap.dedent", "kind": "Gdef"}, "error": {".class": "SymbolTableNode", "cross_ref": "logging.error", "kind": "Gdef"}, "find_mod": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.module_paths.find_mod", "kind": "Gdef"}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef"}, "get_py_filename": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.get_py_filename", "kind": "Gdef"}, "indent": {".class": "SymbolTableNode", "cross_ref": "textwrap.indent", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "line_cell_magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.line_cell_magic", "kind": "Gdef"}, "line_magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.line_magic", "kind": "Gdef"}, "magic_arguments": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic_arguments", "kind": "Gdef"}, "magics_class": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.magics_class", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "needs_local_scope": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.needs_local_scope", "kind": "Gdef"}, "no_var_expand": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.no_var_expand", "kind": "Gdef"}, "oinspect": {".class": "SymbolTableNode", "cross_ref": "IPython.core.oinspect", "kind": "Gdef"}, "on_off": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.on_off", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "output_can_be_silenced": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.output_can_be_silenced", "kind": "Gdef"}, "page": {".class": "SymbolTableNode", "cross_ref": "IPython.core.page", "kind": "Gdef"}, "parse": {".class": "SymbolTableNode", "cross_ref": "ast.parse", "kind": "Gdef"}, "parse_breakpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["text", "current_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.magics.execution.parse_breakpoint", "name": "parse_breakpoint", "type": null}}, "preserve_keys": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.contexts.preserve_keys", "kind": "Gdef"}, "profile": {".class": "SymbolTableNode", "cross_ref": "cProfile", "kind": "Gdef"}, "pstats": {".class": "SymbolTableNode", "cross_ref": "pstats", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "shellglob": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.shellglob", "kind": "Gdef"}, "shlex": {".class": "SymbolTableNode", "cross_ref": "shlex", "kind": "Gdef"}, "skip_doctest": {".class": "SymbolTableNode", "cross_ref": "IPython.testing.skipdoctest.skip_doctest", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "timeit": {".class": "SymbolTableNode", "cross_ref": "timeit", "kind": "Gdef"}, "unparse": {".class": "SymbolTableNode", "cross_ref": "ast.unparse", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\execution.py"}