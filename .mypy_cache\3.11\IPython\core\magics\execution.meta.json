{"data_mtime": 1753781401, "dep_lines": [65, 43, 43, 43, 44, 45, 46, 47, 58, 59, 60, 61, 62, 63, 64, 43, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 36, 37, 38, 39, 40, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.magics.ast_mod", "IPython.core.magic_arguments", "IPython.core.oinspect", "IPython.core.page", "IPython.core.displayhook", "IPython.core.error", "IPython.core.macro", "IPython.core.magic", "IPython.testing.skipdoctest", "IPython.utils.capture", "IPython.utils.contexts", "IPython.utils.ipstruct", "IPython.utils.module_paths", "IPython.utils.path", "IPython.utils.timing", "IPython.core", "ast", "bdb", "builtins", "copy", "cProfile", "gc", "itertools", "math", "os", "pstats", "re", "shlex", "sys", "time", "timeit", "typing", "io", "logging", "pathlib", "pdb", "textwrap", "warnings", "pprint", "operator", "collections", "string", "json", "contextlib", "inspect", "html", "traitlets.utils.warnings", "functools", "types", "traceback", "IPython.testing", "IPython.utils", "_frozen_importlib", "_io", "_lsprof", "_typeshed", "_typeshed.importlib", "_warnings", "abc", "enum", "profile", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing_extensions"], "hash": "89cb2baa199ae5c6a96c7450af4549f708219591", "id": "IPython.core.magics.execution", "ignore_all": true, "interface_hash": "7abbdaffc4911c5a2c556057791d9f9f173cf8cb", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\execution.py", "plugin_data": null, "size": 61631, "suppressed": [], "version_id": "1.15.0"}