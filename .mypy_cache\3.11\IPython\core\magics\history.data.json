{".class": "MypyFile", "_fullname": "IPython.core.magics.history", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HistoryMagics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.magic.Magics"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.magics.history.HistoryMagics", "name": "HistoryMagics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.magics.history.HistoryMagics", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.magics.history", "mro": ["IPython.core.magics.history.HistoryMagics", "IPython.core.magic.Magics", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "history": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.history.HistoryMagics.history", "name": "history", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.history.HistoryMagics.history", "name": "history", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "recall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.history.HistoryMagics.recall", "name": "recall", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.history.HistoryMagics.recall", "name": "recall", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "rerun": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.magics.history.HistoryMagics.rerun", "name": "rerun", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.magics.history.HistoryMagics.rerun", "name": "rerun", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.magics.history.HistoryMagics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.magics.history.HistoryMagics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Magics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.Magics", "kind": "Gdef"}, "StdinNotImplementedError": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.StdinNotImplementedError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.history.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.history.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.history.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.history.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.history.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.magics.history.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_unspecified": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.magics.history._unspecified", "name": "_unspecified", "type": "builtins.object"}}, "argument": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic_arguments.argument", "kind": "Gdef"}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.io", "kind": "Gdef"}, "io_open": {".class": "SymbolTableNode", "cross_ref": "_io.open", "kind": "Gdef"}, "line_magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.line_magic", "kind": "Gdef"}, "magic_arguments": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic_arguments.magic_arguments", "kind": "Gdef"}, "magics_class": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.magics_class", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_argstring": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic_arguments.parse_argstring", "kind": "Gdef"}, "skip_doctest": {".class": "SymbolTableNode", "cross_ref": "IPython.testing.skipdoctest.skip_doctest", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\history.py"}