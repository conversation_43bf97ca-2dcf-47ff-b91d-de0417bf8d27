{"data_mtime": 1753781401, "dep_lines": [21, 22, 23, 24, 25, 26, 27, 21, 16, 17, 18, 399, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.page", "IPython.core.error", "IPython.core.magic", "IPython.testing.skipdoctest", "IPython.utils.encoding", "IPython.utils.openpy", "IPython.utils.path", "IPython.core", "gc", "re", "sys", "numpy", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "traitlets.utils.warnings", "functools", "types", "traceback", "typing", "IPython.testing", "_frozen_importlib", "_typeshed", "abc", "array", "mmap", "numpy._typing", "numpy._typing._dtype_like", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets"], "hash": "c93874a0504ebe569b9afe5228213bf47cd9d04d", "id": "IPython.core.magics.namespace", "ignore_all": true, "interface_hash": "6338a0a88f40e2b95ed314b409e2178c1e00fc78", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\namespace.py", "plugin_data": null, "size": 24825, "suppressed": [], "version_id": "1.15.0"}