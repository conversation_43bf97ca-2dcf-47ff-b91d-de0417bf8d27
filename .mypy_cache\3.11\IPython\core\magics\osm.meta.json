{"data_mtime": 1753781401, "dep_lines": [16, 17, 18, 19, 20, 21, 24, 25, 26, 27, 16, 9, 10, 11, 12, 13, 14, 28, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.magic_arguments", "IPython.core.oinspect", "IPython.core.page", "IPython.core.alias", "IPython.core.error", "IPython.core.magic", "IPython.testing.skipdoctest", "IPython.utils.openpy", "IPython.utils.process", "IPython.utils.terminal", "IPython.core", "io", "os", "pathlib", "re", "sys", "pprint", "traitlets", "warnings", "builtins", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "inspect", "html", "traitlets.utils.warnings", "functools", "types", "traceback", "typing", "IPython.testing", "_frozen_importlib", "_warnings", "abc", "enum", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing_extensions"], "hash": "e2a20a6717bfa8759bed4cbfcbaa07f0b060f1a8", "id": "IPython.core.magics.osm", "ignore_all": true, "interface_hash": "e1b2eed6f8ea2c9f5ef06d5e1be61f2a49134203", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\osm.py", "plugin_data": null, "size": 30696, "suppressed": [], "version_id": "1.15.0"}