{"data_mtime": 1753781401, "dep_lines": [19, 20, 21, 22, 7, 19, 6, 8, 9, 10, 11, 12, 13, 14, 15, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.magic_arguments", "IPython.core.async_helpers", "IPython.core.magic", "IPython.utils.process", "asyncio.exceptions", "IPython.core", "asyncio", "atexit", "errno", "os", "signal", "sys", "time", "subprocess", "threading", "traitlets", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "inspect", "html", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "typing", "IPython.utils", "IPython.utils._process_win32", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "asyncio.subprocess", "asyncio.windows_events", "enum", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel"], "hash": "a935cb7152d7a81f0b21a09f90966b11cff8a126", "id": "IPython.core.magics.script", "ignore_all": true, "interface_hash": "2100dc2bb24c1a4d740ff81f5be2f654a40d2277", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\script.py", "plugin_data": null, "size": 13286, "suppressed": [], "version_id": "1.15.0"}