{"data_mtime": 1753781401, "dep_lines": [43, 44, 45, 46, 46, 47, 48, 49, 50, 51, 52, 53, 43, 46, 15, 16, 17, 18, 19, 21, 22, 23, 24, 25, 28, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 56, 57, 55], "dep_prios": [10, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 20, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["IPython.core.page", "IPython.lib.pretty", "IPython.testing.skipdoctest", "IPython.utils.PyColorize", "IPython.utils.openpy", "IPython.utils.dir2", "IPython.utils.path", "IPython.utils.text", "IPython.utils.wildcard", "IPython.utils.coloransi", "IPython.utils.colorable", "IPython.utils.decorators", "IPython.core", "IPython.utils", "dataclasses", "inspect", "textwrap", "ast", "html", "io", "linecache", "os", "types", "warnings", "typing", "traitlets", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "sys", "traitlets.utils.warnings", "functools", "re", "traceback", "IPython.testing", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing_extensions"], "hash": "4d25d405e2b6592b87ee6a709e4e50a890d7ebaa", "id": "IPython.core.oinspect", "ignore_all": true, "interface_hash": "6eea60565bb5dbbee4f0b85730c3a56150ff7305", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\oinspect.py", "plugin_data": null, "size": 42747, "suppressed": ["pygments.lexers", "pygments.formatters", "pygments"], "version_id": "1.15.0"}