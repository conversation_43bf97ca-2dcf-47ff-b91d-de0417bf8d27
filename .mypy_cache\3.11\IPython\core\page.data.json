{".class": "MypyFile", "_fullname": "IPython.core.page", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "TryNext": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.TryNext", "kind": "Gdef"}, "UnsupportedOperation": {".class": "SymbolTableNode", "cross_ref": "io.UnsupportedOperation", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.page.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.page.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.page.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.page.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.page.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.page.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_detect_screen_size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["screen_lines_def"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page._detect_screen_size", "name": "_detect_screen_size", "type": null}}, "as_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["page_func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.as_hook", "name": "as_hook", "type": null}}, "chop": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.data.chop", "kind": "Gdef"}, "display": {".class": "SymbolTableNode", "cross_ref": "IPython.core.display_functions.display", "kind": "Gdef"}, "display_page": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["strng", "start", "screen_lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.display_page", "name": "display_page", "type": null}}, "esc_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.page.esc_re", "name": "esc_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "get_ipython": {".class": "SymbolTableNode", "cross_ref": "IPython.core.getipython.get_ipython", "kind": "Gdef"}, "get_pager_cmd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["pager_cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.get_pager_cmd", "name": "get_pager_cmd", "type": null}}, "get_pager_start": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pager", "start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.get_pager_start", "name": "get_pager_start", "type": null}}, "get_terminal_size": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.terminal.get_terminal_size", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "msvcrt": {".class": "SymbolTableNode", "cross_ref": "msvcrt", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "page": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["data", "start", "screen_lines", "pager_cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.page", "name": "page", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["data", "start", "screen_lines", "pager_cmd"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "page", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "page_dumb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["strng", "start", "screen_lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.page_dumb", "name": "page_dumb", "type": null}}, "page_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["fname", "start", "pager_cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.page_file", "name": "page_file", "type": null}}, "page_more": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.page_more", "name": "page_more", "type": null}}, "pager_page": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["strng", "start", "screen_lines", "pager_cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.page.pager_page", "name": "pager_page", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["strng", "start", "screen_lines", "pager_cmd"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pager_page", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "py3compat": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.py3compat", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "system": {".class": "SymbolTableNode", "cross_ref": "IPython.utils._process_win32.system", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\page.py"}