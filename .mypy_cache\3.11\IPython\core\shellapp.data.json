{".class": "MypyFile", "_fullname": "IPython.core.shellapp", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef"}, "CaselessStrEnum": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.CaselessStrEnum", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.loader.Config", "kind": "Gdef"}, "Configurable": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.configurable.Configurable", "kind": "Gdef"}, "DottedObjectName": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.DottedObjectName", "kind": "Gdef"}, "ENV_CONFIG_DIRS": {".class": "SymbolTableNode", "cross_ref": "IPython.core.application.ENV_CONFIG_DIRS", "kind": "Gdef"}, "Instance": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Instance", "kind": "Gdef"}, "InteractiveShellApp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["traitlets.config.configurable.Configurable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.shellapp.InteractiveShellApp", "name": "InteractiveShellApp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.core.shellapp", "mro": ["IPython.core.shellapp.InteractiveShellApp", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "_exec_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "fname", "shell_futures"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp._exec_file", "name": "_exec_file", "type": null}}, "_run_cmd_line_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp._run_cmd_line_code", "name": "_run_cmd_line_code", "type": null}}, "_run_exec_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp._run_exec_files", "name": "_run_exec_files", "type": null}}, "_run_exec_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp._run_exec_lines", "name": "_run_exec_lines", "type": null}}, "_run_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp._run_module", "name": "_run_module", "type": null}}, "_run_startup_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp._run_startup_files", "name": "_run_startup_files", "type": null}}, "_user_ns_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "change"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.core.shellapp.InteractiveShellApp._user_ns_changed", "name": "_user_ns_changed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.core.shellapp.InteractiveShellApp._user_ns_changed", "name": "_user_ns_changed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "code_to_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.code_to_run", "name": "code_to_run", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "default_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.default_extensions", "name": "default_extensions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "exec_PYTHONSTARTUP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.exec_PYTHONSTARTUP", "name": "exec_PYTHONSTARTUP", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "exec_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.exec_files", "name": "exec_files", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "exec_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.exec_lines", "name": "exec_lines", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.extensions", "name": "extensions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "extra_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.extra_extensions", "name": "extra_extensions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "traitlets.traitlets.List"}}}, "file_to_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.file_to_run", "name": "file_to_run", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "gui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.gui", "name": "gui", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CaselessStrEnum"}}}, "hide_initial_ns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.hide_initial_ns", "name": "hide_initial_ns", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "ignore_cwd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.ignore_cwd", "name": "ignore_cwd", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "init_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp.init_code", "name": "init_code", "type": null}}, "init_extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp.init_extensions", "name": "init_extensions", "type": null}}, "init_gui_pylab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp.init_gui_pylab", "name": "init_gui_pylab", "type": null}}, "init_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp.init_path", "name": "init_path", "type": null}}, "init_shell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.InteractiveShellApp.init_shell", "name": "init_shell", "type": null}}, "interact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.interact", "name": "interact", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "matplotlib": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.matplotlib", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum"}}, "module_to_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.module_to_run", "name": "module_to_run", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "pylab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.pylab", "name": "pylab", "type": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum"}}, "pylab_import_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.pylab_import_all", "name": "pylab_import_all", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "reraise_ipython_extension_failures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.reraise_ipython_extension_failures", "name": "reraise_ipython_extension_failures", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "shell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.shell", "name": "shell", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}, "user_ns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.InteractiveShellApp.user_ns", "name": "user_ns", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Instance"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.shellapp.InteractiveShellApp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.shellapp.InteractiveShellApp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.List", "kind": "Gdef"}, "MatplotlibBackendCaselessStrEnum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CaselessStrEnum"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum", "name": "MatplotlibBackendCaselessStrEnum", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.shellapp", "mro": ["IPython.core.shellapp.MatplotlibBackendCaselessStrEnum", "traitlets.traitlets.CaselessStrEnum", "traitlets.traitlets.Enum", "traitlets.traitlets.TraitType", "traitlets.traitlets.BaseDescriptor", "builtins.object"], "names": {".class": "SymbolTable", "__getattribute__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum.__getattribute__", "name": "__getattribute__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "default_value", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "default_value", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CaselessStrEnum"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MatplotlibBackendCaselessStrEnum", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.shellapp.MatplotlibBackendCaselessStrEnum", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SYSTEM_CONFIG_DIRS": {".class": "SymbolTableNode", "cross_ref": "IPython.core.application.SYSTEM_CONFIG_DIRS", "kind": "Gdef"}, "Undefined": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Undefined", "kind": "Gdef"}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Unicode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.shellapp.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.shellapp.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.shellapp.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.shellapp.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.shellapp.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.shellapp.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "addflag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.addflag", "name": "addflag", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["args"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "boolean_flag": {".class": "SymbolTableNode", "cross_ref": "traitlets.config.application.boolean_flag", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "filefind": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.path.filefind", "kind": "Gdef"}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef"}, "gui_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.gui_keys", "name": "gui_keys", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "nosep_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.nosep_config", "name": "nosep_config", "type": "traitlets.config.loader.Config"}}, "observe": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.observe", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "preserve_keys": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.contexts.preserve_keys", "kind": "Gdef"}, "pt_inputhooks": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.pt_inputhooks", "kind": "Gdef"}, "shell_aliases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.shell_aliases", "name": "shell_aliases", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "shell_flags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.shellapp.shell_flags", "name": "shell_flags", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\shellapp.py"}