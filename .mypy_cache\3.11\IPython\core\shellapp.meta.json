{"data_mtime": 1753781401, "dep_lines": [16, 17, 18, 19, 20, 21, 32, 135, 32, 10, 11, 12, 13, 14, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 20, 20, 10, 5, 10, 10, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["traitlets.config.application", "traitlets.config.configurable", "traitlets.config.loader", "IPython.core.application", "IPython.utils.contexts", "IPython.utils.path", "IPython.terminal.pt_inputhooks", "IPython.core.pylabtools", "IPython.terminal", "glob", "itertools", "os", "sys", "typing", "traitlets", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "inspect", "html", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "IPython.utils", "_frozen_importlib", "_typeshed", "abc", "traitlets.config", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel"], "hash": "f7a97e312bbdc77ac6150b9f9fc82f8f669bea55", "id": "IPython.core.shellapp", "ignore_all": true, "interface_hash": "96ac6553a3d683d1e217ed47facd3398632777a2", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\shellapp.py", "plugin_data": null, "size": 19270, "suppressed": [], "version_id": "1.15.0"}