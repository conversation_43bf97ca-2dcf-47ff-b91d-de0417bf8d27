{"data_mtime": 1753781401, "dep_lines": [26, 27, 28, 26, 23, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["IPython.utils.py3compat", "IPython.utils.encoding", "IPython.core.oinspect", "IPython.utils", "re", "sys", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "traitlets.utils.warnings", "functools", "types", "traceback", "typing", "_frozen_importlib", "abc", "enum"], "hash": "0c2023c2ccf07a33420acfd46f03eeef835e8a6d", "id": "IPython.core.splitinput", "ignore_all": true, "interface_hash": "22d135f888071ac3be65e2a1a631fe79f93ea439", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\splitinput.py", "plugin_data": null, "size": 5014, "suppressed": [], "version_id": "1.15.0"}