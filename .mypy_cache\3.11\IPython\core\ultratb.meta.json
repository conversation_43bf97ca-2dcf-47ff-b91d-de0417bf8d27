{"data_mtime": 1753781401, "dep_lines": [108, 111, 112, 113, 114, 115, 116, 117, 92, 108, 111, 801, 93, 94, 95, 96, 97, 98, 99, 100, 102, 104, 108, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 105, 106], "dep_prios": [10, 10, 5, 5, 10, 10, 10, 5, 5, 20, 20, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["IPython.utils.colorable", "IPython.core.debugger", "IPython.core.display_trap", "IPython.core.excolors", "IPython.utils.PyColorize", "IPython.utils.path", "IPython.utils.py3compat", "IPython.utils.terminal", "collections.abc", "IPython.utils", "IPython.core", "executing.executing", "functools", "inspect", "linecache", "pydoc", "sys", "time", "traceback", "types", "typing", "stack_data", "IPython", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "html", "traitlets.utils.warnings", "re", "IPython.core.getipython", "_frozen_importlib", "_typeshed", "abc", "bdb", "cmd", "pdb", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing_extensions"], "hash": "4478aa0e1d6e248c4a28b120d9a40fe12e1e7cb2", "id": "IPython.core.ultratb", "ignore_all": true, "interface_hash": "0232e5dab220f845a9452588e03a0e95d633db9d", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\ultratb.py", "plugin_data": null, "size": 56668, "suppressed": ["pygments.formatters.terminal256", "pygments.styles"], "version_id": "1.15.0"}