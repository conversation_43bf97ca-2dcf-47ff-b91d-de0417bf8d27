{".class": "MypyFile", "_fullname": "IPython.terminal.embed", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Bool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Bool", "kind": "Gdef"}, "CBool": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.CBool", "kind": "Gdef"}, "DummyMod": {".class": "SymbolTableNode", "cross_ref": "IPython.core.interactiveshell.DummyMod", "kind": "Gdef"}, "EmbeddedMagics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.magic.Magics"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.embed.EmbeddedMagics", "name": "EmbeddedMagics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.EmbeddedMagics", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.terminal.embed", "mro": ["IPython.terminal.embed.EmbeddedMagics", "IPython.core.magic.Magics", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "exit_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.embed.EmbeddedMagics.exit_raise", "name": "exit_raise", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.embed.EmbeddedMagics.exit_raise", "name": "exit_raise", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "kill_embedded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "parameter_s"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.embed.EmbeddedMagics.kill_embedded", "name": "kill_embedded", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.terminal.embed.EmbeddedMagics.kill_embedded", "name": "kill_embedded", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.embed.EmbeddedMagics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.embed.EmbeddedMagics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InteractiveShell": {".class": "SymbolTableNode", "cross_ref": "IPython.core.interactiveshell.InteractiveShell", "kind": "Gdef"}, "InteractiveShellEmbed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.terminal.interactiveshell.TerminalInteractiveShell"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.embed.InteractiveShellEmbed", "name": "InteractiveShellEmbed", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.InteractiveShellEmbed", "has_param_spec_type": false, "metaclass_type": "traitlets.traitlets.MetaHasTraits", "metadata": {}, "module_name": "IPython.terminal.embed", "mro": ["IPython.terminal.embed.InteractiveShellEmbed", "IPython.terminal.interactiveshell.TerminalInteractiveShell", "IPython.core.interactiveshell.InteractiveShell", "traitlets.config.configurable.SingletonConfigurable", "traitlets.config.configurable.LoggingConfigurable", "traitlets.config.configurable.Configurable", "traitlets.traitlets.HasTraits", "traitlets.traitlets.HasDescriptors", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "header", "local_ns", "module", "dummy", "stack_depth", "compile_flags", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.__init__", "name": "__init__", "type": null}}, "_call_location_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed._call_location_id", "name": "_call_location_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_disable_init_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.InteractiveShellEmbed._disable_init_location", "name": "_disable_init_location", "type": null}}, "_inactive_locations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed._inactive_locations", "name": "_inactive_locations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_init_location_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed._init_location_id", "name": "_init_location_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "display_banner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.display_banner", "name": "display_banner", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CBool"}}}, "dummy_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.dummy_mode", "name": "dummy_mode", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}, "embedded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.embedded", "name": "embedded", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CBool"}}}, "embedded_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.embedded_active", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.embedded_active", "name": "embedded_active", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.embedded_active", "name": "embedded_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.embed.InteractiveShellEmbed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "embedded_active of InteractiveShellEmbed", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.embedded_active", "name": "embedded_active", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "embedded_active", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.embed.InteractiveShellEmbed"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "embedded_active", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "exit_msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.exit_msg", "name": "exit_msg", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Unicode"}}}, "init_magics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.init_magics", "name": "init_magics", "type": null}}, "init_sys_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.init_sys_modules", "name": "init_sys_modules", "type": null}}, "mainloop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "local_ns", "module", "stack_depth", "compile_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.mainloop", "name": "mainloop", "type": null}}, "old_banner2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.old_banner2", "name": "old_banner2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "should_raise": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.should_raise", "name": "should_raise", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "traitlets.traitlets.CBool"}}}, "term_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.embed.InteractiveShellEmbed.term_title", "name": "term_title", "type": {".class": "Instance", "args": ["builtins.bool", {".class": "UnionType", "items": ["builtins.bool", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "traitlets.traitlets.Bool"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.embed.InteractiveShellEmbed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.embed.InteractiveShellEmbed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KillEmbedded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.embed.KillEmbedded", "name": "KillEmbedded", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.KillEmbedded", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.terminal.embed", "mro": ["IPython.terminal.embed.KillEmbedded", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.embed.KillEmbedded.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.embed.KillEmbedded", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KillEmbeded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.terminal.embed.KillEmbeded", "line": 28, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "IPython.terminal.embed.KillEmbedded"}}, "Magics": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.Magics", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TerminalInteractiveShell": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.interactiveshell.TerminalInteractiveShell", "kind": "Gdef"}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "traitlets.traitlets.Unicode", "kind": "Gdef"}, "_Sentinel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.embed._Sentinel", "name": "_Sentinel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed._Sentinel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.terminal.embed", "mro": ["IPython.terminal.embed._Sentinel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed._Sentinel.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed._Sentinel.__repr__", "name": "__repr__", "type": null}}, "repr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.embed._Sentinel.repr", "name": "repr", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.embed._Sentinel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.embed._Sentinel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.embed.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.embed.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.embed.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.embed.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.embed.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.embed.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "ask_yes_no": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.io.ask_yes_no", "kind": "Gdef"}, "compilerop": {".class": "SymbolTableNode", "cross_ref": "IPython.core.compilerop", "kind": "Gdef"}, "embed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 4], "arg_names": ["header", "compile_flags", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.embed.embed", "name": "embed", "type": null}}, "line_magic": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.line_magic", "kind": "Gdef"}, "load_default_config": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.ipapp.load_default_config", "kind": "Gdef"}, "magic_arguments": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic_arguments", "kind": "Gdef"}, "magics_class": {".class": "SymbolTableNode", "cross_ref": "IPython.core.magic.magics_class", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "ultratb": {".class": "SymbolTableNode", "cross_ref": "IPython.core.ultratb", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\terminal\\embed.py"}