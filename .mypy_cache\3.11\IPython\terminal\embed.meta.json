{"data_mtime": 1753781401, "dep_lines": [12, 12, 13, 14, 15, 16, 17, 20, 12, 9, 10, 19, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 20, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.ultratb", "IPython.core.compilerop", "IPython.core.magic_arguments", "IPython.core.magic", "IPython.core.interactiveshell", "IPython.terminal.interactiveshell", "IPython.terminal.ipapp", "IPython.utils.io", "IPython.core", "sys", "warnings", "traitlets", "typing", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "IPython.utils", "IPython.utils.colorable", "_frozen_importlib", "_typeshed", "abc", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing_extensions"], "hash": "9969123d6a75fee97742c0086e3f4524f18d4163", "id": "IPython.terminal.embed", "ignore_all": true, "interface_hash": "6f1ac9a927199ce036c84452f39498f2f1ebfea4", "mtime": 1740598148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\terminal\\embed.py", "plugin_data": null, "size": 16042, "suppressed": [], "version_id": "1.15.0"}