{".class": "MypyFile", "_fullname": "IPython.terminal.shortcuts", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALT_AND_COMOBO_CONTROL_BINDINGS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.ALT_AND_COMOBO_CONTROL_BINDINGS", "name": "ALT_AND_COMOBO_CONTROL_BINDINGS", "type": {".class": "Instance", "args": ["IPython.terminal.shortcuts.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "AUTO_MATCH_BINDINGS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.AUTO_MATCH_BINDINGS", "name": "AUTO_MATCH_BINDINGS", "type": {".class": "Instance", "args": ["IPython.terminal.shortcuts.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "AUTO_SUGGEST_BINDINGS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.AUTO_SUGGEST_BINDINGS", "name": "AUTO_SUGGEST_BINDINGS", "type": {".class": "Instance", "args": ["IPython.terminal.shortcuts.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BaseBinding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.shortcuts.BaseBinding", "name": "BaseBinding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.BaseBinding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "keys", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "IPython.terminal.shortcuts", "mro": ["IPython.terminal.shortcuts.BaseBinding", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "IPython.terminal.shortcuts.BaseBinding.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "command", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.BaseBinding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "command", "keys"], "arg_types": ["IPython.terminal.shortcuts.BaseBinding", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseBinding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "IPython.terminal.shortcuts.BaseBinding.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "command"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["command", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.terminal.shortcuts.BaseBinding.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["command", "keys"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>B<PERSON>ing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "IPython.terminal.shortcuts.BaseBinding.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["command", "keys"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>B<PERSON>ing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_settable_property", "is_ready"], "fullname": "IPython.terminal.shortcuts.BaseBinding.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.terminal.shortcuts.BaseBinding.keys", "name": "keys", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.shortcuts.BaseBinding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.shortcuts.BaseBinding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Binding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.terminal.shortcuts.BaseBinding"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.shortcuts.Binding", "name": "Binding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.Binding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "keys", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "condition", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "IPython.terminal.shortcuts", "mro": ["IPython.terminal.shortcuts.Binding", "IPython.terminal.shortcuts.BaseBinding", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "IPython.terminal.shortcuts.Binding.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "command", "keys", "condition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.Binding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "command", "keys", "condition"], "arg_types": ["IPython.terminal.shortcuts.Binding", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Binding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "IPython.terminal.shortcuts.Binding.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "command"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "condition"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.Binding.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.shortcuts.Binding"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of Binding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["command", "keys", "condition"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.terminal.shortcuts.Binding.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["command", "keys", "condition"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Binding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "IPython.terminal.shortcuts.Binding.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["command", "keys", "condition"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of Binding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.Binding.__post_init__", "name": "__post_init__", "type": null}}, "command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_settable_property", "is_ready"], "fullname": "IPython.terminal.shortcuts.Binding.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.Binding.condition", "name": "condition", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.shortcuts.Binding.filter", "name": "filter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.shortcuts.Binding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.shortcuts.Binding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClipboardEmpty": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.clipboard.ClipboardEmpty", "kind": "Gdef", "module_public": false}, "Condition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Condition", "kind": "Gdef", "module_public": false}, "DEFAULT_BUFFER": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.DEFAULT_BUFFER", "kind": "Gdef", "module_public": false}, "InputMode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.vi_state.InputMode", "kind": "Gdef", "module_public": false}, "KEY_BINDINGS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.KEY_BINDINGS", "name": "KEY_BINDINGS", "type": {".class": "Instance", "args": ["IPython.terminal.shortcuts.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "KeyBindings": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "kind": "Gdef", "module_public": false}, "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "RuntimeBinding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.terminal.shortcuts.BaseBinding"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.shortcuts.RuntimeBinding", "name": "RuntimeBinding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.RuntimeBinding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 40, "name": "keys", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "filter", "type": "prompt_toolkit.filters.base.Condition"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "IPython.terminal.shortcuts", "mro": ["IPython.terminal.shortcuts.RuntimeBinding", "IPython.terminal.shortcuts.BaseBinding", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "IPython.terminal.shortcuts.RuntimeBinding.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "command", "keys", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.RuntimeBinding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "command", "keys", "filter"], "arg_types": ["IPython.terminal.shortcuts.RuntimeBinding", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "prompt_toolkit.filters.base.Condition"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RuntimeBinding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "IPython.terminal.shortcuts.RuntimeBinding.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "command"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "filter"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["command", "keys", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "IPython.terminal.shortcuts.RuntimeBinding.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["command", "keys", "filter"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "prompt_toolkit.filters.base.Condition"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of RuntimeBinding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "IPython.terminal.shortcuts.RuntimeBinding.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["command", "keys", "filter"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "prompt_toolkit.filters.base.Condition"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of RuntimeBinding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "command": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_settable_property", "is_ready"], "fullname": "IPython.terminal.shortcuts.RuntimeBinding.command", "name": "command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.terminal.shortcuts.RuntimeBinding.filter", "name": "filter", "type": "prompt_toolkit.filters.base.Condition"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.shortcuts.RuntimeBinding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.shortcuts.RuntimeBinding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SIMPLE_CONTROL_BINDINGS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.SIMPLE_CONTROL_BINDINGS", "name": "SIMPLE_CONTROL_BINDINGS", "type": {".class": "Instance", "args": ["IPython.terminal.shortcuts.Binding"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "TryNext": {".class": "SymbolTableNode", "cross_ref": "IPython.core.error.TryNext", "kind": "Gdef", "module_public": false}, "UNASSIGNED_ALLOWED_COMMANDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.UNASSIGNED_ALLOWED_COMMANDS", "name": "UNASSIGNED_ALLOWED_COMMANDS", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ViState": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.vi_state.ViState", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_binding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["bindings", "binding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.add_binding", "name": "add_binding", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["bindings", "binding"], "arg_types": ["prompt_toolkit.key_binding.key_bindings.KeyBindings", "IPython.terminal.shortcuts.Binding"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_binding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto_suggest": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.auto_suggest", "kind": "Gdef", "module_public": false}, "create_identifier": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.create_identifier", "name": "create_identifier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["handler"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_identifier", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_ipython_shortcuts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["shell", "skip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.create_ipython_shortcuts", "name": "create_ipython_shortcuts", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["shell", "skip"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_ipython_shortcuts", "ret_type": "prompt_toolkit.key_binding.key_bindings.KeyBindings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "dismiss_completion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.dismiss_completion", "name": "dismiss_completion", "type": null}}, "display_completions_like_readline": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.completion.display_completions_like_readline", "kind": "Gdef", "module_public": false}, "filter_from_string": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.filters.filter_from_string", "kind": "Gdef", "module_public": false}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "get_ipython": {".class": "SymbolTableNode", "cross_ref": "IPython.core.getipython.get_ipython", "kind": "Gdef", "module_public": false}, "handle_return_or_newline_or_execute": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.handle_return_or_newline_or_execute", "name": "handle_return_or_newline_or_execute", "type": null}}, "indent_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.indent_buffer", "name": "indent_buffer", "type": null}}, "match": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.auto_match", "kind": "Gdef", "module_public": false}, "nc": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.named_commands", "kind": "Gdef", "module_public": false}, "newline_autoindent": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.newline_autoindent", "name": "newline_autoindent", "type": null}}, "newline_or_execute_outer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["shell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.newline_or_execute_outer", "name": "newline_or_execute_outer", "type": null}}, "next_history_or_next_completion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.next_history_or_next_completion", "name": "next_history_or_next_completion", "type": null}}, "open_input_in_editor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.open_input_in_editor", "name": "open_input_in_editor", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "previous_history_or_previous_completion": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.previous_history_or_previous_completion", "name": "previous_history_or_previous_completion", "type": null}}, "quit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.quit", "name": "quit", "type": null}}, "reformat_and_execute": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.reformat_and_execute", "name": "reformat_and_execute", "type": null}}, "reformat_text_before_cursor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["buffer", "document", "shell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.reformat_text_before_cursor", "name": "reformat_text_before_cursor", "type": null}}, "reset_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.reset_buffer", "name": "reset_buffer", "type": null}}, "reset_search_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.reset_search_buffer", "name": "reset_search_buffer", "type": null}}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef", "module_public": false}, "suspend_to_bg": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.suspend_to_bg", "name": "suspend_to_bg", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "tkinter_clipboard_get": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.clipboard.tkinter_clipboard_get", "kind": "Gdef", "module_public": false}, "undoc": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.decorators.undoc", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "win32_clipboard_get": {".class": "SymbolTableNode", "cross_ref": "IPython.lib.clipboard.win32_clipboard_get", "kind": "Gdef", "module_public": false}, "win_paste": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.win_paste", "name": "win_paste", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.win_paste", "name": "win_paste", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py"}