{".class": "MypyFile", "_fullname": "IPython.terminal.shortcuts.auto_suggest", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AppendAutoSuggestionInAnyLine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.layout.processors.Processor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine", "name": "AppendAutoSuggestionInAnyLine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.terminal.shortcuts.auto_suggest", "mro": ["IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine", "prompt_toolkit.layout.processors.Processor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "style"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "style"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AppendAutoSuggestionInAnyLine", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine._debug", "name": "_debug", "type": "builtins.bool"}}, "apply_transformation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine.apply_transformation", "name": "apply_transformation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ti"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine", "prompt_toolkit.layout.processors.TransformationInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_transformation of AppendAutoSuggestionInAnyLine", "ret_type": "prompt_toolkit.layout.processors.Transformation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine.style", "name": "style", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.shortcuts.auto_suggest.AppendAutoSuggestionInAnyLine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoSuggest": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.auto_suggest.AutoSuggest", "kind": "Gdef"}, "AutoSuggestFromHistory": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "kind": "Gdef"}, "Buffer": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.buffer.Buffer", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.document.Document", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "History": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.history.History", "kind": "Gdef"}, "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NavigableAutoSuggestFromHistory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.auto_suggest.AutoSuggestFromHistory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "name": "NavigableAutoSuggestFromHistory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.terminal.shortcuts.auto_suggest", "mro": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "prompt_toolkit.auto_suggest.AutoSuggest", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.__init__", "name": "__init__", "type": null}}, "_cancel_running_llm_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._cancel_running_llm_task", "name": "_cancel_running_llm_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cancel_running_llm_task of NavigableAutoSuggestFromHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connected_apps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._connected_apps", "name": "_connected_apps", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_dismiss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "buffer", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._dismiss", "name": "_dismiss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "buffer", "args", "kwargs"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dismiss of NavigableAutoSuggestFromHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "text", "skip_lines", "history", "previous"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._find_match", "name": "_find_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "text", "skip_lines", "history", "previous"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "builtins.str", "builtins.float", "prompt_toolkit.history.History", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_match of NavigableAutoSuggestFromHistory", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_next_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "text", "skip_lines", "history"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._find_next_match", "name": "_find_next_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "text", "skip_lines", "history"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "builtins.str", "builtins.float", "prompt_toolkit.history.History"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_next_match of NavigableAutoSuggestFromHistory", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_previous_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "text", "skip_lines", "history"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._find_previous_match", "name": "_find_previous_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "text", "skip_lines", "history"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "builtins.str", "builtins.float", "prompt_toolkit.history.History"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_previous_match of NavigableAutoSuggestFromHistory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_llm_provider": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._llm_provider", "name": "_llm_provider", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_llm_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._llm_task", "name": "_llm_task", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "_asyncio.Task"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_trigger_llm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._trigger_llm", "name": "_trigger_llm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_trigger_llm of NavigableAutoSuggestFromHistory", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_trigger_llm_core": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory._trigger_llm_core", "name": "_trigger_llm_core", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buffer"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "prompt_toolkit.buffer.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_trigger_llm_core of NavigableAutoSuggestFromHistory", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pt_app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pt_app"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "prompt_toolkit.shortcuts.prompt.PromptSession"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of NavigableAutoSuggestFromHistory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.disconnect", "name": "disconnect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disconnect of NavigableAutoSuggestFromHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "other_than", "history"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.down", "name": "down", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "other_than", "history"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "builtins.str", "builtins.str", "prompt_toolkit.history.History"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "down of NavigableAutoSuggestFromHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.get_suggestion", "name": "get_suggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "buffer", "document"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "prompt_toolkit.buffer.Buffer", "prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_suggestion of NavigableAutoSuggestFromHistory", "ret_type": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.Suggestion", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_history_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.reset_history_position", "name": "reset_history_position", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "prompt_toolkit.buffer.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_history_position of NavigableAutoSuggestFromHistory", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.skip_lines", "name": "skip_lines", "type": "builtins.int"}}, "up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "other_than", "history"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.up", "name": "up", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "other_than", "history"], "arg_types": ["IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "builtins.str", "builtins.str", "prompt_toolkit.history.History"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "up of NavigableAutoSuggestFromHistory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Processor": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.Processor", "kind": "Gdef"}, "PromptSession": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.shortcuts.prompt.PromptSession", "kind": "Gdef"}, "Provider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "IPython.terminal.shortcuts.auto_suggest.Provider", "line": 583, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["prompt_toolkit.auto_suggest.AutoSuggestFromHistory", "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef"}, "Suggestion": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.auto_suggest.Suggestion", "kind": "Gdef"}, "Transformation": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.Transformation", "kind": "Gdef"}, "TransformationInput": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.processors.TransformationInput", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_MIN_LINES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.auto_suggest._MIN_LINES", "name": "_MIN_LINES", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.__getattr__", "name": "__getattr__", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.auto_suggest.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_deprected_accept_in_vi_insert_mode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest._deprected_accept_in_vi_insert_mode", "name": "_deprected_accept_in_vi_insert_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deprected_accept_in_vi_insert_mode", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_query": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest._get_query", "name": "_get_query", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["document"], "arg_types": ["prompt_toolkit.document.Document"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_query", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_swap_autosuggestion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["buffer", "provider", "direction_method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest._swap_autosuggestion", "name": "_swap_autosuggestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["buffer", "provider", "direction_method"], "arg_types": ["prompt_toolkit.buffer.Buffer", "IPython.terminal.shortcuts.auto_suggest.NavigableAutoSuggestFromHistory", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_swap_autosuggestion", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_hint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buffer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest._update_hint", "name": "_update_hint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buffer"], "arg_types": ["prompt_toolkit.buffer.Buffer"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_hint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.accept", "name": "accept", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept_and_keep_cursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.accept_and_keep_cursor", "name": "accept_and_keep_cursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept_and_keep_cursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept_and_move_cursor_left": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.accept_and_move_cursor_left", "name": "accept_and_move_cursor_left", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept_and_move_cursor_left", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept_character": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.accept_character", "name": "accept_character", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept_character", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept_or_jump_to_end": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.accept_or_jump_to_end", "name": "accept_or_jump_to_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept_or_jump_to_end", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept_token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.accept_token", "name": "accept_token", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept_token", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accept_word": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.accept_word", "name": "accept_word", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "accept_word", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "backspace_and_resume_hint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.backspace_and_resume_hint", "name": "backspace_and_resume_hint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backspace_and_resume_hint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "discard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.discard", "name": "discard", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "discard", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "down_and_update_hint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.down_and_update_hint", "name": "down_and_update_hint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "down_and_update_hint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_tokens": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.tokenutil.generate_tokens", "kind": "Gdef"}, "get_ipython": {".class": "SymbolTableNode", "cross_ref": "IPython.core.getipython.get_ipython", "kind": "Gdef"}, "jai_models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.auto_suggest.jai_models", "name": "jai_models", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.shortcuts.auto_suggest.jai_models", "source_any": null, "type_of_any": 3}}}, "jupyter_ai_magics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.auto_suggest.jupyter_ai_magics", "name": "jupyter_ai_magics", "type": {".class": "AnyType", "missing_import_name": "IPython.terminal.shortcuts.auto_suggest.jupyter_ai_magics", "source_any": null, "type_of_any": 3}}}, "llm_autosuggestion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "IPython.terminal.shortcuts.auto_suggest.llm_autosuggestion", "name": "llm_autosuggestion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "llm_autosuggestion", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nc": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.bindings.named_commands", "kind": "Gdef"}, "pass_through": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.filters.pass_through", "kind": "Gdef"}, "prompt_toolkit": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "resume_hinting": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.resume_hinting", "name": "resume_hinting", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resume_hinting", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "swap_autosuggestion_down": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.swap_autosuggestion_down", "name": "swap_autosuggestion_down", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swap_autosuggestion_down", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "swap_autosuggestion_up": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.swap_autosuggestion_up", "name": "swap_autosuggestion_up", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "swap_autosuggestion_up", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tokenize": {".class": "SymbolTableNode", "cross_ref": "tokenize", "kind": "Gdef"}, "up_and_update_hint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.auto_suggest.up_and_update_hint", "name": "up_and_update_hint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["event"], "arg_types": ["prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "up_and_update_hint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py"}