{"data_mtime": 1753781401, "dep_lines": [11, 25, 11, 16, 22, 23, 9, 10, 12, 13, 14, 15, 1, 2, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29, 29, 28, 29], "dep_prios": [10, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 10, 20], "dependencies": ["prompt_toolkit.key_binding.bindings.named_commands", "IPython.terminal.shortcuts.filters", "prompt_toolkit.key_binding.bindings", "prompt_toolkit.layout.processors", "IPython.core.getipython", "IPython.utils.tokenutil", "prompt_toolkit.buffer", "prompt_toolkit.key_binding", "prompt_toolkit.auto_suggest", "prompt_toolkit.document", "prompt_toolkit.history", "prompt_toolkit.shortcuts", "re", "asyncio", "tokenize", "io", "typing", "warnings", "prompt_toolkit", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "types", "traceback", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "prompt_toolkit.key_binding.key_processor", "prompt_toolkit.layout", "prompt_toolkit.shortcuts.prompt", "prompt_toolkit.utils", "typing_extensions"], "hash": "a18c2ac6f8c7901080de49b3c6fa2acefa2e9d98", "id": "IPython.terminal.shortcuts.auto_suggest", "ignore_all": true, "interface_hash": "928dd4426175fc9439511c45829b8f00a3536f34", "mtime": 1740598149, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py", "plugin_data": null, "size": 23018, "suppressed": ["jupyter_ai.completions.models", "jupyter_ai.completions", "jupyter_ai_magics", "jupyter_ai"], "version_id": "1.15.0"}