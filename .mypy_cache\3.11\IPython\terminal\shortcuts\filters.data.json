{".class": "MypyFile", "_fullname": "IPython.terminal.shortcuts.filters", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Always": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Always", "kind": "Gdef", "module_public": false}, "BINARY_OP_DUNDERS": {".class": "SymbolTableNode", "cross_ref": "IPython.core.guarded_eval.BINARY_OP_DUNDERS", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Condition": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Condition", "kind": "Gdef", "module_public": false}, "DEFAULT_BUFFER": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.DEFAULT_BUFFER", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Filter": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Filter", "kind": "Gdef", "module_public": false}, "FocusableElement": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.layout.layout.FocusableElement", "kind": "Gdef", "module_public": false}, "KEYBINDING_FILTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.filters.KEYBINDING_FILTERS", "name": "KEYBINDING_FILTERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "KeyPressEvent": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.key_binding.key_processor.KeyPressEvent", "kind": "Gdef", "module_public": false}, "Never": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.base.Never", "kind": "Gdef", "module_public": false}, "PassThrough": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["prompt_toolkit.filters.base.Filter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.terminal.shortcuts.filters.PassThrough", "name": "PassThrough", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.PassThrough", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "IPython.terminal.shortcuts.filters", "mro": ["IPython.terminal.shortcuts.filters.PassThrough", "prompt_toolkit.filters.base.Filter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.PassThrough.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.PassThrough.__init__", "name": "__init__", "type": null}}, "_is_replying": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.PassThrough._is_replying", "name": "_is_replying", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.PassThrough.reply", "name": "reply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["IPython.terminal.shortcuts.filters.PassThrough", "prompt_toolkit.key_binding.key_processor.KeyPressEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reply of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.terminal.shortcuts.filters.PassThrough.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.terminal.shortcuts.filters.PassThrough", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SEARCH_BUFFER": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.enums.SEARCH_BUFFER", "kind": "Gdef", "module_public": false}, "UNARY_OP_DUNDERS": {".class": "SymbolTableNode", "cross_ref": "IPython.core.guarded_eval.UNARY_OP_DUNDERS", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.filters.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.filters.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.filters.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.filters.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.filters.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.filters.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.terminal.shortcuts.filters.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_find_dunder": {".class": "SymbolTableNode", "cross_ref": "IPython.core.guarded_eval._find_dunder", "kind": "Gdef", "module_public": false}, "_following_text_cache": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.filters._following_text_cache", "name": "_following_text_cache", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "prompt_toolkit.filters.base.Condition"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_preceding_text_cache": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.filters._preceding_text_cache", "name": "_preceding_text_cache", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "prompt_toolkit.filters.base.Condition"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "all_quotes_paired": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["quote", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.all_quotes_paired", "name": "all_quotes_paired", "type": null}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef", "module_public": false}, "auto_match": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.auto_match", "name": "auto_match", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.auto_match", "name": "auto_match", "type": "prompt_toolkit.filters.base.Condition"}}}, "auto_suggest": {".class": "SymbolTableNode", "cross_ref": "IPython.terminal.shortcuts.auto_suggest", "kind": "Gdef", "module_public": false}, "cursor_in_leading_ws": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.cursor_in_leading_ws", "name": "cursor_in_leading_ws", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.cursor_in_leading_ws", "name": "cursor_in_leading_ws", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "default_buffer_focused": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.filters.default_buffer_focused", "name": "default_buffer_focused", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ebivim": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.ebivim", "name": "ebivim", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.ebivim", "name": "ebivim", "type": "prompt_toolkit.filters.base.Condition"}}}, "emacs_insert_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.emacs_insert_mode", "kind": "Gdef", "module_public": false}, "eval_node": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.eval_node", "name": "eval_node", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["node"], "arg_types": [{".class": "UnionType", "items": ["ast.AST", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eval_node", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_from_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.filter_from_string", "name": "filter_from_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["code"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_from_string", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "following_text": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.following_text", "name": "following_text", "type": null}}, "get_app": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.application.current.get_app", "kind": "Gdef", "module_public": false}, "get_ipython": {".class": "SymbolTableNode", "cross_ref": "IPython.core.getipython.get_ipython", "kind": "Gdef", "module_public": false}, "has_completions": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_completions", "kind": "Gdef", "module_public": false}, "has_focus": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.has_focus", "name": "has_focus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "prompt_toolkit.layout.layout.FocusableElement"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_focus", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_focus_impl": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_focus", "kind": "Gdef", "module_public": false}, "has_line_above": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.has_line_above", "name": "has_line_above", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_line_above", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.has_line_above", "name": "has_line_above", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "has_line_below": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.has_line_below", "name": "has_line_below", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_line_below", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.has_line_below", "name": "has_line_below", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "has_selection": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_selection", "kind": "Gdef", "module_public": false}, "has_suggestion": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.has_suggestion", "kind": "Gdef", "module_public": false}, "is_cursor_at_the_end_of_line": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.is_cursor_at_the_end_of_line", "name": "is_cursor_at_the_end_of_line", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_cursor_at_the_end_of_line", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.is_cursor_at_the_end_of_line", "name": "is_cursor_at_the_end_of_line", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "is_windows_os": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.is_windows_os", "name": "is_windows_os", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.is_windows_os", "name": "is_windows_os", "type": "prompt_toolkit.filters.base.Condition"}}}, "navigable_suggestions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.navigable_suggestions", "name": "navigable_suggestions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.navigable_suggestions", "name": "navigable_suggestions", "type": "prompt_toolkit.filters.base.Condition"}}}, "not_inside_unclosed_string": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.not_inside_unclosed_string", "name": "not_inside_unclosed_string", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.not_inside_unclosed_string", "name": "not_inside_unclosed_string", "type": "prompt_toolkit.filters.base.Condition"}}}, "pass_through": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.terminal.shortcuts.filters.pass_through", "name": "pass_through", "type": "IPython.terminal.shortcuts.filters.PassThrough"}}, "preceding_text": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.terminal.shortcuts.filters.preceding_text", "name": "preceding_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pattern"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preceding_text", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "readline_like_completions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.readline_like_completions", "name": "readline_like_completions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.readline_like_completions", "name": "readline_like_completions", "type": "prompt_toolkit.filters.base.Condition"}}}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef", "module_public": false}, "supports_suspend": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.terminal.shortcuts.filters.supports_suspend", "name": "supports_suspend", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "IPython.terminal.shortcuts.filters.supports_suspend", "name": "supports_suspend", "type": "prompt_toolkit.filters.base.Condition"}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "undoc": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.decorators.undoc", "kind": "Gdef", "module_public": false}, "vi_insert_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.vi_insert_mode", "kind": "Gdef", "module_public": false}, "vi_mode": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.filters.app.vi_mode", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py"}