{".class": "MypyFile", "_fullname": "_pytest._code", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Code": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.Code", "kind": "Gdef"}, "ExceptionInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionInfo", "kind": "Gdef"}, "Frame": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.Frame", "kind": "Gdef"}, "Source": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.source.Source", "kind": "Gdef"}, "Traceback": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.Traceback", "kind": "Gdef"}, "TracebackEntry": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.TracebackEntry", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest._code.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest._code.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest._code.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest._code.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest._code.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest._code.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest._code.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest._code.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "filter_traceback": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.filter_traceback", "kind": "Gdef"}, "getfslineno": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.getfslineno", "kind": "Gdef"}, "getrawcode": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.source.getrawcode", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pytest\\_code\\__init__.py"}