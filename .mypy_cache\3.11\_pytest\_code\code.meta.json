{"data_mtime": 1753783515, "dep_lines": [41, 46, 45, 48, 49, 50, 2, 4, 5, 6, 9, 10, 11, 12, 13, 14, 16, 19, 38, 40, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 10, 10, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.source", "_pytest._io.saferepr", "_pytest._io", "_pytest.compat", "_pytest.deprecated", "_pytest.pathlib", "__future__", "ast", "dataclasses", "inspect", "io", "os", "pathlib", "re", "sys", "traceback", "types", "typing", "pluggy", "_pytest", "builtins", "_frozen_importlib", "_pytest._io.terminalwriter", "_typeshed", "abc", "enum", "typing_extensions"], "hash": "9989c3789e4b0dfe8dacd25221b3fd9755a14224", "id": "_pytest._code.code", "ignore_all": true, "interface_hash": "7783c113a3ba7e8f4eb8e29416abedb23391c7fb", "mtime": 1746804122, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pytest\\_code\\code.py", "plugin_data": null, "size": 50133, "suppressed": [], "version_id": "1.15.0"}