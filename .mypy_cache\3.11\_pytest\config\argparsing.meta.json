{"data_mtime": 1753783514, "dep_lines": [19, 18, 20, 112, 2, 4, 5, 6, 7, 8, 18, 546, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 5, 10, 5, 10, 10, 5, 20, 20, 5, 20, 30, 30, 30, 30], "dependencies": ["_pytest.config.exceptions", "_pytest._io", "_pytest.deprecated", "_pytest._argcomplete", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "gettext", "os", "sys", "typing", "_pytest", "textwrap", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "1451e8716a746de901dbde160759885d96f6e68c", "id": "_pytest.config.argparsing", "ignore_all": true, "interface_hash": "e31ea8a78bb8e30f8beecfc686c87f0cad70ad68", "mtime": 1746804122, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pytest\\config\\argparsing.py", "plugin_data": null, "size": 20562, "suppressed": [], "version_id": "1.15.0"}