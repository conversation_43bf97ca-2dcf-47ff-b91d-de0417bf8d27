{"data_mtime": 1753783514, "dep_lines": [11, 13, 1, 3, 4, 5, 7, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 10, 10, 5, 20, 30, 30, 30, 30], "dependencies": ["_pytest.compat", "_pytest.deprecated", "__future__", "functools", "pathlib", "typing", "warnings", "pluggy", "builtins", "dataclasses", "_frozen_importlib", "abc", "os", "pluggy._hooks"], "hash": "1e5e2cb0360c3a42da0d5325fd6f904687f845a8", "id": "_pytest.config.compat", "ignore_all": true, "interface_hash": "474c8084f2ad21720f70a9c4af58e1822a148062", "mtime": 1746804122, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pytest\\config\\compat.py", "plugin_data": null, "size": 2938, "suppressed": [], "version_id": "1.15.0"}