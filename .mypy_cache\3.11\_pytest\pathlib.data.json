{".class": "MypyFile", "_fullname": "_pytest.pathlib", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CouldNotResolvePathError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pathlib.CouldNotResolvePathError", "name": "CouldNotResolvePathError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.CouldNotResolvePathError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pathlib", "mro": ["_pytest.pathlib.CouldNotResolvePathError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pathlib.CouldNotResolvePathError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pathlib.CouldNotResolvePathError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EBADF": {".class": "SymbolTableNode", "cross_ref": "errno.EBADF", "kind": "Gdef"}, "ELOOP": {".class": "SymbolTableNode", "cross_ref": "errno.ELOOP", "kind": "Gdef"}, "ENOENT": {".class": "SymbolTableNode", "cross_ref": "errno.ENOENT", "kind": "Gdef"}, "ENOTDIR": {".class": "SymbolTableNode", "cross_ref": "errno.ENOTDIR", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "ImportMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pathlib.ImportMode", "name": "ImportMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "_pytest.pathlib.ImportMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "_pytest.pathlib", "mro": ["_pytest.pathlib.ImportMode", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.pathlib.ImportMode.append", "name": "append", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "append"}, "type_ref": "builtins.str"}}}, "importlib": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.pathlib.ImportMode.importlib", "name": "importlib", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "importlib"}, "type_ref": "builtins.str"}}}, "prepend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.pathlib.ImportMode.prepend", "name": "prepend", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "prepend"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pathlib.ImportMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pathlib.ImportMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImportPathMismatchError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ImportError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.pathlib.ImportPathMismatchError", "name": "ImportPathMismatchError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.ImportPathMismatchError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.pathlib", "mro": ["_pytest.pathlib.ImportPathMismatchError", "builtins.ImportError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pathlib.ImportPathMismatchError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.pathlib.ImportPathMismatchError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LOCK_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.pathlib.LOCK_TIMEOUT", "name": "LOCK_TIMEOUT", "type": "builtins.int"}}, "ModuleSpec": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib.ModuleSpec", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "NamespaceLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.NamespaceLoader", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PathFinder": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.PathFinder", "kind": "Gdef"}, "PurePath": {".class": "SymbolTableNode", "cross_ref": "pathlib.PurePath", "kind": "Gdef"}, "PytestWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestWarning", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_AnyPurePath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pathlib._AnyPurePath", "name": "_AnyPurePath", "upper_bound": "pathlib.PurePath", "values": [], "variance": 0}}, "_IGNORED_ERRORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.pathlib._IGNORED_ERRORS", "name": "_IGNORED_ERRORS", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_IGNORED_WINERRORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.pathlib._IGNORED_WINERRORS", "name": "_IGNORED_WINERRORS", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pathlib.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pathlib.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pathlib.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pathlib.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pathlib.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.pathlib.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_force_symlink": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["root", "target", "link_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib._force_symlink", "name": "_force_symlink", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["root", "target", "link_to"], "arg_types": ["pathlib.Path", {".class": "UnionType", "items": ["builtins.str", "pathlib.PurePath"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_force_symlink", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ignore_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib._ignore_error", "name": "_ignore_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["exception"], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ignore_error", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_import_module_using_spec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["module_name", "module_path", "module_location", "insert_modules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib._import_module_using_spec", "name": "_import_module_using_spec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["module_name", "module_path", "module_location", "insert_modules"], "arg_types": ["builtins.str", "pathlib.Path", "pathlib.Path", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_import_module_using_spec", "ret_type": {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_same": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["f1", "f2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib._is_same", "name": "_is_same", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["f1", "f2"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_same", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "absolutepath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.absolutepath", "name": "absolutepath", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "absolutepath", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "_pytest.compat.assert_never", "kind": "Gdef"}, "atexit": {".class": "SymbolTableNode", "cross_ref": "atexit", "kind": "Gdef"}, "bestrelpath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["directory", "dest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.bestrelpath", "name": "bestrelpath", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["directory", "dest"], "arg_types": ["pathlib.Path", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bestrelpath", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cleanup_candidates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["root", "prefix", "keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.cleanup_candidates", "name": "cleanup_candidates", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["root", "prefix", "keep"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cleanup_candidates", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cleanup_dead_symlinks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.cleanup_dead_symlinks", "name": "cleanup_dead_symlinks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["root"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cleanup_dead_symlinks", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cleanup_numbered_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["root", "prefix", "keep", "consider_lock_dead_if_created_before"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.cleanup_numbered_dir", "name": "cleanup_numbered_dir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["root", "prefix", "keep", "consider_lock_dead_if_created_before"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.int", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cleanup_numbered_dir", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commonpath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path1", "path2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.commonpath", "name": "commonpath", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path1", "path2"], "arg_types": ["pathlib.Path", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commonpath", "ret_type": {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_module_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["root", "module_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.compute_module_name", "name": "compute_module_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["root", "module_path"], "arg_types": ["pathlib.Path", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_module_name", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "create_cleanup_lock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.create_cleanup_lock", "name": "create_cleanup_lock", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["p"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_cleanup_lock", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ensure_deletable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path", "consider_lock_dead_if_created_before"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.ensure_deletable", "name": "ensure_deletable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "consider_lock_dead_if_created_before"], "arg_types": ["pathlib.Path", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_deletable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ensure_extended_length_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.ensure_extended_length_path", "name": "ensure_extended_length_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_extended_length_path", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expanduser": {".class": "SymbolTableNode", "cross_ref": "posixpath.expanduser", "kind": "Gdef"}, "expandvars": {".class": "SymbolTableNode", "cross_ref": "posixpath.expandvars", "kind": "Gdef"}, "extract_suffixes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["iter", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.extract_suffixes", "name": "extract_suffixes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["iter", "prefix"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_suffixes", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_prefixed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["root", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.find_prefixed", "name": "find_prefixed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["root", "prefix"], "arg_types": ["pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_prefixed", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_suffixes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["root", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.find_suffixes", "name": "find_suffixes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["root", "prefix"], "arg_types": ["pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_suffixes", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch", "kind": "Gdef"}, "fnmatch_ex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pattern", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.fnmatch_ex", "name": "fnmatch_ex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pattern", "path"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fnmatch_ex", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_extended_length_path_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.get_extended_length_path_str", "name": "get_extended_length_path_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_extended_length_path_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_lock_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.get_lock_path", "name": "get_lock_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pathlib._AnyPurePath", "id": -1, "name": "_AnyPurePath", "namespace": "_pytest.pathlib.get_lock_path", "upper_bound": "pathlib.PurePath", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_lock_path", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pathlib._AnyPurePath", "id": -1, "name": "_AnyPurePath", "namespace": "_pytest.pathlib.get_lock_path", "upper_bound": "pathlib.PurePath", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.pathlib._AnyPurePath", "id": -1, "name": "_AnyPurePath", "namespace": "_pytest.pathlib.get_lock_path", "upper_bound": "pathlib.PurePath", "values": [], "variance": 0}]}}}, "import_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3], "arg_names": ["path", "mode", "root", "consider_namespace_packages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.import_path", "name": "import_path", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3], "arg_names": ["path", "mode", "root", "consider_namespace_packages"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "_pytest.pathlib.ImportMode"], "uses_pep604_syntax": true}, "pathlib.Path", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_path", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "insert_missing_modules": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["modules", "module_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.insert_missing_modules", "name": "insert_missing_modules", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["modules", "module_name"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "types.ModuleType"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_missing_modules", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_importable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module_name", "module_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.is_importable", "name": "is_importable", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module_name", "module_path"], "arg_types": ["builtins.str", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_importable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isabs": {".class": "SymbolTableNode", "cross_ref": "posixpath.isabs", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "make_numbered_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["root", "prefix", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.make_numbered_dir", "name": "make_numbered_dir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["root", "prefix", "mode"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_numbered_dir", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_numbered_dir_with_cleanup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["root", "prefix", "keep", "lock_timeout", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.make_numbered_dir_with_cleanup", "name": "make_numbered_dir_with_cleanup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["root", "prefix", "keep", "lock_timeout", "mode"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.int", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_numbered_dir_with_cleanup", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_delete_a_numbered_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.maybe_delete_a_numbered_dir", "name": "maybe_delete_a_numbered_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maybe_delete_a_numbered_dir", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_name_from_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path", "root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.module_name_from_path", "name": "module_name_from_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "root"], "arg_types": ["pathlib.Path", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module_name_from_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_rm_rf_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["func", "path", "excinfo", "start_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.on_rm_rf_error", "name": "on_rm_rf_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["func", "path", "excinfo", "start_path"], "arg_types": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.BaseException"}, "builtins.BaseException", {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_rm_rf_error", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_num": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["maybe_num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.parse_num", "name": "parse_num", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["maybe_num"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_num", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "parts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.parts", "name": "parts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parts", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "posix_sep": {".class": "SymbolTableNode", "cross_ref": "posixpath.sep", "kind": "Gdef"}, "register_cleanup_lock_removal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["lock_path", "register"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.register_cleanup_lock_removal", "name": "register_cleanup_lock_removal", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["lock_path", "register"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_cleanup_lock_removal", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_from_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["input", "rootpath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.resolve_from_str", "name": "resolve_from_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["input", "rootpath"], "arg_types": ["builtins.str", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_from_str", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_package_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.resolve_package_path", "name": "resolve_package_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_package_path", "ret_type": {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_pkg_root_and_module_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["path", "consider_namespace_packages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.resolve_pkg_root_and_module_name", "name": "resolve_pkg_root_and_module_name", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["path", "consider_namespace_packages"], "arg_types": ["pathlib.Path", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_pkg_root_and_module_name", "ret_type": {".class": "TupleType", "implicit": false, "items": ["pathlib.Path", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rm_rf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.rm_rf", "name": "rm_rf", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rm_rf", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "safe_exists": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.safe_exists", "name": "safe_exists", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["p"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "safe_exists", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scandir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["path", "sort_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.scandir", "name": "scandir", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["path", "sort_key"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scandir", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sep": {".class": "SymbolTableNode", "cross_ref": "posixpath.sep", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "skip": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.skip", "kind": "Gdef"}, "spec_matches_module_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module_spec", "module_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.spec_matches_module_path", "name": "spec_matches_module_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module_spec", "module_path"], "arg_types": [{".class": "UnionType", "items": ["_frozen_importlib.ModuleSpec", {".class": "NoneType"}], "uses_pep604_syntax": true}, "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "spec_matches_module_path", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "symlink_or_skip": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["src", "dst", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.symlink_or_skip", "name": "symlink_or_skip", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["src", "dst", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "symlink_or_skip", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "try_cleanup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path", "consider_lock_dead_if_created_before"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.try_cleanup", "name": "try_cleanup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "consider_lock_dead_if_created_before"], "arg_types": ["pathlib.Path", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "try_cleanup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}, "visit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["path", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.pathlib.visit", "name": "visit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["path", "recurse"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.<PERSON><PERSON><PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\_pytest\\pathlib.py"}