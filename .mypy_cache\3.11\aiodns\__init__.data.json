{".class": "MypyFile", "_fullname": "aiodns", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DNSResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiodns.DNSResolver", "name": "DNSResolver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiodns", "mro": ["aiodns.DNSResolver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "nameservers", "loop", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "nameservers", "loop", "kwargs"], "arg_types": ["aiodns.DNSResolver", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fut", "result", "errorno"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "aiodns.DNSResolver._callback", "name": "_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fut", "result", "errorno"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._callback", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._callback", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_callback of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._callback", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver._callback", "name": "_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fut", "result", "errorno"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._callback", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._callback", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_callback of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._callback", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_channel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiodns.DNSResolver._channel", "name": "_channel", "type": "pycares.Channel"}}, "_event_thread": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiodns.DNSResolver._event_thread", "name": "_event_thread", "type": "builtins.bool"}}, "_get_future_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver._get_future_callback", "name": "_get_future_callback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiodns.DNSResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_future_callback of DNSResolver", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._get_future_callback", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._get_future_callback", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "id": -1, "name": "_T", "namespace": "aiodns.DNSResolver._get_future_callback", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_handle_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver._handle_event", "name": "_handle_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "fd", "event"], "arg_types": ["aiodns.DNSResolver", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_event of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver._make_channel", "name": "_make_channel", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["aiodns.DNSResolver", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_channel of DNSResolver", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "pycares.Channel"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_fds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver._read_fds", "name": "_read_fds", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_sock_state_cb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fd", "readable", "writable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver._sock_state_cb", "name": "_sock_state_cb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fd", "readable", "writable"], "arg_types": ["aiodns.DNSResolver", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sock_state_cb of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_start_timer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver._start_timer", "name": "_start_timer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiodns.DNSResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_start_timer of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiodns.DNSResolver._timeout", "name": "_timeout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_timer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver._timer", "name": "_timer", "type": {".class": "UnionType", "items": ["asyncio.events.TimerHandle", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_timer_cb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver._timer_cb", "name": "_timer_cb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiodns.DNSResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_timer_cb of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_write_fds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver._write_fds", "name": "_write_fds", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver.cancel", "name": "cancel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiodns.DNSResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "host", "family", "port", "proto", "type", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "host", "family", "port", "proto", "type", "flags"], "arg_types": ["aiodns.DNSResolver", "builtins.str", "socket.AddressFamily", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getaddrinfo of DNSResolver", "ret_type": {".class": "Instance", "args": ["pycares.ares_addrinfo_result"], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyaddr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver.gethostbyaddr", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["aiodns.DNSResolver", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gethos<PERSON><PERSON><PERSON>dr of DNSResolver", "ret_type": {".class": "Instance", "args": ["pycares.ares_host_result"], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "family"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver.gethostbyname", "name": "gethostbyname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "family"], "arg_types": ["aiodns.DNSResolver", "builtins.str", "socket.AddressFamily"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gethostbyname of DNSResolver", "ret_type": {".class": "Instance", "args": ["pycares.ares_host_result"], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getnameinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "sockaddr", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver.getnameinfo", "name": "getnameinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sockaddr", "flags"], "arg_types": ["aiodns.DNSResolver", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getnameinfo of DNSResolver", "ret_type": {".class": "Instance", "args": ["pycares.ares_nameinfo_result"], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aiodns.DNSResolver.loop", "name": "loop", "type": "asyncio.events.AbstractEventLoop"}}, "nameservers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "aiodns.DNSResolver.nameservers", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.nameservers", "name": "nameservers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiodns.DNSResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nameservers of DNSResolver", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.nameservers", "name": "nameservers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiodns.DNSResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nameservers of DNSResolver", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aiodns.DNSResolver.nameservers", "name": "nameservers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["aiodns.DNSResolver", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nameservers of DNSResolver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "nameservers", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aiodns.DNSResolver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nameservers of DNSResolver", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "aiodns.DNSResolver.query", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "A"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_a_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "A"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_a_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "AAAA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_aaaa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "AAAA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_aaaa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "CAA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_caa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "CAA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_caa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "CNAME"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_cname_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "CNAME"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_cname_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "MX"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_mx_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "MX"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_mx_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "NAPTR"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_naptr_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "NAPTR"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_naptr_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "NS"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_ns_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "NS"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_ns_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "PTR"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_ptr_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "PTR"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_ptr_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "SOA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_soa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "SOA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_soa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "SRV"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_srv_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "SRV"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_srv_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "TXT"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_txt_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "aiodns.DNSResolver.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "TXT"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_txt_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "A"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_a_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "AAAA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_aaaa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "CAA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_caa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "CNAME"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_cname_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "MX"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_mx_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "NAPTR"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_naptr_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "NS"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_ns_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "PTR"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_ptr_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "SOA"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_soa_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "SRV"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_srv_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "host", "qtype", "qclass"], "arg_types": ["aiodns.DNSResolver", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "TXT"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of DNSResolver", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["pycares.ares_query_txt_result"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "_asyncio.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns.DNSResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiodns.DNSResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "READ": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiodns.READ", "name": "READ", "type": "builtins.int"}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WINDOWS_SELECTOR_ERR_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiodns.WINDOWS_SELECTOR_ERR_MSG", "name": "WINDOWS_SELECTOR_ERR_MSG", "type": "builtins.str"}}, "WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiodns.WRITE", "name": "WRITE", "type": "builtins.int"}}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiodns._LOGGER", "name": "_LOGGER", "type": "logging.Logger"}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiodns.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "aiodns.__version__", "name": "__version__", "type": "builtins.str"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "error": {".class": "SymbolTableNode", "cross_ref": "aiodns.error", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "pycares": {".class": "SymbolTableNode", "cross_ref": "pycares", "kind": "Gdef", "module_public": false}, "query_class_map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiodns.query_class_map", "name": "query_class_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "query_type_map": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aiodns.query_type_map", "name": "query_type_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\aiodns\\__init__.py"}