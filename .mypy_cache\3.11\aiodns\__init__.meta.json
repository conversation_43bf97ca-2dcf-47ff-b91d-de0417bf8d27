{"data_mtime": 1753783521, "dep_lines": [7, 21, 1, 2, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 104], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 20, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["collections.abc", "aiodns.error", "asyncio", "functools", "logging", "pycares", "socket", "sys", "typing", "builtins", "dataclasses", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "enum", "types"], "hash": "c9ba364a983ebd7962e6f2971e18d869c53ea5fa", "id": "aiodns", "ignore_all": true, "interface_hash": "512703b95556dc4b9e3ce9b348100b5e0f22f362", "mtime": 1749398794, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\aiodns\\__init__.py", "plugin_data": null, "size": 10884, "suppressed": ["winloop"], "version_id": "1.15.0"}