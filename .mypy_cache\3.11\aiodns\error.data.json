{".class": "MypyFile", "_fullname": "aiodns.error", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARES_EADDRGETNETWORKPARAMS": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EADDRGETNETWORKPARAMS", "kind": "Gdef"}, "ARES_EBADFAMILY": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EBADFAMILY", "kind": "Gdef"}, "ARES_EBADFLAGS": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EBADFLAGS", "kind": "Gdef"}, "ARES_EBADHINTS": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EBADHINTS", "kind": "Gdef"}, "ARES_EBADNAME": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EBADNAME", "kind": "Gdef"}, "ARES_EBADQUERY": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EBADQUERY", "kind": "Gdef"}, "ARES_EBADRESP": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EBADRESP", "kind": "Gdef"}, "ARES_EBADSTR": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EBADSTR", "kind": "Gdef"}, "ARES_ECANCELLED": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ECANCELLED", "kind": "Gdef"}, "ARES_ECONNREFUSED": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ECONNREFUSED", "kind": "Gdef"}, "ARES_EDESTRUCTION": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EDESTRUCTION", "kind": "Gdef"}, "ARES_EFILE": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EFILE", "kind": "Gdef"}, "ARES_EFORMERR": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EFORMERR", "kind": "Gdef"}, "ARES_ELOADIPHLPAPI": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ELOADIPHLPAPI", "kind": "Gdef"}, "ARES_ENODATA": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ENODATA", "kind": "Gdef"}, "ARES_ENOMEM": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ENOMEM", "kind": "Gdef"}, "ARES_ENONAME": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ENONAME", "kind": "Gdef"}, "ARES_ENOTFOUND": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ENOTFOUND", "kind": "Gdef"}, "ARES_ENOTIMP": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ENOTIMP", "kind": "Gdef"}, "ARES_ENOTINITIALIZED": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ENOTINITIALIZED", "kind": "Gdef"}, "ARES_EOF": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EOF", "kind": "Gdef"}, "ARES_EREFUSED": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_EREFUSED", "kind": "Gdef"}, "ARES_ESERVFAIL": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ESERVFAIL", "kind": "Gdef"}, "ARES_ESERVICE": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ESERVICE", "kind": "Gdef"}, "ARES_ETIMEOUT": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_ETIMEOUT", "kind": "Gdef"}, "ARES_SUCCESS": {".class": "SymbolTableNode", "cross_ref": "pycares.errno.ARES_SUCCESS", "kind": "Gdef"}, "DNSError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aiodns.error.DNSError", "name": "DNSError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aiodns.error.DNSError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aiodns.error", "mro": ["aiodns.error.DNSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aiodns.error.DNSError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aiodns.error.DNSError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.error.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.error.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.error.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.error.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.error.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aiodns.error.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\aiodns\\error.py"}