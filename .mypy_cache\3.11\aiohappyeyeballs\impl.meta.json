{"data_mtime": 1753783524, "dep_lines": [10, 11, 3, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 10, 10, 10, 5, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["aiohappyeyeballs._staggered", "aiohappyeyeballs.types", "asyncio", "collections", "functools", "itertools", "socket", "typing", "aiohappyeyeballs", "builtins", "dataclasses", "_frozen_importlib", "_socket", "abc", "asyncio.events", "enum"], "hash": "53be9d00630d62370ad424f1fefe51ada9d9f90c", "id": "aiohappyeyeballs.impl", "ignore_all": true, "interface_hash": "b23af76ce4cb3cb9d093348718b8660b41dffc4f", "mtime": 1740598102, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\aiohappyeyeballs\\impl.py", "plugin_data": null, "size": 7708, "suppressed": [], "version_id": "1.15.0"}