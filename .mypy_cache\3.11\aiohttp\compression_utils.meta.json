{"data_mtime": 1753783521, "dep_lines": [3, 1, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 8], "dep_prios": [5, 10, 10, 5, 5, 20, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["concurrent.futures", "asyncio", "zlib", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc", "asyncio.locks", "asyncio.mixins", "concurrent", "concurrent.futures._base", "typing_extensions"], "hash": "330ce0303260d7f6896dbeee02ee6ba88a20e444", "id": "aiohttp.compression_utils", "ignore_all": true, "interface_hash": "9b2251d6d857689006f701d660d94808520627a7", "mtime": 1740598185, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\aiohttp\\compression_utils.py", "plugin_data": null, "size": 5854, "suppressed": ["brotli", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "version_id": "1.15.0"}