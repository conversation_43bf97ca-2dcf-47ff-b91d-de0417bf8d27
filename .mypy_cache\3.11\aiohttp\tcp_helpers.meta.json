{"data_mtime": 1753783521, "dep_lines": [3, 4, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 20, 30, 30, 30, 30], "dependencies": ["asyncio", "socket", "contextlib", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc", "asyncio.transports", "types"], "hash": "d11d204b42e139b713bd27a3aae3dd96c414926d", "id": "aiohttp.tcp_helpers", "ignore_all": true, "interface_hash": "cad93c508454f15452df7b59ad5ed25ce204486f", "mtime": 1740598185, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\aiohttp\\tcp_helpers.py", "plugin_data": null, "size": 998, "suppressed": [], "version_id": "1.15.0"}