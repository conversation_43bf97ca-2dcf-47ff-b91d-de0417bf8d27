{".class": "MypyFile", "_fullname": "anyio", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncFile": {".class": "SymbolTableNode", "cross_ref": "anyio._core._fileio.AsyncFile", "kind": "Gdef"}, "BrokenResourceError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.BrokenResourceError", "kind": "Gdef"}, "BrokenWorkerIntepreter": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.BrokenWorkerIntepreter", "kind": "Gdef"}, "BrokenWorkerProcess": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.BrokenWorkerProcess", "kind": "Gdef"}, "BusyResourceError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.BusyResourceError", "kind": "Gdef"}, "CancelScope": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.CancelScope", "kind": "Gdef"}, "CapacityLimiter": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.CapacityLimiter", "kind": "Gdef"}, "CapacityLimiterStatistics": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.CapacityLimiterStatistics", "kind": "Gdef"}, "ClosedResourceError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.ClosedResourceError", "kind": "Gdef"}, "Condition": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Condition", "kind": "Gdef"}, "ConditionStatistics": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.ConditionStatistics", "kind": "Gdef"}, "DelimiterNotFound": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.DelimiterNotFound", "kind": "Gdef"}, "EndOfStream": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.EndOfStream", "kind": "Gdef"}, "Event": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Event", "kind": "Gdef"}, "EventStatistics": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.EventStatistics", "kind": "Gdef"}, "IncompleteRead": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.IncompleteRead", "kind": "Gdef"}, "Lock": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Lock", "kind": "Gdef"}, "LockStatistics": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.LockStatistics", "kind": "Gdef"}, "NamedTemporaryFile": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.NamedTemporaryFile", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "anyio._core._fileio.Path", "kind": "Gdef"}, "ResourceGuard": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.ResourceGuard", "kind": "Gdef"}, "Semaphore": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.Semaphore", "kind": "Gdef"}, "SemaphoreStatistics": {".class": "SymbolTableNode", "cross_ref": "anyio._core._synchronization.SemaphoreStatistics", "kind": "Gdef"}, "SpooledTemporaryFile": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.SpooledTemporaryFile", "kind": "Gdef"}, "TASK_STATUS_IGNORED": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.TASK_STATUS_IGNORED", "kind": "Gdef"}, "TaskInfo": {".class": "SymbolTableNode", "cross_ref": "anyio._core._testing.TaskInfo", "kind": "Gdef"}, "TemporaryDirectory": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.TemporaryDirectory", "kind": "Gdef"}, "TemporaryFile": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.TemporaryFile", "kind": "Gdef"}, "TypedAttributeLookupError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.TypedAttributeLookupError", "kind": "Gdef"}, "TypedAttributeProvider": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.TypedAttributeProvider", "kind": "Gdef"}, "TypedAttributeSet": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.TypedAttributeSet", "kind": "Gdef"}, "WouldBlock": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.WouldBlock", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "anyio.__value", "name": "__value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "aclose_forcefully": {".class": "SymbolTableNode", "cross_ref": "anyio._core._resources.aclose_forcefully", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "connect_tcp": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.connect_tcp", "kind": "Gdef"}, "connect_unix": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.connect_unix", "kind": "Gdef"}, "create_connected_udp_socket": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.create_connected_udp_socket", "kind": "Gdef"}, "create_connected_unix_datagram_socket": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.create_connected_unix_datagram_socket", "kind": "Gdef"}, "create_memory_object_stream": {".class": "SymbolTableNode", "cross_ref": "anyio._core._streams.create_memory_object_stream", "kind": "Gdef"}, "create_task_group": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.create_task_group", "kind": "Gdef"}, "create_tcp_listener": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.create_tcp_listener", "kind": "Gdef"}, "create_udp_socket": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.create_udp_socket", "kind": "Gdef"}, "create_unix_datagram_socket": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.create_unix_datagram_socket", "kind": "Gdef"}, "create_unix_listener": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.create_unix_listener", "kind": "Gdef"}, "current_effective_deadline": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.current_effective_deadline", "kind": "Gdef"}, "current_time": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.current_time", "kind": "Gdef"}, "fail_after": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.fail_after", "kind": "Gdef"}, "get_all_backends": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.get_all_backends", "kind": "Gdef"}, "get_cancelled_exc_class": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.get_cancelled_exc_class", "kind": "Gdef"}, "get_current_task": {".class": "SymbolTableNode", "cross_ref": "anyio._core._testing.get_current_task", "kind": "Gdef"}, "get_running_tasks": {".class": "SymbolTableNode", "cross_ref": "anyio._core._testing.get_running_tasks", "kind": "Gdef"}, "getaddrinfo": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.getaddrinfo", "kind": "Gdef"}, "getnameinfo": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.getnameinfo", "kind": "Gdef"}, "gettempdir": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.gettempdir", "kind": "Gdef"}, "gettempdirb": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.gettempdirb", "kind": "Gdef"}, "mkdtemp": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.mkdtemp", "kind": "Gdef"}, "mkstemp": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tempfile.mkstemp", "kind": "Gdef"}, "move_on_after": {".class": "SymbolTableNode", "cross_ref": "anyio._core._tasks.move_on_after", "kind": "Gdef"}, "open_file": {".class": "SymbolTableNode", "cross_ref": "anyio._core._fileio.open_file", "kind": "Gdef"}, "open_process": {".class": "SymbolTableNode", "cross_ref": "anyio._core._subprocesses.open_process", "kind": "Gdef"}, "open_signal_receiver": {".class": "SymbolTableNode", "cross_ref": "anyio._core._signals.open_signal_receiver", "kind": "Gdef"}, "run": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.run", "kind": "Gdef"}, "run_process": {".class": "SymbolTableNode", "cross_ref": "anyio._core._subprocesses.run_process", "kind": "Gdef"}, "sleep": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.sleep", "kind": "Gdef"}, "sleep_forever": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.sleep_forever", "kind": "Gdef"}, "sleep_until": {".class": "SymbolTableNode", "cross_ref": "anyio._core._eventloop.sleep_until", "kind": "Gdef"}, "typed_attribute": {".class": "SymbolTableNode", "cross_ref": "anyio._core._typedattr.typed_attribute", "kind": "Gdef"}, "wait_all_tasks_blocked": {".class": "SymbolTableNode", "cross_ref": "anyio._core._testing.wait_all_tasks_blocked", "kind": "Gdef"}, "wait_readable": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.wait_readable", "kind": "Gdef"}, "wait_socket_readable": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.wait_socket_readable", "kind": "Gdef"}, "wait_socket_writable": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.wait_socket_writable", "kind": "Gdef"}, "wait_writable": {".class": "SymbolTableNode", "cross_ref": "anyio._core._sockets.wait_writable", "kind": "Gdef"}, "wrap_file": {".class": "SymbolTableNode", "cross_ref": "anyio._core._fileio.wrap_file", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\anyio\\__init__.py"}