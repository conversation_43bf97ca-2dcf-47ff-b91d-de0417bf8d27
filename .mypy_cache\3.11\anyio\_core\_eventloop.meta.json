{"data_mtime": 1753783521, "dep_lines": [6, 19, 1, 3, 4, 5, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 5, 10, 10, 10, 5, 5, 5, 10, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "anyio.abc", "__future__", "math", "sys", "threading", "contextlib", "importlib", "typing", "sniffio", "builtins", "dataclasses", "_frozen_importlib", "_thread", "_typeshed", "abc", "anyio.abc._eventloop"], "hash": "5db12510b1a53a24a28c64b11df1073775b98d6f", "id": "anyio._core._eventloop", "ignore_all": true, "interface_hash": "5885843cb3f2b755973e917b37098b19095b160b", "mtime": 1742985019, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\anyio\\_core\\_eventloop.py", "plugin_data": null, "size": 4695, "suppressed": [], "version_id": "1.15.0"}