{"data_mtime": 1753783521, "dep_lines": [11, 12, 4, 10, 1, 3, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._eventloop", "anyio._core._tasks", "collections.abc", "anyio.abc", "__future__", "sys", "io", "os", "subprocess", "typing", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc", "anyio.abc._resources", "anyio.abc._subprocesses"], "hash": "4320a104833b32aa279fa0c5d8a47344e6543eb7", "id": "anyio._core._subprocesses", "ignore_all": true, "interface_hash": "f2b23950fe307d573ba06eeebffe68a696d8d847", "mtime": 1742985019, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py", "plugin_data": null, "size": 8047, "suppressed": [], "version_id": "1.15.0"}