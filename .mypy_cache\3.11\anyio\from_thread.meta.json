{"data_mtime": 1753783521, "dep_lines": [23, 25, 26, 28, 4, 5, 23, 27, 1, 3, 6, 11, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio._core._eventloop", "anyio._core._synchronization", "anyio._core._tasks", "anyio.abc._tasks", "collections.abc", "concurrent.futures", "anyio._core", "anyio.abc", "__future__", "sys", "contextlib", "dataclasses", "inspect", "threading", "types", "typing", "builtins", "_frozen_importlib", "_thread", "_typeshed", "abc", "concurrent", "concurrent.futures._base", "enum"], "hash": "348513ae481f3e6cba9e6fc67712c0cb0e4f92fe", "id": "anyio.from_thread", "ignore_all": true, "interface_hash": "fec8d86e2dfe5a75a4e09886b714532cb597f86b", "mtime": 1742985019, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\anyio\\from_thread.py", "plugin_data": null, "size": 17478, "suppressed": [], "version_id": "1.15.0"}