{"data_mtime": 1753783521, "dep_lines": [3, 7, 1, 4, 5, 125, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "anyio.abc", "__future__", "dataclasses", "typing", "anyio", "builtins", "_frozen_importlib", "abc", "anyio._core", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._streams", "anyio.abc._tasks", "types"], "hash": "36c3719abf4c10603805c0c14e50ba73fec72bd7", "id": "anyio.streams.stapled", "ignore_all": true, "interface_hash": "baf494e21ad00a7f25c3879938df8235f056faab", "mtime": 1742985019, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\anyio\\streams\\stapled.py", "plugin_data": null, "size": 4302, "suppressed": [], "version_id": "1.15.0"}