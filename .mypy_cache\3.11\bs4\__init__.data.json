{".class": "MypyFile", "_fullname": "bs4", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"TreeBuilder\" and \"type\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.builder.TreeBuilder", "builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.<subclass of \"TreeBuilder\" and \"type\">", "name": "<subclass of \"TreeBuilder\" and \"type\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "bs4.<subclass of \"TreeBuilder\" and \"type\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4", "mro": ["bs4.<subclass of \"TreeBuilder\" and \"type\">", "bs4.builder.TreeBuilder", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AttributeResemblesVariableWarning": {".class": "SymbolTableNode", "cross_ref": "bs4._warnings.AttributeResemblesVariableWarning", "kind": "Gdef"}, "BeautifulSoup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.element.Tag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.BeautifulSoup", "name": "BeautifulSoup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4", "mro": ["bs4.BeautifulSoup", "bs4.element.Tag", "bs4.element.PageElement", "builtins.object"], "names": {".class": "SymbolTable", "ASCII_SPACES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.BeautifulSoup.ASCII_SPACES", "name": "ASCII_SPACES", "type": "builtins.str"}}, "DEFAULT_BUILDER_FEATURES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.BeautifulSoup.DEFAULT_BUILDER_FEATURES", "name": "DEFAULT_BUILDER_FEATURES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "ROOT_TAG_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.BeautifulSoup.ROOT_TAG_NAME", "name": "ROOT_TAG_NAME", "type": "builtins.str"}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.BeautifulSoup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getstate__ of BeautifulSoup", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "markup", "features", "builder", "parse_only", "from_encoding", "exclude_encodings", "element_classes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "markup", "features", "builder", "parse_only", "from_encoding", "exclude_encodings", "element_classes", "kwargs"], "arg_types": ["bs4.BeautifulSoup", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._IncomingMarkup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["bs4.builder.TreeBuilder", {".class": "TypeType", "item": "bs4.builder.TreeBuilder"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["bs4.filter.SoupStrainer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._Encodings"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "bs4.element.PageElement"}, {".class": "TypeType", "item": "bs4.element.PageElement"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["bs4.BeautifulSoup", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setstate__ of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_decode_markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "bs4.BeautifulSoup._decode_markup", "name": "_decode_markup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "arg_types": [{".class": "TypeType", "item": "bs4.BeautifulSoup"}, {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_decode_markup of BeautifulSoup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "bs4.BeautifulSoup._decode_markup", "name": "_decode_markup", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}, "_feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup._feed", "name": "_feed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.BeautifulSoup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_feed of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_linkage_fixer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup._linkage_fixer", "name": "_linkage_fixer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["bs4.BeautifulSoup", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_linkage_fixer of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_markup_is_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "bs4.BeautifulSoup._markup_is_url", "name": "_markup_is_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "arg_types": [{".class": "TypeType", "item": "bs4.BeautifulSoup"}, {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_markup_is_url of BeautifulSoup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "bs4.BeautifulSoup._markup_is_url", "name": "_markup_is_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "arg_types": [{".class": "TypeType", "item": "bs4.BeautifulSoup"}, {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_markup_is_url of BeautifulSoup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_markup_resembles_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "bs4.BeautifulSoup._markup_resembles_filename", "name": "_markup_resembles_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "arg_types": [{".class": "TypeType", "item": "bs4.BeautifulSoup"}, {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_markup_resembles_filename of BeautifulSoup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "bs4.BeautifulSoup._markup_resembles_filename", "name": "_markup_resembles_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "markup"], "arg_types": [{".class": "TypeType", "item": "bs4.BeautifulSoup"}, {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_markup_resembles_filename of BeautifulSoup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_most_recent_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup._most_recent_element", "name": "_most_recent_element", "type": {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_popToTag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "nsprefix", "inclusivePop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup._popToTag", "name": "_popToTag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "name", "nsprefix", "inclusivePop"], "arg_types": ["bs4.BeautifulSoup", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_popToTag of BeautifulSoup", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "builder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.builder", "name": "builder", "type": "bs4.builder.TreeBuilder"}}, "contains_replacement_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.contains_replacement_characters", "name": "contains_replacement_characters", "type": "builtins.bool"}}, "copy_self": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.copy_self", "name": "copy_self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.BeautifulSoup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_self of BeautifulSoup", "ret_type": "bs4.BeautifulSoup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "currentTag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.currentTag", "name": "currentTag", "type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "current_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.current_data", "name": "current_data", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "declared_html_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.declared_html_encoding", "name": "declared_html_encoding", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "indent_level", "eventual_encoding", "formatter", "iterator", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["self", "indent_level", "eventual_encoding", "formatter", "iterator", "kwargs"], "arg_types": ["bs4.BeautifulSoup", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["bs4.formatter.Formatter", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["bs4.element.PageElement"], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of BeautifulSoup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "element_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.element_classes", "name": "element_classes", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "bs4.element.PageElement"}, {".class": "TypeType", "item": "bs4.element.PageElement"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "endData": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "containerClass"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.endData", "name": "endData", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "containerClass"], "arg_types": ["bs4.BeautifulSoup", {".class": "UnionType", "items": [{".class": "TypeType", "item": "bs4.element.NavigableString"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endData of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.handle_data", "name": "handle_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bs4.BeautifulSoup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_data of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_endtag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "nsprefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.handle_endtag", "name": "handle_endtag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "nsprefix"], "arg_types": ["bs4.BeautifulSoup", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_endtag of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_starttag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "namespace", "nsprefix", "attrs", "sourceline", "sourcepos", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.handle_starttag", "name": "handle_starttag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "name", "namespace", "nsprefix", "attrs", "sourceline", "sourcepos", "namespaces"], "arg_types": ["bs4.BeautifulSoup", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawAttributeValues"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_starttag of BeautifulSoup", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "insert_after": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.insert_after", "name": "insert_after", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["bs4.BeautifulSoup", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._InsertableElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_after of BeautifulSoup", "ret_type": {".class": "Instance", "args": ["bs4.element.PageElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "insert_before": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.insert_before", "name": "insert_before", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["bs4.BeautifulSoup", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._InsertableElement"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_before of BeautifulSoup", "ret_type": {".class": "Instance", "args": ["bs4.element.PageElement"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.is_xml", "name": "is_xml", "type": "builtins.bool"}}, "known_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.known_xml", "name": "known_xml", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.markup", "name": "markup", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "new_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "subclass"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.new_string", "name": "new_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "subclass"], "arg_types": ["bs4.BeautifulSoup", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": "bs4.element.NavigableString"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_string of BeautifulSoup", "ret_type": "bs4.element.NavigableString", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "new_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "namespace", "nsprefix", "attrs", "sourceline", "sourcepos", "string", "kwattrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.new_tag", "name": "new_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "namespace", "nsprefix", "attrs", "sourceline", "sourcepos", "string", "kwattrs"], "arg_types": ["bs4.BeautifulSoup", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawAttributeValues"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new_tag of BeautifulSoup", "ret_type": "bs4.element.Tag", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "object_was_parsed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "o", "parent", "most_recent_element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.object_was_parsed", "name": "object_was_parsed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "o", "parent", "most_recent_element"], "arg_types": ["bs4.BeautifulSoup", "bs4.element.PageElement", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_was_parsed of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "open_tag_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.open_tag_counter", "name": "open_tag_counter", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "collections.Counter"}}}, "original_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.original_encoding", "name": "original_encoding", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "parse_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.parse_only", "name": "parse_only", "type": {".class": "UnionType", "items": ["bs4.filter.SoupStrainer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "popTag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.popTag", "name": "popTag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.BeautifulSoup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "popTag of BeautifulSoup", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preserve_whitespace_tag_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.preserve_whitespace_tag_stack", "name": "preserve_whitespace_tag_stack", "type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "pushTag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.pushTag", "name": "pushTag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["bs4.BeautifulSoup", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pushTag of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.BeautifulSoup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of BeautifulSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "string_container": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "base_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulSoup.string_container", "name": "string_container", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "base_class"], "arg_types": ["bs4.BeautifulSoup", {".class": "UnionType", "items": [{".class": "TypeType", "item": "bs4.element.NavigableString"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "string_container of BeautifulSoup", "ret_type": {".class": "TypeType", "item": "bs4.element.NavigableString"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "string_container_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.string_container_stack", "name": "string_container_stack", "type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "tagStack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.BeautifulSoup.tagStack", "name": "tagStack", "type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.BeautifulSoup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.BeautifulSoup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BeautifulStoneSoup": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.BeautifulSoup"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.BeautifulStoneSoup", "name": "BeautifulStoneSoup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.BeautifulStoneSoup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4", "mro": ["bs4.BeautifulStoneSoup", "bs4.BeautifulSoup", "bs4.element.Tag", "bs4.element.PageElement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.BeautifulStoneSoup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["bs4.BeautifulStoneSoup", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BeautifulStoneSoup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.BeautifulStoneSoup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.BeautifulStoneSoup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CData": {".class": "SymbolTableNode", "cross_ref": "bs4.element.CData", "kind": "Gdef"}, "CSS": {".class": "SymbolTableNode", "cross_ref": "bs4.css.CSS", "kind": "Gdef"}, "Comment": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Comment", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef", "module_public": false}, "CounterType": {".class": "SymbolTableNode", "cross_ref": "typing.Counter", "kind": "Gdef", "module_public": false}, "DEFAULT_OUTPUT_ENCODING": {".class": "SymbolTableNode", "cross_ref": "bs4.element.DEFAULT_OUTPUT_ENCODING", "kind": "Gdef", "module_public": false}, "Declaration": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Declaration", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Doctype": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Doctype", "kind": "Gdef"}, "ElementFilter": {".class": "SymbolTableNode", "cross_ref": "bs4.filter.ElementFilter", "kind": "Gdef"}, "FeatureNotFound": {".class": "SymbolTableNode", "cross_ref": "bs4.exceptions.FeatureNotFound", "kind": "Gdef"}, "Formatter": {".class": "SymbolTableNode", "cross_ref": "bs4.formatter.Formatter", "kind": "Gdef", "module_public": false}, "GuessedAtParserWarning": {".class": "SymbolTableNode", "cross_ref": "bs4._warnings.GuessedAtParserWarning", "kind": "Gdef"}, "HTMLParserTreeBuilder": {".class": "SymbolTableNode", "cross_ref": "bs4.builder._htmlparser.HTMLParserTreeBuilder", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "MarkupResemblesLocatorWarning": {".class": "SymbolTableNode", "cross_ref": "bs4._warnings.MarkupResemblesLocatorWarning", "kind": "Gdef"}, "NavigableString": {".class": "SymbolTableNode", "cross_ref": "bs4.element.NavigableString", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PYTHON_SPECIFIC_ENCODINGS": {".class": "SymbolTableNode", "cross_ref": "bs4.element.PYTHON_SPECIFIC_ENCODINGS", "kind": "Gdef", "module_public": false}, "PageElement": {".class": "SymbolTableNode", "cross_ref": "bs4.element.PageElement", "kind": "Gdef", "module_public": false}, "ParserRejectedMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4.exceptions.ParserRejectedMarkup", "kind": "Gdef"}, "ProcessingInstruction": {".class": "SymbolTableNode", "cross_ref": "bs4.element.ProcessingInstruction", "kind": "Gdef"}, "ResultSet": {".class": "SymbolTableNode", "cross_ref": "bs4.element.ResultSet", "kind": "Gdef"}, "Script": {".class": "SymbolTableNode", "cross_ref": "bs4.<PERSON>.<PERSON>", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "SoupStrainer": {".class": "SymbolTableNode", "cross_ref": "bs4.filter.SoupStrainer", "kind": "Gdef", "module_public": false}, "StopParsing": {".class": "SymbolTableNode", "cross_ref": "bs4.exceptions.StopParsing", "kind": "Gdef"}, "Stylesheet": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Stylesheet", "kind": "Gdef"}, "Tag": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Tag", "kind": "Gdef"}, "TemplateString": {".class": "SymbolTableNode", "cross_ref": "bs4.element.TemplateString", "kind": "Gdef"}, "TreeBuilder": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.TreeBuilder", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "UnicodeDammit": {".class": "SymbolTableNode", "cross_ref": "bs4.dammit.UnicodeDammit", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnusualUsageWarning": {".class": "SymbolTableNode", "cross_ref": "bs4._warnings.UnusualUsageWarning", "kind": "Gdef"}, "XMLParsedAsHTMLWarning": {".class": "SymbolTableNode", "cross_ref": "bs4._warnings.XMLParsedAsHTMLWarning", "kind": "Gdef"}, "_Encoding": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encoding", "kind": "Gdef", "module_public": false}, "_Encodings": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encodings", "kind": "Gdef", "module_public": false}, "_IncomingMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._IncomingMarkup", "kind": "Gdef", "module_public": false}, "_InsertableElement": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._InsertableElement", "kind": "Gdef", "module_public": false}, "_RawAttributeValue": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawAttributeValue", "kind": "Gdef", "module_public": false}, "_RawAttributeValues": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawAttributeValues", "kind": "Gdef", "module_public": false}, "_RawMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawMarkup", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "bs4.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.__author__", "name": "__author__", "type": "builtins.str"}}, "__copyright__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.__copyright__", "name": "__copyright__", "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.__file__", "name": "__file__", "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.__license__", "name": "__license__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.__version__", "name": "__version__", "type": "builtins.str"}}, "_deprecated": {".class": "SymbolTableNode", "cross_ref": "bs4._deprecation._deprecated", "kind": "Gdef", "module_public": false}, "_s": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bs4._s", "line": 1147, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "bs4.BeautifulSoup"}}, "_soup": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bs4._soup", "line": 1148, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "bs4.BeautifulSoup"}}, "builder_registry": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.builder_registry", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "soup": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "bs4.soup", "name": "soup", "type": "bs4.BeautifulSoup"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\__init__.py"}