{".class": "MypyFile", "_fullname": "bs4.builder", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AttributeDict": {".class": "SymbolTableNode", "cross_ref": "bs4.element.AttributeDict", "kind": "Gdef", "module_public": false}, "AttributeValueList": {".class": "SymbolTableNode", "cross_ref": "bs4.element.AttributeValueList", "kind": "Gdef", "module_public": false}, "BeautifulSoup": {".class": "SymbolTableNode", "cross_ref": "bs4.BeautifulSoup", "kind": "Gdef", "module_public": false}, "CharsetMetaAttributeValue": {".class": "SymbolTableNode", "cross_ref": "bs4.element.CharsetMetaAttributeValue", "kind": "Gdef", "module_public": false}, "ContentMetaAttributeValue": {".class": "SymbolTableNode", "cross_ref": "bs4.element.ContentMetaAttributeValue", "kind": "Gdef", "module_public": false}, "DetectsXMLParsedAsHTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder.DetectsXMLParsedAsHTML", "name": "DetectsXMLParsedAsHTML", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder.DetectsXMLParsedAsHTML", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder", "mro": ["bs4.builder.DetectsXMLParsedAsHTML", "builtins.object"], "names": {".class": "SymbolTable", "LOOKS_LIKE_HTML": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML.LOOKS_LIKE_HTML", "name": "LOOKS_LIKE_HTML", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "LOOKS_LIKE_HTML_B": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML.LOOKS_LIKE_HTML_B", "name": "LOOKS_LIKE_HTML_B", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "XML_PREFIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML.XML_PREFIX", "name": "XML_PREFIX", "type": "builtins.str"}}, "XML_PREFIX_B": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML.XML_PREFIX_B", "name": "XML_PREFIX_B", "type": "builtins.bytes"}}, "_document_might_be_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "processing_instruction"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.DetectsXMLParsedAsHTML._document_might_be_xml", "name": "_document_might_be_xml", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "processing_instruction"], "arg_types": ["bs4.builder.DetectsXMLParsedAsHTML", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_document_might_be_xml of DetectsXMLParsedAsHTML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_first_processing_instruction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML._first_processing_instruction", "name": "_first_processing_instruction", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_initialize_xml_detector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.DetectsXMLParsedAsHTML._initialize_xml_detector", "name": "_initialize_xml_detector", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.builder.DetectsXMLParsedAsHTML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_initialize_xml_detector of DetectsXMLParsedAsHTML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_root_tag_encountered": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.DetectsXMLParsedAsHTML._root_tag_encountered", "name": "_root_tag_encountered", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["bs4.builder.DetectsXMLParsedAsHTML", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_root_tag_encountered of DetectsXMLParsedAsHTML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_root_tag_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML._root_tag_name", "name": "_root_tag_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "stacklevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML._warn", "name": "_warn", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "stacklevel"], "arg_types": [{".class": "TypeType", "item": "bs4.builder.DetectsXMLParsedAsHTML"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn of DetectsXMLParsedAsHTML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML._warn", "name": "_warn", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "stacklevel"], "arg_types": [{".class": "TypeType", "item": "bs4.builder.DetectsXMLParsedAsHTML"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn of DetectsXMLParsedAsHTML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "warn_if_markup_looks_like_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "markup", "stacklevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML.warn_if_markup_looks_like_xml", "name": "warn_if_markup_looks_like_xml", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "markup", "stacklevel"], "arg_types": [{".class": "TypeType", "item": "bs4.builder.DetectsXMLParsedAsHTML"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn_if_markup_looks_like_xml of DetectsXMLParsedAsHTML", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "bs4.builder.DetectsXMLParsedAsHTML.warn_if_markup_looks_like_xml", "name": "warn_if_markup_looks_like_xml", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "markup", "stacklevel"], "arg_types": [{".class": "TypeType", "item": "bs4.builder.DetectsXMLParsedAsHTML"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn_if_markup_looks_like_xml of DetectsXMLParsedAsHTML", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder.DetectsXMLParsedAsHTML.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder.DetectsXMLParsedAsHTML", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "FAST": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder.FAST", "name": "FAST", "type": "builtins.str"}}, "HTML": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder.HTML", "name": "HTML", "type": "builtins.str"}}, "HTMLTreeBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.builder.TreeBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder.HTMLTreeBuilder", "name": "HTMLTreeBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder.HTMLTreeBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder", "mro": ["bs4.builder.HTMLTreeBuilder", "bs4.builder.TreeBuilder", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_BLOCK_ELEMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.HTMLTreeBuilder.DEFAULT_BLOCK_ELEMENTS", "name": "DEFAULT_BLOCK_ELEMENTS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "DEFAULT_CDATA_LIST_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.HTMLTreeBuilder.DEFAULT_CDATA_LIST_ATTRIBUTES", "name": "DEFAULT_CDATA_LIST_ATTRIBUTES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "DEFAULT_EMPTY_ELEMENT_TAGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.HTMLTreeBuilder.DEFAULT_EMPTY_ELEMENT_TAGS", "name": "DEFAULT_EMPTY_ELEMENT_TAGS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "DEFAULT_PRESERVE_WHITESPACE_TAGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.HTMLTreeBuilder.DEFAULT_PRESERVE_WHITESPACE_TAGS", "name": "DEFAULT_PRESERVE_WHITESPACE_TAGS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "DEFAULT_STRING_CONTAINERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.HTMLTreeBuilder.DEFAULT_STRING_CONTAINERS", "name": "DEFAULT_STRING_CONTAINERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "set_up_substitutions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.HTMLTreeBuilder.set_up_substitutions", "name": "set_up_substitutions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["bs4.builder.HTMLTreeBuilder", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_up_substitutions of HTMLTreeBuilder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder.HTMLTreeBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder.HTMLTreeBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTML_5": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder.HTML_5", "name": "HTML_5", "type": "builtins.str"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef", "module_public": false}, "NavigableString": {".class": "SymbolTableNode", "cross_ref": "bs4.element.NavigableString", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PERMISSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder.PERMISSIVE", "name": "PERMISSIVE", "type": "builtins.str"}}, "ParserRejectedMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4.exceptions.ParserRejectedMarkup", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "RubyParenthesisString": {".class": "SymbolTableNode", "cross_ref": "bs4.element.RubyParenthesisString", "kind": "Gdef", "module_public": false}, "RubyTextString": {".class": "SymbolTableNode", "cross_ref": "bs4.element.RubyTextString", "kind": "Gdef", "module_public": false}, "SAXTreeBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.builder.TreeBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder.SAXTreeBuilder", "name": "SAXTreeBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder", "mro": ["bs4.builder.SAXTreeBuilder", "bs4.builder.TreeBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["bs4.builder.SAXTreeBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.characters", "name": "characters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["bs4.builder.SAXTreeBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "characters of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.builder.SAXTreeBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endDocument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.endDocument", "name": "endDocument", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.builder.SAXTreeBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endDocument of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endElement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.endElement", "name": "endElement", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["bs4.builder.SAXTreeBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endElement of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endElementNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "nsTuple", "nodeName"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.endElementNS", "name": "endElementNS", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "nsTuple", "nodeName"], "arg_types": ["bs4.builder.SAXTreeBuilder", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endElementNS of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endPrefixMapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.endPrefixMapping", "name": "endPrefixMapping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prefix"], "arg_types": ["bs4.builder.SAXTreeBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endPrefixMapping of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "arg_types": ["bs4.builder.SAXTreeBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startDocument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.startDocument", "name": "startDocument", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.builder.SAXTreeBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startDocument of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startElement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.startElement", "name": "startElement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "arg_types": ["bs4.builder.SAXTreeBuilder", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startElement of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startElementNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nsTuple", "nodeName", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.startElementNS", "name": "startElementNS", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "nsTuple", "nodeName", "attrs"], "arg_types": ["bs4.builder.SAXTreeBuilder", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startElementNS of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startPrefixMapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "nodeValue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.SAXTreeBuilder.startPrefixMapping", "name": "startPrefixMapping", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "nodeValue"], "arg_types": ["bs4.builder.SAXTreeBuilder", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startPrefixMapping of SAXTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder.SAXTreeBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder.SAXTreeBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STRICT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder.STRICT", "name": "STRICT", "type": "builtins.str"}}, "Script": {".class": "SymbolTableNode", "cross_ref": "bs4.<PERSON>.<PERSON>", "kind": "Gdef", "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "Stylesheet": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Stylesheet", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tag": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Tag", "kind": "Gdef", "module_public": false}, "TemplateString": {".class": "SymbolTableNode", "cross_ref": "bs4.element.TemplateString", "kind": "Gdef", "module_public": false}, "TreeBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder.TreeBuilder", "name": "TreeBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder", "mro": ["bs4.builder.TreeBuilder", "builtins.object"], "names": {".class": "SymbolTable", "ALTERNATE_NAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.ALTERNATE_NAMES", "name": "ALTERNATE_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "DEFAULT_CDATA_LIST_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.DEFAULT_CDATA_LIST_ATTRIBUTES", "name": "DEFAULT_CDATA_LIST_ATTRIBUTES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "DEFAULT_EMPTY_ELEMENT_TAGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.DEFAULT_EMPTY_ELEMENT_TAGS", "name": "DEFAULT_EMPTY_ELEMENT_TAGS", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "DEFAULT_PRESERVE_WHITESPACE_TAGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.DEFAULT_PRESERVE_WHITESPACE_TAGS", "name": "DEFAULT_PRESERVE_WHITESPACE_TAGS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "DEFAULT_STRING_CONTAINERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.DEFAULT_STRING_CONTAINERS", "name": "DEFAULT_STRING_CONTAINERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.NAME", "name": "NAME", "type": "builtins.str"}}, "TRACKS_LINE_NUMBERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.TRACKS_LINE_NUMBERS", "name": "TRACKS_LINE_NUMBERS", "type": "builtins.bool"}}, "USE_DEFAULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.USE_DEFAULT", "name": "USE_DEFAULT", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "multi_valued_attributes", "preserve_whitespace_tags", "store_line_numbers", "string_containers", "empty_element_tags", "attribute_dict_class", "attribute_value_list_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "multi_valued_attributes", "preserve_whitespace_tags", "store_line_numbers", "string_containers", "empty_element_tags", "attribute_dict_class", "attribute_value_list_class"], "arg_types": ["bs4.builder.TreeBuilder", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "bs4.element.NavigableString"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "TypeType", "item": "bs4.element.AttributeDict"}, {".class": "TypeType", "item": "bs4.element.AttributeValueList"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replace_cdata_list_attribute_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag_name", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder._replace_cdata_list_attribute_values", "name": "_replace_cdata_list_attribute_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag_name", "attrs"], "arg_types": ["bs4.builder.TreeBuilder", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawOrProcessedAttributeValues"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace_cdata_list_attribute_values of TreeBuilder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._AttributeValues"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attribute_dict_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "bs4.builder.TreeBuilder.attribute_dict_class", "name": "attribute_dict_class", "type": {".class": "TypeType", "item": "bs4.element.AttributeDict"}}}, "attribute_value_list_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "bs4.builder.TreeBuilder.attribute_value_list_class", "name": "attribute_value_list_class", "type": {".class": "TypeType", "item": "bs4.element.AttributeValueList"}}}, "can_be_empty_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.can_be_empty_element", "name": "can_be_empty_element", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_name"], "arg_types": ["bs4.builder.TreeBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "can_be_empty_element of TreeBuilder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cdata_list_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.TreeBuilder.cdata_list_attributes", "name": "cdata_list_attributes", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "empty_element_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.empty_element_tags", "name": "empty_element_tags", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.features", "name": "features", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "arg_types": ["bs4.builder.TreeBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of TreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize_soup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "soup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.initialize_soup", "name": "initialize_soup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "soup"], "arg_types": ["bs4.builder.TreeBuilder", "bs4.BeautifulSoup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize_soup of TreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.is_xml", "name": "is_xml", "type": "builtins.bool"}}, "picklable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder.TreeBuilder.picklable", "name": "picklable", "type": "builtins.bool"}}, "prepare_markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "markup", "user_specified_encoding", "document_declared_encoding", "exclude_encodings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.prepare_markup", "name": "prepare_markup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "markup", "user_specified_encoding", "document_declared_encoding", "exclude_encodings"], "arg_types": ["bs4.builder.TreeBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._Encodings"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_markup of TreeBuilder", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preserve_whitespace_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.TreeBuilder.preserve_whitespace_tags", "name": "preserve_whitespace_tags", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.builder.TreeBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of TreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_up_substitutions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.set_up_substitutions", "name": "set_up_substitutions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["bs4.builder.TreeBuilder", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_up_substitutions of TreeBuilder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "soup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.TreeBuilder.soup", "name": "soup", "type": {".class": "UnionType", "items": ["bs4.BeautifulSoup", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "store_line_numbers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "bs4.builder.TreeBuilder.store_line_numbers", "name": "store_line_numbers", "type": "builtins.bool"}}, "string_containers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.TreeBuilder.string_containers", "name": "string_containers", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "bs4.element.NavigableString"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "test_fragment_to_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fragment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilder.test_fragment_to_document", "name": "test_fragment_to_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fragment"], "arg_types": ["bs4.builder.TreeBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_fragment_to_document of TreeBuilder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tracks_line_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.TreeBuilder.tracks_line_numbers", "name": "tracks_line_numbers", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder.TreeBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder.TreeBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TreeBuilderRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder.TreeBuilderRegistry", "name": "TreeBuilderRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilderRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder", "mro": ["bs4.builder.TreeBuilderRegistry", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilderRegistry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.builder.TreeBuilderRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TreeBuilderRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "builders": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.TreeBuilderRegistry.builders", "name": "builders", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "bs4.builder.TreeBuilder"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "builders_for_feature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder.TreeBuilderRegistry.builders_for_feature", "name": "builders_for_feature", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeType", "item": "bs4.builder.TreeBuilder"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "features"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilderRegistry.lookup", "name": "lookup", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "features"], "arg_types": ["bs4.builder.TreeBuilderRegistry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lookup of TreeBuilderRegistry", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "bs4.builder.TreeBuilder"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "treebuilder_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.TreeBuilderRegistry.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "treebuilder_class"], "arg_types": ["bs4.builder.TreeBuilderRegistry", {".class": "TypeType", "item": "bs4.builder.TreeBuilder"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of TreeBuilderRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder.TreeBuilderRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder.TreeBuilderRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "XML": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder.XML", "name": "XML", "type": "builtins.str"}}, "XMLParsedAsHTMLWarning": {".class": "SymbolTableNode", "cross_ref": "bs4._warnings.XMLParsedAsHTMLWarning", "kind": "Gdef", "module_public": false}, "_AttributeValue": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._AttributeValue", "kind": "Gdef", "module_public": false}, "_AttributeValues": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._AttributeValues", "kind": "Gdef", "module_public": false}, "_Encoding": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encoding", "kind": "Gdef", "module_public": false}, "_Encodings": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encodings", "kind": "Gdef", "module_public": false}, "_RawAttributeValue": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawAttributeValue", "kind": "Gdef", "module_public": false}, "_RawMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawMarkup", "kind": "Gdef", "module_public": false}, "_RawOrProcessedAttributeValues": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawOrProcessedAttributeValues", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "bs4.builder.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder.__file__", "name": "__file__", "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder.__license__", "name": "__license__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_html5lib": {".class": "SymbolTableNode", "cross_ref": "bs4.builder._html5lib", "kind": "Gdef", "module_public": false}, "_htmlparser": {".class": "SymbolTableNode", "cross_ref": "bs4.builder._htmlparser", "kind": "Gdef", "module_public": false}, "_lxml": {".class": "SymbolTableNode", "cross_ref": "bs4.builder._lxml", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "builder_registry": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "bs4.builder.builder_registry", "name": "builder_registry", "type": "bs4.builder.TreeBuilderRegistry"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef", "module_public": false}, "nonwhitespace_re": {".class": "SymbolTableNode", "cross_ref": "bs4.element.nonwhitespace_re", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "register_treebuilders_from": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder.register_treebuilders_from", "name": "register_treebuilders_from", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_treebuilders_from", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\builder\\__init__.py"}