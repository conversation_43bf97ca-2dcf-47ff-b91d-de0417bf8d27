{"data_mtime": 1753783523, "dep_lines": [20, 30, 37, 8, 19, 29, 54, 1, 1, 1, 1, 1, 1, 1, 43, 56, 42], "dep_prios": [5, 5, 5, 5, 5, 10, 20, 5, 20, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["bs4._typing", "bs4.builder", "bs4.element", "typing", "typing_extensions", "warnings", "bs4", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc", "bs4.filter", "types"], "hash": "732a7bd36cd79d5356c399743bf3e64c6c269e02", "id": "bs4.builder._html5lib", "ignore_all": true, "interface_hash": "da3052eaffc775e7c2bac69af07816de6d976c54", "mtime": 1748023214, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\builder\\_html5lib.py", "plugin_data": null, "size": 22846, "suppressed": ["html5lib.constants", "html5lib.treebuilders", "html5lib"], "version_id": "1.15.0"}