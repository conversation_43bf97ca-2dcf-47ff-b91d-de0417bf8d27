{".class": "MypyFile", "_fullname": "bs4.builder._htmlparser", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AttributeDict": {".class": "SymbolTableNode", "cross_ref": "bs4.element.AttributeDict", "kind": "Gdef", "module_public": false}, "BeautifulSoup": {".class": "SymbolTableNode", "cross_ref": "bs4.BeautifulSoup", "kind": "Gdef", "module_public": false}, "BeautifulSoupHTMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["html.parser.HTMLParser", "bs4.builder.DetectsXMLParsedAsHTML"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser", "name": "BeautifulSoupHTMLParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder._htmlparser", "mro": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "html.parser.HTMLParser", "_markupbase.ParserBase", "bs4.builder.DetectsXMLParsedAsHTML", "builtins.object"], "names": {".class": "SymbolTable", "IGNORE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.IGNORE", "name": "IGNORE", "type": "builtins.str"}}, "REPLACE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.REPLACE", "name": "REPLACE", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 4], "arg_names": ["self", "soup", "args", "on_duplicate_attribute", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 4], "arg_names": ["self", "soup", "args", "on_duplicate_attribute", "kwargs"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "bs4.BeautifulSoup", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "bs4.builder._htmlparser._DuplicateAttributeHandler"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "already_closed_empty_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.already_closed_empty_element", "name": "already_closed_empty_element", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "attribute_dict_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.attribute_dict_class", "name": "attribute_dict_class", "type": {".class": "TypeType", "item": "bs4.element.AttributeDict"}}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_charref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_charref", "name": "handle_charref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_charref of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_comment", "name": "handle_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_comment of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_data", "name": "handle_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_data of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_decl", "name": "handle_decl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_decl of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_endtag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "check_already_closed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_endtag", "name": "handle_endtag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "check_already_closed"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_endtag of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_entityref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_entityref", "name": "handle_entityref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_entityref of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_pi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_pi", "name": "handle_pi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_pi of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_startendtag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_startendtag", "name": "handle_startendtag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_startendtag of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_starttag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "attrs", "handle_empty_element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.handle_starttag", "name": "handle_starttag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "attrs", "handle_empty_element"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_starttag of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_duplicate_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.on_duplicate_attribute", "name": "on_duplicate_attribute", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "bs4.builder._htmlparser._DuplicateAttributeHandler"}], "uses_pep604_syntax": false}}}, "soup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.soup", "name": "soup", "type": "bs4.BeautifulSoup"}}, "unknown_decl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.unknown_decl", "name": "unknown_decl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bs4.builder._htmlparser.BeautifulSoupHTMLParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unknown_decl of BeautifulSoupHTMLParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder._htmlparser.BeautifulSoupHTMLParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder._htmlparser.BeautifulSoupHTMLParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CData": {".class": "SymbolTableNode", "cross_ref": "bs4.element.CData", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Comment": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Comment", "kind": "Gdef", "module_public": false}, "Declaration": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Declaration", "kind": "Gdef", "module_public": false}, "DetectsXMLParsedAsHTML": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.DetectsXMLParsedAsHTML", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Doctype": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Doctype", "kind": "Gdef", "module_public": false}, "EntitySubstitution": {".class": "SymbolTableNode", "cross_ref": "bs4.dammit.EntitySubstitution", "kind": "Gdef", "module_public": false}, "HTML": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.HTML", "kind": "Gdef", "module_public": false}, "HTMLPARSER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.HTMLPARSER", "name": "HTMLPARSER", "type": "builtins.str"}}, "HTMLParser": {".class": "SymbolTableNode", "cross_ref": "html.parser.HTMLParser", "kind": "Gdef", "module_public": false}, "HTMLParserTreeBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.builder.HTMLTreeBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder", "name": "HTMLParserTreeBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder._htmlparser", "mro": ["bs4.builder._htmlparser.HTMLParserTreeBuilder", "bs4.builder.HTMLTreeBuilder", "bs4.builder.TreeBuilder", "builtins.object"], "names": {".class": "SymbolTable", "NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.NAME", "name": "NAME", "type": "builtins.str"}}, "TRACKS_LINE_NUMBERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.TRACKS_LINE_NUMBERS", "name": "TRACKS_LINE_NUMBERS", "type": "builtins.bool"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "parser_args", "parser_kwargs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "parser_args", "parser_kwargs", "kwargs"], "arg_types": ["bs4.builder._htmlparser.HTMLParserTreeBuilder", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTMLParserTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.features", "name": "features", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "arg_types": ["bs4.builder._htmlparser.HTMLParserTreeBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of HTMLParserTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.is_xml", "name": "is_xml", "type": "builtins.bool"}}, "parser_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.parser_args", "name": "parser_args", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "picklable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.picklable", "name": "picklable", "type": "builtins.bool"}}, "prepare_markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "markup", "user_specified_encoding", "document_declared_encoding", "exclude_encodings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.prepare_markup", "name": "prepare_markup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "markup", "user_specified_encoding", "document_declared_encoding", "exclude_encodings"], "arg_types": ["bs4.builder._htmlparser.HTMLParserTreeBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._Encodings"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_markup of HTMLParserTreeBuilder", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder._htmlparser.HTMLParserTreeBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder._htmlparser.HTMLParserTreeBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTMLTreeBuilder": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.HTMLTreeBuilder", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "NavigableString": {".class": "SymbolTableNode", "cross_ref": "bs4.element.NavigableString", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParserRejectedMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4.exceptions.ParserRejectedMarkup", "kind": "Gdef", "module_public": false}, "ProcessingInstruction": {".class": "SymbolTableNode", "cross_ref": "bs4.element.ProcessingInstruction", "kind": "Gdef", "module_public": false}, "STRICT": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.STRICT", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "UnicodeDammit": {".class": "SymbolTableNode", "cross_ref": "bs4.dammit.UnicodeDammit", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_DuplicateAttributeHandler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bs4.builder._htmlparser._DuplicateAttributeHandler", "line": 58, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_Encoding": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encoding", "kind": "Gdef", "module_public": false}, "_Encodings": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encodings", "kind": "Gdef", "module_public": false}, "_RawMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawMarkup", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._htmlparser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._htmlparser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._htmlparser.__file__", "name": "__file__", "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder._htmlparser.__license__", "name": "__license__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._htmlparser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._htmlparser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._htmlparser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py"}