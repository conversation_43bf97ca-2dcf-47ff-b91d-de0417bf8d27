{".class": "MypyFile", "_fullname": "bs4.builder._lxml", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AttributeDict": {".class": "SymbolTableNode", "cross_ref": "bs4.element.AttributeDict", "kind": "Gdef", "module_public": false}, "BeautifulSoup": {".class": "SymbolTableNode", "cross_ref": "bs4.BeautifulSoup", "kind": "Gdef", "module_public": false}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef", "module_public": false}, "Comment": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Comment", "kind": "Gdef", "module_public": false}, "DetectsXMLParsedAsHTML": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.DetectsXMLParsedAsHTML", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Doctype": {".class": "SymbolTableNode", "cross_ref": "bs4.element.Doctype", "kind": "Gdef", "module_public": false}, "EncodingDetector": {".class": "SymbolTableNode", "cross_ref": "bs4.dammit.EncodingDetector", "kind": "Gdef", "module_public": false}, "FAST": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.FAST", "kind": "Gdef", "module_public": false}, "HTML": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.HTML", "kind": "Gdef", "module_public": false}, "HTMLTreeBuilder": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.HTMLTreeBuilder", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "LXML": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXML", "name": "LXML", "type": "builtins.str"}}, "LXMLTreeBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.builder.HTMLTreeBuilder", "bs4.builder._lxml.LXMLTreeBuilderForXML"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder._lxml.LXMLTreeBuilder", "name": "LXMLTreeBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder._lxml", "mro": ["bs4.builder._lxml.LXMLTreeBuilder", "bs4.builder.HTMLTreeBuilder", "bs4.builder._lxml.LXMLTreeBuilderForXML", "bs4.builder.TreeBuilder", "builtins.object"], "names": {".class": "SymbolTable", "ALTERNATE_NAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilder.ALTERNATE_NAMES", "name": "ALTERNATE_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilder.NAME", "name": "NAME", "type": "builtins.str"}}, "default_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilder.default_parser", "name": "default_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilder", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_parser of LXMLTreeBuilder", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "bs4.builder._lxml._ParserOrParserClass"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilder.features", "name": "features", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilder.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of LXMLTreeBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilder.is_xml", "name": "is_xml", "type": "builtins.bool"}}, "test_fragment_to_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fragment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilder.test_fragment_to_document", "name": "test_fragment_to_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fragment"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_fragment_to_document of LXMLTreeBuilder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder._lxml.LXMLTreeBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder._lxml.LXMLTreeBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LXMLTreeBuilderForXML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["bs4.builder.TreeBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML", "name": "LXMLTreeBuilderForXML", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "bs4.builder._lxml", "mro": ["bs4.builder._lxml.LXMLTreeBuilderForXML", "bs4.builder.TreeBuilder", "builtins.object"], "names": {".class": "SymbolTable", "ALTERNATE_NAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.ALTERNATE_NAMES", "name": "ALTERNATE_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "CHUNK_SIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.CHUNK_SIZE", "name": "CHUNK_SIZE", "type": "builtins.int"}}, "DEFAULT_NSMAPS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.DEFAULT_NSMAPS", "name": "DEFAULT_NSMAPS", "type": {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._NamespaceMapping"}}}, "DEFAULT_NSMAPS_INVERTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.DEFAULT_NSMAPS_INVERTED", "name": "DEFAULT_NSMAPS_INVERTED", "type": {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._InvertedNamespaceMapping"}}}, "DEFAULT_PARSER_CLASS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.DEFAULT_PARSER_CLASS", "name": "DEFAULT_PARSER_CLASS", "type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}}}}, "NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.NAME", "name": "NAME", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "parser", "empty_element_tags", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "parser", "empty_element_tags", "kwargs"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_default_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML._default_parser", "name": "_default_parser", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_getNsTag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML._getNsTag", "name": "_getNsTag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_getNsTag of LXMLTreeBuilderForXML", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prefix_for_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML._prefix_for_namespace", "name": "_prefix_for_namespace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "namespace"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prefix_for_namespace of LXMLTreeBuilderForXML", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_register_namespaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapping"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML._register_namespaces", "name": "_register_namespaces", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mapping"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register_namespaces of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "active_namespace_prefixes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.active_namespace_prefixes", "name": "active_namespace_prefixes", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.comment", "name": "comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "comment of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.data", "name": "data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.default_parser", "name": "default_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_parser of LXMLTreeBuilderForXML", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "bs4.builder._lxml._ParserOrParserClass"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "doctype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "pubid", "system"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.doctype", "name": "doctype", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "pubid", "system"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "doctype of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "empty_element_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.empty_element_tags", "name": "empty_element_tags", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.end", "name": "end", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.features", "name": "features", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "markup"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize_soup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "soup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.initialize_soup", "name": "initialize_soup", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "soup"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", "bs4.BeautifulSoup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize_soup of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.is_xml", "name": "is_xml", "type": "builtins.bool"}}, "nsmaps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.nsmaps", "name": "nsmaps", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._InvertedNamespaceMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.parser", "name": "parser", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "parser_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.parser_for", "name": "parser_for", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encoding"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parser_for of LXMLTreeBuilderForXML", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "bs4.builder._lxml._LXMLParser"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.pi", "name": "pi", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "data"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pi of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_markup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "markup", "user_specified_encoding", "document_declared_encoding", "exclude_encodings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.prepare_markup", "name": "prepare_markup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "markup", "user_specified_encoding", "document_declared_encoding", "exclude_encodings"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._RawMarkup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._Encodings"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_markup of LXMLTreeBuilderForXML", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "processing_instruction_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.processing_instruction_class", "name": "processing_instruction_class", "type": {".class": "TypeType", "item": "bs4.element.ProcessingInstruction"}}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tag", "attrs", "nsmap"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.start", "name": "start", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tag", "attrs", "nsmap"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "TypeAliasType", "args": [], "type_ref": "bs4._typing._NamespaceMapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start of LXMLTreeBuilderForXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_fragment_to_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fragment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.test_fragment_to_document", "name": "test_fragment_to_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fragment"], "arg_types": ["bs4.builder._lxml.LXMLTreeBuilderForXML", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_fragment_to_document of LXMLTreeBuilderForXML", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bs4.builder._lxml.LXMLTreeBuilderForXML.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bs4.builder._lxml.LXMLTreeBuilderForXML", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "NamespacedAttribute": {".class": "SymbolTableNode", "cross_ref": "bs4.element.NamespacedAttribute", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PERMISSIVE": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.PERMISSIVE", "kind": "Gdef", "module_public": false}, "ParserRejectedMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4.exceptions.ParserRejectedMarkup", "kind": "Gdef", "module_public": false}, "ProcessingInstruction": {".class": "SymbolTableNode", "cross_ref": "bs4.element.ProcessingInstruction", "kind": "Gdef", "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TreeBuilder": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.TreeBuilder", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "XML": {".class": "SymbolTableNode", "cross_ref": "bs4.builder.XML", "kind": "Gdef", "module_public": false}, "XMLAttributeDict": {".class": "SymbolTableNode", "cross_ref": "bs4.element.XMLAttributeDict", "kind": "Gdef", "module_public": false}, "XMLProcessingInstruction": {".class": "SymbolTableNode", "cross_ref": "bs4.element.XMLProcessingInstruction", "kind": "Gdef", "module_public": false}, "_Encoding": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encoding", "kind": "Gdef", "module_public": false}, "_Encodings": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._Encodings", "kind": "Gdef", "module_public": false}, "_InvertedNamespaceMapping": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._InvertedNamespaceMapping", "kind": "Gdef", "module_public": false}, "_LXMLParser": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bs4.builder._lxml._LXMLParser", "line": 71, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}}}, "_NamespaceMapping": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._NamespaceMapping", "kind": "Gdef", "module_public": false}, "_NamespacePrefix": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._NamespacePrefix", "kind": "Gdef", "module_public": false}, "_NamespaceURL": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._NamespaceURL", "kind": "Gdef", "module_public": false}, "_ParserOrParserClass": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bs4.builder._lxml._ParserOrParserClass", "line": 72, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bs4.builder._lxml._LXMLParser"}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}}], "uses_pep604_syntax": false}}}, "_RawMarkup": {".class": "SymbolTableNode", "cross_ref": "bs4._typing._RawMarkup", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "bs4.builder._lxml.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._lxml.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._lxml.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._lxml.__file__", "name": "__file__", "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "bs4.builder._lxml.__license__", "name": "__license__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._lxml.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._lxml.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bs4.builder._lxml.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_invert": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bs4.builder._lxml._invert", "name": "_invert", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["d"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_invert", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "etree": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "bs4.builder._lxml.etree", "name": "etree", "type": {".class": "AnyType", "missing_import_name": "bs4.builder._lxml.etree", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\builder\\_lxml.py"}