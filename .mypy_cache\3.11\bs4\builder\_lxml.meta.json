{"data_mtime": 1753783523, "dep_lines": [30, 39, 48, 49, 52, 2, 13, 25, 27, 61, 1, 1, 1, 1, 1, 1, 1, 29], "dep_prios": [5, 5, 5, 5, 25, 5, 5, 5, 5, 25, 5, 20, 30, 30, 30, 30, 30, 5], "dependencies": ["bs4.element", "bs4.builder", "bs4.dammit", "bs4.exceptions", "bs4._typing", "__future__", "typing", "typing_extensions", "io", "bs4", "builtins", "dataclasses", "_frozen_importlib", "_io", "_typeshed", "abc", "types"], "hash": "25a6ec0f142f17f274f6f04ab0675c8a48afc33a", "id": "bs4.builder._lxml", "ignore_all": true, "interface_hash": "551988e40ada00a0a6540c9ea1e548ecdab246e8", "mtime": 1748023214, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\builder\\_lxml.py", "plugin_data": null, "size": 18545, "suppressed": ["lxml"], "version_id": "1.15.0"}