{"data_mtime": 1753783523, "dep_lines": [15, 35, 16, 17, 19, 20, 21, 22, 34, 39, 63, 57, 1, 1, 1, 1, 1, 1, 1, 50], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 10, 10, 5, 20, 30, 30, 30, 30, 30, 10], "dependencies": ["html.entities", "bs4._typing", "collections", "codecs", "re", "logging", "types", "typing", "typing_extensions", "warnings", "charset_normalizer", "chardet", "builtins", "dataclasses", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum"], "hash": "8362443a9df23769d92181b50e60f789881a5249", "id": "bs4.dammit", "ignore_all": true, "interface_hash": "6f586a7b9ee0b932739fe332cdaa9069e4f651b1", "mtime": 1748023214, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\dammit.py", "plugin_data": null, "size": 51472, "suppressed": ["cchardet"], "version_id": "1.15.0"}