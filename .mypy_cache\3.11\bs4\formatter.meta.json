{"data_mtime": 1753783523, "dep_lines": [4, 7, 149, 1, 2, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 20, 5, 5, 5, 5, 20, 30, 30, 30, 30], "dependencies": ["bs4.dammit", "bs4._typing", "bs4.element", "__future__", "typing", "typing_extensions", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "9102f61fe969a748273316834c14f510327676fc", "id": "bs4.formatter", "ignore_all": true, "interface_hash": "0b82b1ed8be24e6447d9c217a9f08aea1f71ab4a", "mtime": 1748023214, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\bs4\\formatter.py", "plugin_data": null, "size": 10464, "suppressed": [], "version_id": "1.15.0"}