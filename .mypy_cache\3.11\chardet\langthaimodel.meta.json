{"data_mtime": 1753783513, "dep_lines": [1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 30, 30, 30], "dependencies": ["chardet.sbcharsetprober", "builtins", "dataclasses", "_frozen_importlib", "abc", "typing"], "hash": "a733226a15d14a755b1953964d7c6eff3971bdd0", "id": "chardet.langthaimodel", "ignore_all": true, "interface_hash": "c21163565abaafca74530b0fdf0bf06cac41270c", "mtime": 1748603652, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\chardet\\langthaimodel.py", "plugin_data": null, "size": 102762, "suppressed": [], "version_id": "1.15.0"}