{"data_mtime": 1753783515, "dep_lines": [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["chardet.big5prober", "chardet.charsetgroupprober", "chardet.cp949p<PERSON>r", "chardet.enums", "chardet.e<PERSON><PERSON><PERSON><PERSON>r", "chardet.e<PERSON><PERSON><PERSON><PERSON>", "chardet.euctwp<PERSON>r", "chardet.gb2312prober", "chardet.johab<PERSON><PERSON>r", "chardet.s<PERSON><PERSON><PERSON>r", "chardet.utf8prober", "builtins", "dataclasses", "_frozen_importlib", "abc", "chardet.charsetp<PERSON>r", "chardet.mbcharsetp<PERSON>r", "enum", "typing"], "hash": "ca909828a090c1ae1acc7ebf47d357052ced7312", "id": "chardet.mbcsgroupprober", "ignore_all": true, "interface_hash": "5e83ea0c2c9c3ef16df849b6a73124d8787bb540", "mtime": 1748603652, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\chardet\\mbcsgroupprober.py", "plugin_data": null, "size": 2131, "suppressed": [], "version_id": "1.15.0"}