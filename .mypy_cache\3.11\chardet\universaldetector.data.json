{".class": "MypyFile", "_fullname": "chardet.universaldetector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CharSetGroupProber": {".class": "SymbolTableNode", "cross_ref": "chardet.charsetgroupprober.CharSetGroupProber", "kind": "Gdef"}, "CharSetProber": {".class": "SymbolTableNode", "cross_ref": "chardet.charsetprober.CharSetProber", "kind": "Gdef"}, "EscCharSetProber": {".class": "SymbolTableNode", "cross_ref": "chardet.escprober.EscCharSetProber", "kind": "Gdef"}, "InputState": {".class": "SymbolTableNode", "cross_ref": "chardet.enums.InputState", "kind": "Gdef"}, "LanguageFilter": {".class": "SymbolTableNode", "cross_ref": "chardet.enums.LanguageFilter", "kind": "Gdef"}, "Latin1Prober": {".class": "SymbolTableNode", "cross_ref": "chardet.latin1prober.Latin1Prober", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MBCSGroupProber": {".class": "SymbolTableNode", "cross_ref": "chardet.mbcsgroupprober.MBCSGroupProber", "kind": "Gdef"}, "MacRomanProber": {".class": "SymbolTableNode", "cross_ref": "chardet.macromanprober.MacRomanProber", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ProbingState": {".class": "SymbolTableNode", "cross_ref": "chardet.enums.ProbingState", "kind": "Gdef"}, "ResultDict": {".class": "SymbolTableNode", "cross_ref": "chardet.resultdict.ResultDict", "kind": "Gdef"}, "SBCSGroupProber": {".class": "SymbolTableNode", "cross_ref": "chardet.sbcsgroupprober.SBCSGroupProber", "kind": "Gdef"}, "UTF1632Prober": {".class": "SymbolTableNode", "cross_ref": "chardet.utf1632prober.UTF1632Prober", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniversalDetector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "chardet.universaldetector.UniversalDetector", "name": "UniversalDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "chardet.universaldetector.UniversalDetector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "chardet.universaldetector", "mro": ["chardet.universaldetector.UniversalDetector", "builtins.object"], "names": {".class": "SymbolTable", "ESC_DETECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "chardet.universaldetector.UniversalDetector.ESC_DETECTOR", "name": "ESC_DETECTOR", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "HIGH_BYTE_DETECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "chardet.universaldetector.UniversalDetector.HIGH_BYTE_DETECTOR", "name": "HIGH_BYTE_DETECTOR", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "ISO_WIN_MAP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "chardet.universaldetector.UniversalDetector.ISO_WIN_MAP", "name": "ISO_WIN_MAP", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "LEGACY_MAP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "chardet.universaldetector.UniversalDetector.LEGACY_MAP", "name": "LEGACY_MAP", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "MINIMUM_THRESHOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "chardet.universaldetector.UniversalDetector.MINIMUM_THRESHOLD", "name": "MINIMUM_THRESHOLD", "type": "builtins.float"}}, "WIN_BYTE_DETECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "chardet.universaldetector.UniversalDetector.WIN_BYTE_DETECTOR", "name": "WIN_BYTE_DETECTOR", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "lang_filter", "should_rename_legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.universaldetector.UniversalDetector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "lang_filter", "should_rename_legacy"], "arg_types": ["chardet.universaldetector.UniversalDetector", "chardet.enums.LanguageFilter", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniversalDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_charset_probers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector._charset_probers", "name": "_charset_probers", "type": {".class": "Instance", "args": ["chardet.charsetprober.CharSetProber"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_esc_charset_prober": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector._esc_charset_prober", "name": "_esc_charset_prober", "type": {".class": "UnionType", "items": ["chardet.escprober.EscCharSetProber", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_got_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector._got_data", "name": "_got_data", "type": "builtins.bool"}}, "_has_win_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector._has_win_bytes", "name": "_has_win_bytes", "type": "builtins.bool"}}, "_input_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector._input_state", "name": "_input_state", "type": "builtins.int"}}, "_last_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector._last_char", "name": "_last_char", "type": "builtins.bytes"}}, "_utf1632_prober": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector._utf1632_prober", "name": "_utf1632_prober", "type": {".class": "UnionType", "items": ["chardet.utf1632prober.UTF1632Prober", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "charset_probers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.universaldetector.UniversalDetector.charset_probers", "name": "charset_probers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "charset_probers of UniversalDetector", "ret_type": {".class": "Instance", "args": ["chardet.charsetprober.CharSetProber"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.charset_probers", "name": "charset_probers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "charset_probers of UniversalDetector", "ret_type": {".class": "Instance", "args": ["chardet.charsetprober.CharSetProber"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.universaldetector.UniversalDetector.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of UniversalDetector", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "chardet.resultdict.ResultDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.done", "name": "done", "type": "builtins.bool"}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.universaldetector.UniversalDetector.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "byte_str"], "arg_types": ["chardet.universaldetector.UniversalDetector", {".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of UniversalDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_win_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.universaldetector.UniversalDetector.has_win_bytes", "name": "has_win_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_win_bytes of UniversalDetector", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.has_win_bytes", "name": "has_win_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_win_bytes of UniversalDetector", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "input_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "chardet.universaldetector.UniversalDetector.input_state", "name": "input_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input_state of UniversalDetector", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.input_state", "name": "input_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "input_state of UniversalDetector", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "lang_filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.lang_filter", "name": "lang_filter", "type": "chardet.enums.LanguageFilter"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.logger", "name": "logger", "type": "logging.Logger"}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "chardet.universaldetector.UniversalDetector.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["chardet.universaldetector.UniversalDetector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of UniversalDetector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "result": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.result", "name": "result", "type": {".class": "TypeAliasType", "args": [], "type_ref": "chardet.resultdict.ResultDict"}}}, "should_rename_legacy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "chardet.universaldetector.UniversalDetector.should_rename_legacy", "name": "should_rename_legacy", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "chardet.universaldetector.UniversalDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "chardet.universaldetector.UniversalDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.universaldetector.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.universaldetector.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.universaldetector.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.universaldetector.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.universaldetector.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "chardet.universaldetector.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "codecs": {".class": "SymbolTableNode", "cross_ref": "codecs", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\chardet\\universaldetector.py"}