{".class": "MypyFile", "_fullname": "charset_normalizer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CharsetMatch": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.models.CharsetMatch", "kind": "Gdef"}, "CharsetMatches": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.models.CharsetMatches", "kind": "Gdef"}, "VERSION": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.version.VERSION", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "charset_normalizer.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.version.__version__", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "detect": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.legacy.detect", "kind": "Gdef"}, "from_bytes": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.api.from_bytes", "kind": "Gdef"}, "from_fp": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.api.from_fp", "kind": "Gdef"}, "from_path": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.api.from_path", "kind": "Gdef"}, "is_binary": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.api.is_binary", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "set_logging_handler": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.set_logging_handler", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\__init__.py"}