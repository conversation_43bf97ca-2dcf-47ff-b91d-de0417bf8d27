{".class": "MypyFile", "_fullname": "charset_normalizer.api", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef"}, "CharsetMatch": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.models.CharsetMatch", "kind": "Gdef"}, "CharsetMatches": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.models.CharsetMatches", "kind": "Gdef"}, "IANA_SUPPORTED": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.IANA_SUPPORTED", "kind": "Gdef"}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "os.PathLike", "kind": "Gdef"}, "TOO_BIG_SEQUENCE": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.TOO_BIG_SEQUENCE", "kind": "Gdef"}, "TOO_SMALL_SEQUENCE": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.TOO_SMALL_SEQUENCE", "kind": "Gdef"}, "TRACE": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.TRACE", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.api.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "any_specified_encoding": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.any_specified_encoding", "kind": "Gdef"}, "coherence_ratio": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.cd.coherence_ratio", "kind": "Gdef"}, "cut_sequence_chunks": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.cut_sequence_chunks", "kind": "Gdef"}, "encoding_languages": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.cd.encoding_languages", "kind": "Gdef"}, "explain_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "charset_normalizer.api.explain_handler", "name": "explain_handler", "type": {".class": "Instance", "args": ["typing.TextIO"], "extra_attrs": null, "type_ref": "logging.StreamHandler"}}}, "from_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sequences", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.api.from_bytes", "name": "from_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sequences", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_bytes", "ret_type": "charset_normalizer.models.CharsetMatches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_fp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["fp", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.api.from_fp", "name": "from_fp", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["fp", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "arg_types": ["typing.BinaryIO", "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_fp", "ret_type": "charset_normalizer.models.CharsetMatches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["path", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.api.from_path", "name": "from_path", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["path", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_path", "ret_type": "charset_normalizer.models.CharsetMatches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iana_name": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.iana_name", "kind": "Gdef"}, "identify_sig_or_bom": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.identify_sig_or_bom", "kind": "Gdef"}, "is_binary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["fp_or_path_or_payload", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.api.is_binary", "name": "is_binary", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["fp_or_path_or_payload", "steps", "chunk_size", "threshold", "cp_isolation", "cp_exclusion", "preemptive_behaviour", "explain", "language_threshold", "enable_fallback"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, "builtins.str", "typing.BinaryIO", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.float", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_binary", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_cp_similar": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_cp_similar", "kind": "Gdef"}, "is_multi_byte_encoding": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_multi_byte_encoding", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "charset_normalizer.api.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mb_encoding_languages": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.cd.mb_encoding_languages", "kind": "Gdef"}, "merge_coherence_ratios": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.cd.merge_coherence_ratios", "kind": "Gdef"}, "mess_ratio": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.md.mess_ratio", "kind": "Gdef"}, "should_strip_sig_or_bom": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.should_strip_sig_or_bom", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\api.py"}