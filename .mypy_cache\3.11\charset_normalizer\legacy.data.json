{".class": "MypyFile", "_fullname": "charset_normalizer.legacy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CHARDET_CORRESPONDENCE": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.CHARDET_CORRESPONDENCE", "kind": "Gdef"}, "ResultDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.legacy.ResultDict", "name": "ResultDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.legacy.ResultDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.legacy", "mro": ["charset_normalizer.legacy.ResultDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["encoding", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["language", "builtins.str"], ["confidence", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["confidence", "encoding", "language"]}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.legacy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.legacy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.legacy.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.legacy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.legacy.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.legacy.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "detect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["byte_str", "should_rename_legacy", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.legacy.detect", "name": "detect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["byte_str", "should_rename_legacy", "kwargs"], "arg_types": ["builtins.bytes", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "charset_normalizer.legacy.ResultDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_bytes": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.api.from_bytes", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\legacy.py"}