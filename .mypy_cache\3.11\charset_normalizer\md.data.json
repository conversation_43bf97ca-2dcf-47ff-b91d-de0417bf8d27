{".class": "MypyFile", "_fullname": "charset_normalizer.md", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ArabicIsolatedFormPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin", "name": "ArabicIsolatedFormPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.ArabicIsolatedFormPlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArabicIsolatedFormPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ArabicIsolatedFormPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin._character_count", "name": "_character_count", "type": "builtins.int"}}, "_isolated_form_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin._isolated_form_count", "name": "_isolated_form_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.ArabicIsolatedFormPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of ArabicIsolatedFormPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.ArabicIsolatedFormPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of ArabicIsolatedFormPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArabicIsolatedFormPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of ArabicIsolatedFormPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArabicIsolatedFormPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of ArabicIsolatedFormPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArabicIsolatedFormPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of ArabicIsolatedFormPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.ArabicIsolatedFormPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.ArabicIsolatedFormPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ArchaicUpperLowerPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin", "name": "ArchaicUpperLowerPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.ArchaicUpperLowerPlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArchaicUpperLowerPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ArchaicUpperLowerPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_buf": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin._buf", "name": "_buf", "type": "builtins.bool"}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin._character_count", "name": "_character_count", "type": "builtins.int"}}, "_character_count_since_last_sep": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin._character_count_since_last_sep", "name": "_character_count_since_last_sep", "type": "builtins.int"}}, "_current_ascii_only": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin._current_ascii_only", "name": "_current_ascii_only", "type": "builtins.bool"}}, "_last_alpha_seen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin._last_alpha_seen", "name": "_last_alpha_seen", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_successive_upper_lower_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin._successive_upper_lower_count", "name": "_successive_upper_lower_count", "type": "builtins.int"}}, "_successive_upper_lower_count_final": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin._successive_upper_lower_count_final", "name": "_successive_upper_lower_count_final", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.ArchaicUpperLowerPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of ArchaicUpperLowerPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.ArchaicUpperLowerPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of ArchaicUpperLowerPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArchaicUpperLowerPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of ArchaicUpperLowerPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArchaicUpperLowerPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of ArchaicUpperLowerPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.ArchaicUpperLowerPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of ArchaicUpperLowerPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.ArchaicUpperLowerPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.ArchaicUpperLowerPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "COMMON_SAFE_ASCII_CHARACTERS": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.COMMON_SAFE_ASCII_CHARACTERS", "kind": "Gdef"}, "CjkInvalidStopPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.CjkInvalidStopPlugin", "name": "CjkInvalidStopPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.CjkInvalidStopPlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.CjkInvalidStopPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CjkInvalidStopPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cjk_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin._cjk_character_count", "name": "_cjk_character_count", "type": "builtins.int"}}, "_wrong_stop_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin._wrong_stop_count", "name": "_wrong_stop_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.CjkInvalidStopPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of CjkInvalidStopPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.CjkInvalidStopPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of CjkInvalidStopPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.CjkInvalidStopPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of CjkInvalidStopPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.CjkInvalidStopPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of CjkInvalidStopPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.CjkInvalidStopPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.CjkInvalidStopPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of CjkInvalidStopPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.CjkInvalidStopPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.CjkInvalidStopPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MessDetectorPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.MessDetectorPlugin", "name": "MessDetectorPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.MessDetectorPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.MessDetectorPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.MessDetectorPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of MessDetectorPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.MessDetectorPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.MessDetectorPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of MessDetectorPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.MessDetectorPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.MessDetectorPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of MessDetectorPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.MessDetectorPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.MessDetectorPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of MessDetectorPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.MessDetectorPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.MessDetectorPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of MessDetectorPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.MessDetectorPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.MessDetectorPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SuperWeirdWordPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.SuperWeirdWordPlugin", "name": "SuperWeirdWordPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.SuperWeirdWordPlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuperWeirdWordPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SuperWeirdWordPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bad_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._bad_character_count", "name": "_bad_character_count", "type": "builtins.int"}}, "_bad_word_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._bad_word_count", "name": "_bad_word_count", "type": "builtins.int"}}, "_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._buffer", "name": "_buffer", "type": "builtins.str"}}, "_buffer_accent_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._buffer_accent_count", "name": "_buffer_accent_count", "type": "builtins.int"}}, "_buffer_glyph_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._buffer_glyph_count", "name": "_buffer_glyph_count", "type": "builtins.int"}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._character_count", "name": "_character_count", "type": "builtins.int"}}, "_foreign_long_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._foreign_long_count", "name": "_foreign_long_count", "type": "builtins.int"}}, "_foreign_long_watch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._foreign_long_watch", "name": "_foreign_long_watch", "type": "builtins.bool"}}, "_is_current_word_bad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._is_current_word_bad", "name": "_is_current_word_bad", "type": "builtins.bool"}}, "_word_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin._word_count", "name": "_word_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.SuperWeirdWordPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of SuperWeirdWordPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.SuperWeirdWordPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of SuperWeirdWordPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuperWeirdWordPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of SuperWeirdWordPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuperWeirdWordPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of SuperWeirdWordPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuperWeirdWordPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuperWeirdWordPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of SuperWeirdWordPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.SuperWeirdWordPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.SuperWeirdWordPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SuspiciousDuplicateAccentPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin", "name": "SuspiciousDuplicateAccentPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.SuspiciousDuplicateAccentPlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousDuplicateAccentPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SuspiciousDuplicateAccentPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin._character_count", "name": "_character_count", "type": "builtins.int"}}, "_last_latin_character": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin._last_latin_character", "name": "_last_latin_character", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_successive_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin._successive_count", "name": "_successive_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.SuspiciousDuplicateAccentPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of SuspiciousDuplicateAccentPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.SuspiciousDuplicateAccentPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of SuspiciousDuplicateAccentPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousDuplicateAccentPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of SuspiciousDuplicateAccentPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousDuplicateAccentPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of SuspiciousDuplicateAccentPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousDuplicateAccentPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of SuspiciousDuplicateAccentPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.SuspiciousDuplicateAccentPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SuspiciousRange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.SuspiciousRange", "name": "SuspiciousRange", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousRange", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.SuspiciousRange", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousRange.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousRange"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SuspiciousRange", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousRange._character_count", "name": "_character_count", "type": "builtins.int"}}, "_last_printable_seen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousRange._last_printable_seen", "name": "_last_printable_seen", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_suspicious_successive_range_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousRange._suspicious_successive_range_count", "name": "_suspicious_successive_range_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousRange.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.SuspiciousRange", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of SuspiciousRange", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousRange.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.SuspiciousRange", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of SuspiciousRange", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.SuspiciousRange.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousRange"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of SuspiciousRange", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.SuspiciousRange.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousRange"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of SuspiciousRange", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.SuspiciousRange.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.SuspiciousRange"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of Suspicious<PERSON><PERSON>e", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.SuspiciousRange.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.SuspiciousRange", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TRACE": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.TRACE", "kind": "Gdef"}, "TooManyAccentuatedPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin", "name": "TooManyAccentuatedPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.TooManyAccentuatedPlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.Too<PERSON>anyAccentuatedPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManyAccentuatedPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TooManyAccentuatedPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_accentuated_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin._accentuated_count", "name": "_accentuated_count", "type": "builtins.int"}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin._character_count", "name": "_character_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.TooManyAccentuatedPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of TooManyAccentuatedPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.TooManyAccentuatedPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of TooManyAccentuatedPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManyAccentuatedPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of TooManyAccentuatedPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManyAccentuatedPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of TooManyAccentuatedPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManyAccentuatedPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of TooManyAccentuatedPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.TooManyAccentuatedPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.TooManyAccentuatedPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TooManySymbolOrPunctuationPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin", "name": "TooManySymbolOrPunctuationPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.TooManySymbolOrPunctuationPlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManySymbolOrPunctuationPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TooManySymbolOrPunctuationPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin._character_count", "name": "_character_count", "type": "builtins.int"}}, "_frenzy_symbol_in_word": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin._frenzy_symbol_in_word", "name": "_frenzy_symbol_in_word", "type": "builtins.bool"}}, "_last_printable_char": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin._last_printable_char", "name": "_last_printable_char", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_punctuation_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin._punctuation_count", "name": "_punctuation_count", "type": "builtins.int"}}, "_symbol_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin._symbol_count", "name": "_symbol_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.TooManySymbolOrPunctuationPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of TooManySymbolOrPunctuationPlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.TooManySymbolOrPunctuationPlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of TooManySymbolOrPunctuationPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManySymbolOrPunctuationPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of TooManySymbolOrPunctuationPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManySymbolOrPunctuationPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of TooManySymbolOrPunctuationPlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.TooManySymbolOrPunctuationPlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of TooManySymbolOrPunctuationPlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.TooManySymbolOrPunctuationPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UNICODE_SECONDARY_RANGE_KEYWORD": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.constant.UNICODE_SECONDARY_RANGE_KEYWORD", "kind": "Gdef"}, "UnprintablePlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["charset_normalizer.md.MessDetectorPlugin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "charset_normalizer.md.UnprintablePlugin", "name": "UnprintablePlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.UnprintablePlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "charset_normalizer.md", "mro": ["charset_normalizer.md.UnprintablePlugin", "charset_normalizer.md.MessDetectorPlugin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.UnprintablePlugin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.UnprintablePlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnprintablePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_character_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.UnprintablePlugin._character_count", "name": "_character_count", "type": "builtins.int"}}, "_unprintable_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.UnprintablePlugin._unprintable_count", "name": "_unprintable_count", "type": "builtins.int"}}, "eligible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.UnprintablePlugin.eligible", "name": "eligible", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.UnprintablePlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eligible of UnprintablePlugin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "feed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "character"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.UnprintablePlugin.feed", "name": "feed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "character"], "arg_types": ["charset_normalizer.md.UnprintablePlugin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "feed of UnprintablePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "charset_normalizer.md.UnprintablePlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.UnprintablePlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of UnprintablePlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "charset_normalizer.md.UnprintablePlugin.ratio", "name": "ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.UnprintablePlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ratio of UnprintablePlugin", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "charset_normalizer.md.UnprintablePlugin.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["charset_normalizer.md.UnprintablePlugin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of UnprintablePlugin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "charset_normalizer.md.UnprintablePlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "charset_normalizer.md.UnprintablePlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.md.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.md.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.md.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.md.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.md.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "charset_normalizer.md.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "getLogger": {".class": "SymbolTableNode", "cross_ref": "logging.getLogger", "kind": "Gdef"}, "is_accentuated": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_accentuated", "kind": "Gdef"}, "is_arabic": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_arabic", "kind": "Gdef"}, "is_arabic_isolated_form": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_arabic_isolated_form", "kind": "Gdef"}, "is_case_variable": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_case_variable", "kind": "Gdef"}, "is_cjk": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_cjk", "kind": "Gdef"}, "is_emoticon": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_emoticon", "kind": "Gdef"}, "is_hangul": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_hangul", "kind": "Gdef"}, "is_hiragana": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_hiragana", "kind": "Gdef"}, "is_katakana": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_katakana", "kind": "Gdef"}, "is_latin": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_latin", "kind": "Gdef"}, "is_punctuation": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_punctuation", "kind": "Gdef"}, "is_separator": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_separator", "kind": "Gdef"}, "is_suspiciously_successive_range": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["unicode_range_a", "unicode_range_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.md.is_suspiciously_successive_range", "name": "is_suspiciously_successive_range", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["unicode_range_a", "unicode_range_b"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_suspiciously_successive_range", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.md.is_suspiciously_successive_range", "name": "is_suspiciously_successive_range", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "is_symbol": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_symbol", "kind": "Gdef"}, "is_thai": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_thai", "kind": "Gdef"}, "is_unprintable": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.is_unprintable", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "mess_ratio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["decoded_sequence", "maximum_threshold", "debug"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "charset_normalizer.md.mess_ratio", "name": "mess_ratio", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["decoded_sequence", "maximum_threshold", "debug"], "arg_types": ["builtins.str", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mess_ratio", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "charset_normalizer.md.mess_ratio", "name": "mess_ratio", "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "remove_accent": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.remove_accent", "kind": "Gdef"}, "unicode_range": {".class": "SymbolTableNode", "cross_ref": "charset_normalizer.utils.unicode_range", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\charset_normalizer\\md.py"}