{".class": "MypyFile", "_fullname": "cryptography.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlreadyFinalized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.AlreadyFinalized", "name": "AlreadyFinalized", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.AlreadyFinalized", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.AlreadyFinalized", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.AlreadyFinalized.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.AlreadyFinalized", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AlreadyUpdated": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.AlreadyUpdated", "name": "AlreadyUpdated", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.AlreadyUpdated", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.AlreadyUpdated", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.AlreadyUpdated.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.AlreadyUpdated", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InternalError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.InternalError", "name": "InternalError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.InternalError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.InternalError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "err_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.InternalError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "err_code"], "arg_types": ["cryptography.exceptions.InternalError", "builtins.str", {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InternalError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "err_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.exceptions.InternalError.err_code", "name": "err_code", "type": {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.InternalError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.InternalError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.InvalidKey", "name": "Invalid<PERSON>ey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.InvalidKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.InvalidKey", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.InvalidKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.InvalidKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidSignature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.InvalidSignature", "name": "InvalidSignature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.InvalidSignature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.InvalidSignature", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.InvalidSignature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.InvalidSignature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidTag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.InvalidTag", "name": "InvalidTag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.InvalidTag", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.InvalidTag", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.InvalidTag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.InvalidTag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotYetFinalized": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.NotYetFinalized", "name": "NotYetFinalized", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.NotYetFinalized", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.NotYetFinalized", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.NotYetFinalized.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.NotYetFinalized", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.exceptions.UnsupportedAlgorithm", "name": "UnsupportedAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.UnsupportedAlgorithm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.exceptions", "mro": ["cryptography.exceptions.UnsupportedAlgorithm", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.exceptions.UnsupportedAlgorithm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "reason"], "arg_types": ["cryptography.exceptions.UnsupportedAlgorithm", "builtins.str", {".class": "UnionType", "items": ["cryptography.hazmat.bindings._rust.exceptions._Reasons", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnsupportedAlgorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.exceptions.UnsupportedAlgorithm._reason", "name": "_reason", "type": {".class": "UnionType", "items": ["cryptography.hazmat.bindings._rust.exceptions._Reasons", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.exceptions.UnsupportedAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.exceptions.UnsupportedAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Reasons": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.exceptions._Reasons", "line": 14, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.exceptions._Reasons"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "rust_exceptions": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.exceptions", "kind": "Gdef"}, "rust_openssl": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\exceptions.py"}