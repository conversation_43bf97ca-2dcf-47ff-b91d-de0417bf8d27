{".class": "MypyFile", "_fullname": "cryptography.hazmat._oid", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AttributeOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.AttributeOID", "name": "AttributeOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.AttributeOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.AttributeOID", "builtins.object"], "names": {".class": "SymbolTable", "CHALLENGE_PASSWORD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.AttributeOID.CHALLENGE_PASSWORD", "name": "CHALLENGE_PASSWORD", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "UNSTRUCTURED_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.AttributeOID.UNSTRUCTURED_NAME", "name": "UNSTRUCTURED_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.AttributeOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.AttributeOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AuthorityInformationAccessOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.AuthorityInformationAccessOID", "name": "AuthorityInformationAccessOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.AuthorityInformationAccessOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.AuthorityInformationAccessOID", "builtins.object"], "names": {".class": "SymbolTable", "CA_ISSUERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.AuthorityInformationAccessOID.CA_ISSUERS", "name": "CA_ISSUERS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "OCSP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.AuthorityInformationAccessOID.OCSP", "name": "OCSP", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.AuthorityInformationAccessOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.AuthorityInformationAccessOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CRLEntryExtensionOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.CRLEntryExtensionOID", "name": "CRLEntryExtensionOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.CRLEntryExtensionOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.CRLEntryExtensionOID", "builtins.object"], "names": {".class": "SymbolTable", "CERTIFICATE_ISSUER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.CRLEntryExtensionOID.CERTIFICATE_ISSUER", "name": "CERTIFICATE_ISSUER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CRL_REASON": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.CRLEntryExtensionOID.CRL_REASON", "name": "CRL_REASON", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "INVALIDITY_DATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.CRLEntryExtensionOID.INVALIDITY_DATE", "name": "INVALIDITY_DATE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.CRLEntryExtensionOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.CRLEntryExtensionOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CertificatePoliciesOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.CertificatePoliciesOID", "name": "CertificatePoliciesOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.CertificatePoliciesOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.CertificatePoliciesOID", "builtins.object"], "names": {".class": "SymbolTable", "ANY_POLICY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.CertificatePoliciesOID.ANY_POLICY", "name": "ANY_POLICY", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CPS_QUALIFIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.CertificatePoliciesOID.CPS_QUALIFIER", "name": "CPS_QUALIFIER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CPS_USER_NOTICE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.CertificatePoliciesOID.CPS_USER_NOTICE", "name": "CPS_USER_NOTICE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.CertificatePoliciesOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.CertificatePoliciesOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtendedKeyUsageOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID", "name": "ExtendedKeyUsageOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.ExtendedKeyUsageOID", "builtins.object"], "names": {".class": "SymbolTable", "ANY_EXTENDED_KEY_USAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.ANY_EXTENDED_KEY_USAGE", "name": "ANY_EXTENDED_KEY_USAGE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CERTIFICATE_TRANSPARENCY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.CERTIFICATE_TRANSPARENCY", "name": "CERTIFICATE_TRANSPARENCY", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CLIENT_AUTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.CLIENT_AUTH", "name": "CLIENT_AUTH", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CODE_SIGNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.CODE_SIGNING", "name": "CODE_SIGNING", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "EMAIL_PROTECTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.EMAIL_PROTECTION", "name": "EMAIL_PROTECTION", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "IPSEC_IKE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.IPSEC_IKE", "name": "IPSEC_IKE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "KERBEROS_PKINIT_KDC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.KERBEROS_PKINIT_KDC", "name": "KERBEROS_PKINIT_KDC", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "OCSP_SIGNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.OCSP_SIGNING", "name": "OCSP_SIGNING", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SERVER_AUTH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.SERVER_AUTH", "name": "SERVER_AUTH", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SMARTCARD_LOGON": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.SMARTCARD_LOGON", "name": "SMARTCARD_LOGON", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "TIME_STAMPING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.TIME_STAMPING", "name": "TIME_STAMPING", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.ExtendedKeyUsageOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.ExtendedKeyUsageOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtensionOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.ExtensionOID", "name": "ExtensionOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.ExtensionOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.ExtensionOID", "builtins.object"], "names": {".class": "SymbolTable", "ADMISSIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.ADMISSIONS", "name": "ADMISSIONS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "AUTHORITY_INFORMATION_ACCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.AUTHORITY_INFORMATION_ACCESS", "name": "AUTHORITY_INFORMATION_ACCESS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "AUTHORITY_KEY_IDENTIFIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.AUTHORITY_KEY_IDENTIFIER", "name": "AUTHORITY_KEY_IDENTIFIER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "BASIC_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.BASIC_CONSTRAINTS", "name": "BASIC_CONSTRAINTS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CERTIFICATE_POLICIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.CERTIFICATE_POLICIES", "name": "CERTIFICATE_POLICIES", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CRL_DISTRIBUTION_POINTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.CRL_DISTRIBUTION_POINTS", "name": "CRL_DISTRIBUTION_POINTS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "CRL_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.CRL_NUMBER", "name": "CRL_NUMBER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "DELTA_CRL_INDICATOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.DELTA_CRL_INDICATOR", "name": "DELTA_CRL_INDICATOR", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "EXTENDED_KEY_USAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.EXTENDED_KEY_USAGE", "name": "EXTENDED_KEY_USAGE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "FRESHEST_CRL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.FRESHEST_CRL", "name": "FRESHEST_CRL", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "INHIBIT_ANY_POLICY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.INHIBIT_ANY_POLICY", "name": "INHIBIT_ANY_POLICY", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ISSUER_ALTERNATIVE_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.ISSUER_ALTERNATIVE_NAME", "name": "ISSUER_ALTERNATIVE_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ISSUING_DISTRIBUTION_POINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.ISSUING_DISTRIBUTION_POINT", "name": "ISSUING_DISTRIBUTION_POINT", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "KEY_USAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.KEY_USAGE", "name": "KEY_USAGE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "MS_CERTIFICATE_TEMPLATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.MS_CERTIFICATE_TEMPLATE", "name": "MS_CERTIFICATE_TEMPLATE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "NAME_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.NAME_CONSTRAINTS", "name": "NAME_CONSTRAINTS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "OCSP_NO_CHECK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.OCSP_NO_CHECK", "name": "OCSP_NO_CHECK", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "POLICY_CONSTRAINTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.POLICY_CONSTRAINTS", "name": "POLICY_CONSTRAINTS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "POLICY_MAPPINGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.POLICY_MAPPINGS", "name": "POLICY_MAPPINGS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "PRECERT_POISON": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.PRECERT_POISON", "name": "PRECERT_POISON", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "PRECERT_SIGNED_CERTIFICATE_TIMESTAMPS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.PRECERT_SIGNED_CERTIFICATE_TIMESTAMPS", "name": "PRECERT_SIGNED_CERTIFICATE_TIMESTAMPS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SIGNED_CERTIFICATE_TIMESTAMPS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.SIGNED_CERTIFICATE_TIMESTAMPS", "name": "SIGNED_CERTIFICATE_TIMESTAMPS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SUBJECT_ALTERNATIVE_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.SUBJECT_ALTERNATIVE_NAME", "name": "SUBJECT_ALTERNATIVE_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SUBJECT_DIRECTORY_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.SUBJECT_DIRECTORY_ATTRIBUTES", "name": "SUBJECT_DIRECTORY_ATTRIBUTES", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SUBJECT_INFORMATION_ACCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.SUBJECT_INFORMATION_ACCESS", "name": "SUBJECT_INFORMATION_ACCESS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SUBJECT_KEY_IDENTIFIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.SUBJECT_KEY_IDENTIFIER", "name": "SUBJECT_KEY_IDENTIFIER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "TLS_FEATURE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.ExtensionOID.TLS_FEATURE", "name": "TLS_FEATURE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.ExtensionOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.ExtensionOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NameOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.NameOID", "name": "NameOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.NameOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.NameOID", "builtins.object"], "names": {".class": "SymbolTable", "BUSINESS_CATEGORY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.BUSINESS_CATEGORY", "name": "BUSINESS_CATEGORY", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "COMMON_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.COMMON_NAME", "name": "COMMON_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "COUNTRY_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.COUNTRY_NAME", "name": "COUNTRY_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "DN_QUALIFIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.DN_QUALIFIER", "name": "DN_QUALIFIER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "DOMAIN_COMPONENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.DOMAIN_COMPONENT", "name": "DOMAIN_COMPONENT", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "EMAIL_ADDRESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.EMAIL_ADDRESS", "name": "EMAIL_ADDRESS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "GENERATION_QUALIFIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.GENERATION_QUALIFIER", "name": "GENERATION_QUALIFIER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "GIVEN_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.GIVEN_NAME", "name": "GIVEN_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "INITIALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.INITIALS", "name": "INITIALS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "INN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.INN", "name": "INN", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "JURISDICTION_COUNTRY_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.JURISDICTION_COUNTRY_NAME", "name": "JURISDICTION_COUNTRY_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "JURISDICTION_LOCALITY_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.JURISDICTION_LOCALITY_NAME", "name": "JURISDICTION_LOCALITY_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "JURISDICTION_STATE_OR_PROVINCE_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.JURISDICTION_STATE_OR_PROVINCE_NAME", "name": "JURISDICTION_STATE_OR_PROVINCE_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "LOCALITY_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.LOCALITY_NAME", "name": "LOCALITY_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "OGRN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.OGRN", "name": "OGRN", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ORGANIZATIONAL_UNIT_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.ORGANIZATIONAL_UNIT_NAME", "name": "ORGANIZATIONAL_UNIT_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ORGANIZATION_IDENTIFIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.ORGANIZATION_IDENTIFIER", "name": "ORGANIZATION_IDENTIFIER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ORGANIZATION_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.ORGANIZATION_NAME", "name": "ORGANIZATION_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "POSTAL_ADDRESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.POSTAL_ADDRESS", "name": "POSTAL_ADDRESS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "POSTAL_CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.POSTAL_CODE", "name": "POSTAL_CODE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "PSEUDONYM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.PSEUDONYM", "name": "PSEUDONYM", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SERIAL_NUMBER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.SERIAL_NUMBER", "name": "SERIAL_NUMBER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SNILS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.SNILS", "name": "SNILS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "STATE_OR_PROVINCE_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.STATE_OR_PROVINCE_NAME", "name": "STATE_OR_PROVINCE_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "STREET_ADDRESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.STREET_ADDRESS", "name": "STREET_ADDRESS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "SURNAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.SURNAME", "name": "SURNAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "TITLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.TITLE", "name": "TITLE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "UNSTRUCTURED_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.UNSTRUCTURED_NAME", "name": "UNSTRUCTURED_NAME", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "USER_ID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.USER_ID", "name": "USER_ID", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "X500_UNIQUE_IDENTIFIER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.NameOID.X500_UNIQUE_IDENTIFIER", "name": "X500_UNIQUE_IDENTIFIER", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.NameOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.NameOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OCSPExtensionOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.OCSPExtensionOID", "name": "OCSPExtensionOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.OCSPExtensionOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.OCSPExtensionOID", "builtins.object"], "names": {".class": "SymbolTable", "ACCEPTABLE_RESPONSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.OCSPExtensionOID.ACCEPTABLE_RESPONSES", "name": "ACCEPTABLE_RESPONSES", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "NONCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.OCSPExtensionOID.NONCE", "name": "NONCE", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.OCSPExtensionOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.OCSPExtensionOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObjectIdentifier": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.ObjectIdentifier", "kind": "Gdef"}, "PublicKeyAlgorithmOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID", "name": "PublicKeyAlgorithmOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.PublicKeyAlgorithmOID", "builtins.object"], "names": {".class": "SymbolTable", "DSA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.DSA", "name": "DSA", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "EC_PUBLIC_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.EC_PUBLIC_KEY", "name": "EC_PUBLIC_KEY", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ED25519": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.ED25519", "name": "ED25519", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ED448": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.ED448", "name": "ED448", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSAES_PKCS1_v1_5": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.RSAES_PKCS1_v1_5", "name": "RSAES_PKCS1_v1_5", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSASSA_PSS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.RSASSA_PSS", "name": "RSASSA_PSS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "X25519": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.X25519", "name": "X25519", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "X448": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.X448", "name": "X448", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.PublicKeyAlgorithmOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.PublicKeyAlgorithmOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SignatureAlgorithmOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID", "name": "SignatureAlgorithmOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.SignatureAlgorithmOID", "builtins.object"], "names": {".class": "SymbolTable", "DSA_WITH_SHA1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.DSA_WITH_SHA1", "name": "DSA_WITH_SHA1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "DSA_WITH_SHA224": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.DSA_WITH_SHA224", "name": "DSA_WITH_SHA224", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "DSA_WITH_SHA256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.DSA_WITH_SHA256", "name": "DSA_WITH_SHA256", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "DSA_WITH_SHA384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.DSA_WITH_SHA384", "name": "DSA_WITH_SHA384", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "DSA_WITH_SHA512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.DSA_WITH_SHA512", "name": "DSA_WITH_SHA512", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA1", "name": "ECDSA_WITH_SHA1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA224": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA224", "name": "ECDSA_WITH_SHA224", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA256", "name": "ECDSA_WITH_SHA256", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA384", "name": "ECDSA_WITH_SHA384", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA3_224": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA3_224", "name": "ECDSA_WITH_SHA3_224", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA3_256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA3_256", "name": "ECDSA_WITH_SHA3_256", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA3_384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA3_384", "name": "ECDSA_WITH_SHA3_384", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA3_512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA3_512", "name": "ECDSA_WITH_SHA3_512", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ECDSA_WITH_SHA512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ECDSA_WITH_SHA512", "name": "ECDSA_WITH_SHA512", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ED25519": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ED25519", "name": "ED25519", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "ED448": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.ED448", "name": "ED448", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "GOSTR3410_2012_WITH_3411_2012_256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.GOSTR3410_2012_WITH_3411_2012_256", "name": "GOSTR3410_2012_WITH_3411_2012_256", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "GOSTR3410_2012_WITH_3411_2012_512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.GOSTR3410_2012_WITH_3411_2012_512", "name": "GOSTR3410_2012_WITH_3411_2012_512", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "GOSTR3411_94_WITH_3410_2001": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.GOSTR3411_94_WITH_3410_2001", "name": "GOSTR3411_94_WITH_3410_2001", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSASSA_PSS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSASSA_PSS", "name": "RSASSA_PSS", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_MD5": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_MD5", "name": "RSA_WITH_MD5", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA1", "name": "RSA_WITH_SHA1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA224": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA224", "name": "RSA_WITH_SHA224", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA256", "name": "RSA_WITH_SHA256", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA384", "name": "RSA_WITH_SHA384", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA3_224": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA3_224", "name": "RSA_WITH_SHA3_224", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA3_256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA3_256", "name": "RSA_WITH_SHA3_256", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA3_384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA3_384", "name": "RSA_WITH_SHA3_384", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA3_512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA3_512", "name": "RSA_WITH_SHA3_512", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "RSA_WITH_SHA512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.RSA_WITH_SHA512", "name": "RSA_WITH_SHA512", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}, "_RSA_WITH_SHA1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID._RSA_WITH_SHA1", "name": "_RSA_WITH_SHA1", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.SignatureAlgorithmOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.SignatureAlgorithmOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubjectInformationAccessOID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat._oid.SubjectInformationAccessOID", "name": "SubjectInformationAccessOID", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat._oid.SubjectInformationAccessOID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat._oid", "mro": ["cryptography.hazmat._oid.SubjectInformationAccessOID", "builtins.object"], "names": {".class": "SymbolTable", "CA_REPOSITORY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid.SubjectInformationAccessOID.CA_REPOSITORY", "name": "CA_REPOSITORY", "type": "cryptography.hazmat.bindings._rust.ObjectIdentifier"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat._oid.SubjectInformationAccessOID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat._oid.SubjectInformationAccessOID", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_OID_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat._oid._OID_NAMES", "name": "_OID_NAMES", "type": {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.ObjectIdentifier", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SIG_OIDS_TO_HASH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cryptography.hazmat._oid._SIG_OIDS_TO_HASH", "name": "_SIG_OIDS_TO_HASH", "type": {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.ObjectIdentifier", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat._oid.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat._oid.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat._oid.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat._oid.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat._oid.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat._oid.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py"}