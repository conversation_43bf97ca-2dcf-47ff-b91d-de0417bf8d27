{"data_mtime": 1753783516, "dep_lines": [7, 5, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 30, 30, 30], "dependencies": ["cryptography.hazmat.backends.openssl.backend", "__future__", "builtins", "dataclasses", "_frozen_importlib", "abc", "typing"], "hash": "d2269d50185189c27ea14c46fb5696cde643980d", "id": "cryptography.hazmat.backends.openssl", "ignore_all": true, "interface_hash": "96182a4bf2b9d0f749c394d636482990f02e359e", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py", "plugin_data": null, "size": 305, "suppressed": [], "version_id": "1.15.0"}