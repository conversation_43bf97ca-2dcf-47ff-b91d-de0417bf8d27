{"data_mtime": 1753783516, "dep_lines": [7, 8, 11, 12, 13, 22, 25, 7, 8, 9, 10, 11, 19, 9, 5, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 20, 20, 10, 5, 20, 5, 20, 5, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.bindings.openssl.binding", "cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.utils", "cryptography.hazmat.primitives.asymmetric.padding", "cryptography.hazmat.primitives.ciphers.algorithms", "cryptography.hazmat.primitives.ciphers.modes", "cryptography.hazmat.bindings._rust", "cryptography.hazmat.bindings.openssl", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives._asymmetric", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat.primitives.ciphers", "cryptography.hazmat.primitives", "__future__", "builtins", "dataclasses", "_frozen_importlib", "abc", "cryptography.hazmat.bindings", "cryptography.hazmat.primitives._cipheralgorithm", "typing"], "hash": "fe4f01b3f1063b0101b083a77e25ea1a3ca5ab02", "id": "cryptography.hazmat.backends.openssl.backend", "ignore_all": true, "interface_hash": "9a601c447e7b214465d2291e1b498a23f567420b", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py", "plugin_data": null, "size": 9413, "suppressed": [], "version_id": "1.15.0"}