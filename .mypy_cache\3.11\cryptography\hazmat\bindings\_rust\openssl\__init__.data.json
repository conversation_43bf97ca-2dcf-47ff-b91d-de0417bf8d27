{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.openssl", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "CRYPTOGRAPHY_IS_BORINGSSL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.CRYPTOGRAPHY_IS_BORINGSSL", "name": "CRYPTOGRAPHY_IS_BORINGSSL", "type": "builtins.bool"}}, "CRYPTOGRAPHY_IS_LIBRESSL": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.CRYPTOGRAPHY_IS_LIBRESSL", "name": "CRYPTOGRAPHY_IS_LIBRESSL", "type": "builtins.bool"}}, "CRYPTOGRAPHY_OPENSSL_300_OR_GREATER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.CRYPTOGRAPHY_OPENSSL_300_OR_GREATER", "name": "CRYPTOGRAPHY_OPENSSL_300_OR_GREATER", "type": "builtins.bool"}}, "CRYPTOGRAPHY_OPENSSL_309_OR_GREATER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.CRYPTOGRAPHY_OPENSSL_309_OR_GREATER", "name": "CRYPTOGRAPHY_OPENSSL_309_OR_GREATER", "type": "builtins.bool"}}, "CRYPTOGRAPHY_OPENSSL_320_OR_GREATER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.CRYPTOGRAPHY_OPENSSL_320_OR_GREATER", "name": "CRYPTOGRAPHY_OPENSSL_320_OR_GREATER", "type": "builtins.bool"}}, "OpenSSLError": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError", "name": "OpenSSLError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl", "mro": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError", "builtins.object"], "names": {".class": "SymbolTable", "lib": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError.lib", "name": "lib", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lib of OpenSSLError", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError.lib", "name": "lib", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lib of OpenSSLError", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError.reason", "name": "reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reason of OpenSSLError", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError.reason", "name": "reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reason of OpenSSLError", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "reason_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError.reason_text", "name": "reason_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reason_text of OpenSSLError", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError.reason_text", "name": "reason_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reason_text of OpenSSLError", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.OpenSSLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Providers": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.Providers", "name": "Providers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.Providers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl", "mro": ["cryptography.hazmat.bindings._rust.openssl.Providers", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.Providers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.Providers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_legacy_provider_loaded": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl._legacy_provider_loaded", "name": "_legacy_provider_loaded", "type": "builtins.bool"}}, "_providers": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl._providers", "name": "_providers", "type": "cryptography.hazmat.bindings._rust.openssl.Providers"}}, "aead": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.aead", "kind": "Gdef"}, "capture_error_stack": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.capture_error_stack", "name": "capture_error_stack", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capture_error_stack", "ret_type": {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.openssl.OpenSSLError"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ciphers": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.ciphers", "kind": "Gdef"}, "cmac": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.cmac", "kind": "Gdef"}, "dh": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.dh", "kind": "Gdef"}, "dsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.dsa", "kind": "Gdef"}, "ec": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.ec", "kind": "Gdef"}, "ed25519": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.ed25519", "kind": "Gdef"}, "ed448": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.ed448", "kind": "Gdef"}, "enable_fips": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["providers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.enable_fips", "name": "enable_fips", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["providers"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.Providers"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_fips", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.hashes", "kind": "Gdef"}, "hmac": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.hmac", "kind": "Gdef"}, "is_fips_enabled": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.is_fips_enabled", "name": "is_fips_enabled", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_fips_enabled", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kdf": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.kdf", "kind": "Gdef"}, "keys": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.keys", "kind": "Gdef"}, "openssl_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.openssl_version", "name": "openssl_version", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "openssl_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "openssl_version_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.openssl_version_text", "name": "openssl_version_text", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "openssl_version_text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poly1305": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.poly1305", "kind": "Gdef"}, "raise_openssl_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.raise_openssl_error", "name": "raise_openssl_error", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raise_openssl_error", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.rsa", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}, "x25519": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.x25519", "kind": "Gdef"}, "x448": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl.x448", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\__init__.pyi"}