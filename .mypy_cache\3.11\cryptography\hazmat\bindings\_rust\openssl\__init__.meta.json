{"data_mtime": 1753783516, "dep_lines": [7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 5, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.openssl.aead", "cryptography.hazmat.bindings._rust.openssl.ciphers", "cryptography.hazmat.bindings._rust.openssl.cmac", "cryptography.hazmat.bindings._rust.openssl.dh", "cryptography.hazmat.bindings._rust.openssl.dsa", "cryptography.hazmat.bindings._rust.openssl.ec", "cryptography.hazmat.bindings._rust.openssl.ed448", "cryptography.hazmat.bindings._rust.openssl.ed25519", "cryptography.hazmat.bindings._rust.openssl.hashes", "cryptography.hazmat.bindings._rust.openssl.hmac", "cryptography.hazmat.bindings._rust.openssl.kdf", "cryptography.hazmat.bindings._rust.openssl.keys", "cryptography.hazmat.bindings._rust.openssl.poly1305", "cryptography.hazmat.bindings._rust.openssl.rsa", "cryptography.hazmat.bindings._rust.openssl.x448", "cryptography.hazmat.bindings._rust.openssl.x25519", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc", "types"], "hash": "715bcec9e8eadf71c3a5a9cfc92a89972a60ae6f", "id": "cryptography.hazmat.bindings._rust.openssl", "ignore_all": true, "interface_hash": "140cebc31eb9d6da9ac23c1f238f036983834629", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\__init__.pyi", "plugin_data": null, "size": 1410, "suppressed": [], "version_id": "1.15.0"}