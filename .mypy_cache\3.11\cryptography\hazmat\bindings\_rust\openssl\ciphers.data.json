{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AEADDecryptionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADDecryptionContext", "name": "AEADDecryptionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADDecryptionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.ciphers", "mro": ["cryptography.hazmat.bindings._rust.openssl.ciphers.AEADDecryptionContext", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADDecryptionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADDecryptionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AEADEncryptionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADEncryptionContext", "name": "AEADEncryptionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADEncryptionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.ciphers", "mro": ["cryptography.hazmat.bindings._rust.openssl.ciphers.AEADEncryptionContext", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADEncryptionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.ciphers.AEADEncryptionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CipherContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.CipherContext", "name": "CipherContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.CipherContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.ciphers", "mro": ["cryptography.hazmat.bindings._rust.openssl.ciphers.CipherContext", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.CipherContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.ciphers.CipherContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_advance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers._advance", "name": "_advance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "n"], "arg_types": [{".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext"], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_advance", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_advance_aad": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ctx", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers._advance_aad", "name": "_advance_aad", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ctx", "n"], "arg_types": [{".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext"], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_advance_aad", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cipher_supported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.cipher_supported", "name": "cipher_supported", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.Mode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cipher_supported", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ciphers": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.ciphers", "kind": "Gdef", "module_hidden": true, "module_public": false}, "create_decryption_ctx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_decryption_ctx", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_decryption_ctx", "name": "create_decryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_decryption_ctx", "name": "create_decryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_decryption_ctx", "name": "create_decryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.Mode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_decryption_ctx", "name": "create_decryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.Mode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.Mode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_decryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "create_encryption_ctx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_encryption_ctx", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_encryption_ctx", "name": "create_encryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_encryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_encryption_ctx", "name": "create_encryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_encryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_encryption_ctx", "name": "create_encryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.Mode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_encryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.ciphers.create_encryption_ctx", "name": "create_encryption_ctx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.Mode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_encryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.ModeWithAuthenticationTag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_encryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.AEADEncryptionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["algorithm", "mode"], "arg_types": ["cryptography.hazmat.primitives._cipheralgorithm.CipherAlgorithm", "cryptography.hazmat.primitives.ciphers.modes.Mode"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_encryption_ctx", "ret_type": "cryptography.hazmat.primitives.ciphers.base.CipherContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "modes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.ciphers.modes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\ciphers.pyi"}