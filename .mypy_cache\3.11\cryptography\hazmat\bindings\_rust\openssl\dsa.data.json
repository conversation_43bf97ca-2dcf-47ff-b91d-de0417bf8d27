{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.openssl.dsa", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "DSAParameterNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", "name": "DSAParameterNumbers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.dsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "p", "q", "g"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "p", "q", "g"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DSAParameterNumbers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "g": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.g", "name": "g", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "g of DSAParameterNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.g", "name": "g", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "g of DSAParameterNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "p": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.p", "name": "p", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "p of DSAParameterNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.p", "name": "p", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "p of DSAParameterNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.parameters", "name": "parameters", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameters of DSAParameterNumbers", "ret_type": "cryptography.hazmat.primitives.asymmetric.dsa.DSAParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "q": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.q", "name": "q", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "q of DSAParameterNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.q", "name": "q", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "q of DSAParameterNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DSAParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameters", "name": "DSAParameters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameters", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.dsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameters", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DSAPrivateKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateKey", "name": "DSAPrivateKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.dsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateKey", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DSAPrivateNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers", "name": "DSAPrivateNumbers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.dsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "public_numbers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "public_numbers"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers", "builtins.int", "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DSAPrivateNumbers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "private_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers.private_key", "name": "private_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_key of DSAPrivateNumbers", "ret_type": "cryptography.hazmat.primitives.asymmetric.dsa.DSAPrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "public_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of DSAPrivateNumbers", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of DSAPrivateNumbers", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers.x", "name": "x", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "x of DSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers.x", "name": "x", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "x of DSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPrivateNumbers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DSAPublicKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicKey", "name": "DSAPublicKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.dsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicKey", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DSAPublicNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", "name": "DSAPublicNumbers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.dsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "y", "parameter_numbers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "y", "parameter_numbers"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", "builtins.int", "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DSAPublicNumbers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parameter_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers.parameter_numbers", "name": "parameter_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameter_numbers of DSAPublicNumbers", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers.parameter_numbers", "name": "parameter_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameter_numbers of DSAPublicNumbers", "ret_type": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAParameterNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of DSAPublicNumbers", "ret_type": "cryptography.hazmat.primitives.asymmetric.dsa.DSAPublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers.y", "name": "y", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "y of DSAPublicNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers.y", "name": "y", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "y of DSAPublicNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.dsa", "kind": "Gdef", "module_hidden": true, "module_public": false}, "generate_parameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.dsa.generate_parameters", "name": "generate_parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["key_size"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_parameters", "ret_type": "cryptography.hazmat.primitives.asymmetric.dsa.DSAParameters", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\dsa.pyi"}