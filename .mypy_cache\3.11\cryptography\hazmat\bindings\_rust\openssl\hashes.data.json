{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.openssl.hashes", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Hash": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives.hashes.HashContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash", "name": "Hash", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.hashes", "mro": ["cryptography.hazmat.bindings._rust.openssl.hashes.Hash", "cryptography.hazmat.primitives.hashes.HashContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "algorithm", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "algorithm", "backend"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.hashes.Hash", "cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Hash", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "algorithm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash.algorithm", "name": "algorithm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.hashes.Hash"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "algorithm of Hash", "ret_type": "cryptography.hazmat.primitives.hashes.HashAlgorithm", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash.algorithm", "name": "algorithm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.hashes.Hash"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "algorithm of Hash", "ret_type": "cryptography.hazmat.primitives.hashes.HashAlgorithm", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.hashes.Hash"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of <PERSON><PERSON>", "ret_type": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash.finalize", "name": "finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.hashes.Hash"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize of Hash", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.hashes.Hash", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of Hash", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.hashes.Hash", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "hash_supported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.hashes.hash_supported", "name": "hash_supported", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["algorithm"], "arg_types": ["cryptography.hazmat.primitives.hashes.HashAlgorithm"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hash_supported", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\hashes.pyi"}