{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.openssl.keys", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "PrivateKeyTypes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.types.PrivateKeyTypes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "PublicKeyTypes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.types.PublicKeyTypes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "load_der_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["data", "password", "backend", "unsafe_skip_rsa_key_validation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.load_der_private_key", "name": "load_der_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["data", "password", "backend", "unsafe_skip_rsa_key_validation"], "arg_types": ["builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_der_private_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.PrivateKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_der_public_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.load_der_public_key", "name": "load_der_public_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_der_public_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.PublicKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_pem_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["data", "password", "backend", "unsafe_skip_rsa_key_validation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.load_pem_private_key", "name": "load_pem_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["data", "password", "backend", "unsafe_skip_rsa_key_validation"], "arg_types": ["builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_pem_private_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.PrivateKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_pem_public_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.keys.load_pem_public_key", "name": "load_pem_public_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_pem_public_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.PublicKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\keys.pyi"}