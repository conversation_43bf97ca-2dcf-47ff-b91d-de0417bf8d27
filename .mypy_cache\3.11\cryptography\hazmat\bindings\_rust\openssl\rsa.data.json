{".class": "MypyFile", "_fullname": "cryptography.hazmat.bindings._rust.openssl.rsa", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "RSAPrivateKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateKey", "name": "RSAPrivateKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.rsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateKey", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RSAPrivateNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", "name": "RSAPrivateNumbers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.rsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "p", "q", "d", "dmp1", "dmq1", "iqmp", "public_numbers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "p", "q", "d", "dmp1", "dmq1", "iqmp", "public_numbers"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RSAPrivateNumbers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "d": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.d", "name": "d", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "d of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.d", "name": "d", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "d of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dmp1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.dmp1", "name": "dmp1", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dmp1 of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.dmp1", "name": "dmp1", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dmp1 of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dmq1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.dmq1", "name": "dmq1", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dmq1 of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.dmq1", "name": "dmq1", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dmq1 of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "iqmp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.iqmp", "name": "iqmp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iqmp of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.iqmp", "name": "iqmp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iqmp of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "p": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.p", "name": "p", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "p of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.p", "name": "p", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "p of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "backend", "unsafe_skip_rsa_key_validation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.private_key", "name": "private_key", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "backend", "unsafe_skip_rsa_key_validation"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_key of RSAPrivateNumbers", "ret_type": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "public_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of RSAPrivateNumbers", "ret_type": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of RSAPrivateNumbers", "ret_type": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "q": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.q", "name": "q", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "q of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.q", "name": "q", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "q of RSAPrivateNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RSAPublicKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicKey", "name": "RSAPublicKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.rsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicKey", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RSAPublicNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "name": "RSAPublicNumbers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.bindings._rust.openssl.rsa", "mro": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "e", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "e", "n"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RSAPublicNumbers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "e": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers.e", "name": "e", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "e of RSAPublicNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers.e", "name": "e", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "e of RSAPublicNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "n": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers.n", "name": "n", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "n of RSAPublicNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers.n", "name": "n", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "n of RSAPublicNumbers", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "backend"], "arg_types": ["cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of RSAPublicNumbers", "ret_type": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "generate_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["public_exponent", "key_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.bindings._rust.openssl.rsa.generate_private_key", "name": "generate_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["public_exponent", "key_size"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_private_key", "ret_type": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa", "kind": "Gdef", "module_hidden": true, "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\rsa.pyi"}