{"data_mtime": 1753783515, "dep_lines": [5, 5, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.x25519", "cryptography.hazmat.primitives.asymmetric", "builtins", "dataclasses", "_frozen_importlib", "abc", "cryptography.hazmat.primitives", "types", "typing"], "hash": "ccbb66d808aae099cab391b310402f2827f3f4e1", "id": "cryptography.hazmat.bindings._rust.openssl.x25519", "ignore_all": true, "interface_hash": "7f4779e9002d2fff3bf7b7c5f9ae4a369769cf51", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\x25519.pyi", "plugin_data": null, "size": 484, "suppressed": [], "version_id": "1.15.0"}