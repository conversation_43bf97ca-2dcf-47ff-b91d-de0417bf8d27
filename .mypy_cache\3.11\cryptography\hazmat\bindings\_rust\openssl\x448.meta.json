{"data_mtime": 1753783515, "dep_lines": [5, 5, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.x448", "cryptography.hazmat.primitives.asymmetric", "builtins", "dataclasses", "_frozen_importlib", "abc", "cryptography.hazmat.primitives", "types", "typing"], "hash": "3427f42f6bd5d9f79f53ddab8035d4e9be0fc5fd", "id": "cryptography.hazmat.bindings._rust.openssl.x448", "ignore_all": true, "interface_hash": "2a866f2aa520066d50384a3038dc0c7e5ae0408f", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust\\openssl\\x448.pyi", "plugin_data": null, "size": 466, "suppressed": [], "version_id": "1.15.0"}