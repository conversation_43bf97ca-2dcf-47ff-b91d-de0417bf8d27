{".class": "MypyFile", "_fullname": "cryptography.hazmat.primitives._serialization", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BestAvailableEncryption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.BestAvailableEncryption", "name": "BestAvailableEncryption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.BestAvailableEncryption", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.BestAvailableEncryption", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.BestAvailableEncryption.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "password"], "arg_types": ["cryptography.hazmat.primitives._serialization.BestAvailableEncryption", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BestAvailableEncryption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization.BestAvailableEncryption.password", "name": "password", "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.BestAvailableEncryption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.BestAvailableEncryption", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Encoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.Encoding", "name": "Encoding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.hazmat.primitives._serialization.Encoding", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.Encoding", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.Encoding.DER", "name": "DER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "DER"}, "type_ref": "builtins.str"}}}, "OpenSSH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.Encoding.OpenSSH", "name": "OpenSSH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "OpenSSH"}, "type_ref": "builtins.str"}}}, "PEM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.Encoding.PEM", "name": "PEM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "PEM"}, "type_ref": "builtins.str"}}}, "Raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.Encoding.Raw", "name": "Raw", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "Raw"}, "type_ref": "builtins.str"}}}, "SMIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.Encoding.SMIME", "name": "SMIME", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "S/MIME"}, "type_ref": "builtins.str"}}}, "X962": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.Encoding.X962", "name": "X962", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ANSI X9.62"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.Encoding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.Encoding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HashAlgorithm": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes.HashAlgorithm", "kind": "Gdef"}, "KeySerializationEncryption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "name": "KeySerializationEncryption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeySerializationEncryptionBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "name": "KeySerializationEncryptionBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "format", "_kdf_rounds", "_hmac_hash", "_key_cert_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "format", "_kdf_rounds", "_hmac_hash", "_key_cert_algorithm"], "arg_types": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "cryptography.hazmat.primitives._serialization.PrivateFormat", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives._serialization.PBES", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KeySerializationEncryptionBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder._format", "name": "_format", "type": "cryptography.hazmat.primitives._serialization.PrivateFormat"}}, "_hmac_hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder._hmac_hash", "name": "_hmac_hash", "type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_kdf_rounds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder._kdf_rounds", "name": "_kdf_rounds", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_key_cert_algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder._key_cert_algorithm", "name": "_key_cert_algorithm", "type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives._serialization.PBES", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "password"], "arg_types": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of KeySerializationEncryptionBuilder", "ret_type": "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hmac_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder.hmac_hash", "name": "hmac_hash", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "algorithm"], "arg_types": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hmac_hash of KeySerializationEncryptionBuilder", "ret_type": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kdf_rounds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rounds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder.kdf_rounds", "name": "kdf_rounds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rounds"], "arg_types": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kdf_rounds of KeySerializationEncryptionBuilder", "ret_type": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_cert_algorithm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder.key_cert_algorithm", "name": "key_cert_algorithm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "algorithm"], "arg_types": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "cryptography.hazmat.primitives._serialization.PBES"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_cert_algorithm of KeySerializationEncryptionBuilder", "ret_type": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoEncryption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.NoEncryption", "name": "NoEncryption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.NoEncryption", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.NoEncryption", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.NoEncryption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.NoEncryption", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PBES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.PBES", "name": "PBES", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.hazmat.primitives._serialization.PBES", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.PBES", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PBESv1SHA1And3KeyTripleDESCBC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PBES.PBESv1SHA1And3KeyTripleDESCBC", "name": "PBESv1SHA1And3KeyTripleDESCBC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "PBESv1 using SHA1 and 3-Key TripleDES"}, "type_ref": "builtins.str"}}}, "PBESv2SHA256AndAES256CBC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PBES.PBESv2SHA256AndAES256CBC", "name": "PBESv2SHA256AndAES256CBC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "PBESv2 using SHA256 PBKDF2 and AES256 CBC"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.PBES.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.PBES", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParameterFormat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.ParameterFormat", "name": "ParameterFormat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.hazmat.primitives._serialization.ParameterFormat", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.ParameterFormat", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "PKCS3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.ParameterFormat.PKCS3", "name": "PKCS3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "PKCS3"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.ParameterFormat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.ParameterFormat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PrivateFormat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat", "name": "PrivateFormat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.PrivateFormat", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "OpenSSH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat.OpenSSH", "name": "OpenSSH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "OpenSSH"}, "type_ref": "builtins.str"}}}, "PKCS12": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat.PKCS12", "name": "PKCS12", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "PKCS12"}, "type_ref": "builtins.str"}}}, "PKCS8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat.PKCS8", "name": "PKCS8", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "PKCS8"}, "type_ref": "builtins.str"}}}, "Raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat.Raw", "name": "Raw", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "Raw"}, "type_ref": "builtins.str"}}}, "TraditionalOpenSSL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat.TraditionalOpenSSL", "name": "TraditionalOpenSSL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TraditionalOpenSSL"}, "type_ref": "builtins.str"}}}, "encryption_builder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat.encryption_builder", "name": "encryption_builder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives._serialization.PrivateFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encryption_builder of PrivateFormat", "ret_type": "cryptography.hazmat.primitives._serialization.KeySerializationEncryptionBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.PrivateFormat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.PrivateFormat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PublicFormat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat", "name": "PublicFormat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization.PublicFormat", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CompressedPoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat.CompressedPoint", "name": "CompressedPoint", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "X9.62 Compressed Point"}, "type_ref": "builtins.str"}}}, "OpenSSH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat.OpenSSH", "name": "OpenSSH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "OpenSSH"}, "type_ref": "builtins.str"}}}, "PKCS1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat.PKCS1", "name": "PKCS1", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "Raw PKCS#1"}, "type_ref": "builtins.str"}}}, "Raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat.Raw", "name": "Raw", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "Raw"}, "type_ref": "builtins.str"}}}, "SubjectPublicKeyInfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat.SubjectPublicKeyInfo", "name": "SubjectPublicKeyInfo", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "X.509 subjectPublicKeyInfo with PKCS#1"}, "type_ref": "builtins.str"}}}, "UncompressedPoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat.UncompressedPoint", "name": "UncompressedPoint", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "X9.62 Uncompressed Point"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization.PublicFormat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization.PublicFormat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_KeySerializationEncryption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption", "name": "_KeySerializationEncryption", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives._serialization", "mro": ["cryptography.hazmat.primitives._serialization._KeySerializationEncryption", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3, 3], "arg_names": ["self", "format", "password", "kdf_rounds", "hmac_hash", "key_cert_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3, 3], "arg_names": ["self", "format", "password", "kdf_rounds", "hmac_hash", "key_cert_algorithm"], "arg_types": ["cryptography.hazmat.primitives._serialization._KeySerializationEncryption", "cryptography.hazmat.primitives._serialization.PrivateFormat", "builtins.bytes", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives._serialization.PBES", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _KeySerializationEncryption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption._format", "name": "_format", "type": "cryptography.hazmat.primitives._serialization.PrivateFormat"}}, "_hmac_hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption._hmac_hash", "name": "_hmac_hash", "type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_kdf_rounds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption._kdf_rounds", "name": "_kdf_rounds", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_key_cert_algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption._key_cert_algorithm", "name": "_key_cert_algorithm", "type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives._serialization.PBES", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption.password", "name": "password", "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives._serialization.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives._serialization.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives._serialization.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives._serialization.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives._serialization.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives._serialization.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.utils", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py"}