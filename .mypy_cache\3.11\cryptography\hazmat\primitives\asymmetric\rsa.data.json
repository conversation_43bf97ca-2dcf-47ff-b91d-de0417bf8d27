{".class": "MypyFile", "_fullname": "cryptography.hazmat.primitives.asymmetric.rsa", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsymmetricPadding": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding", "kind": "Gdef"}, "RSAPrivateKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["decrypt", 1], ["key_size", 1], ["private_bytes", 1], ["private_numbers", 1], ["public_key", 1], ["sign", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "name": "RSAPrivateKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.rsa", "mro": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "builtins.object"], "names": {".class": "SymbolTable", "decrypt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ciphertext", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.decrypt", "name": "decrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ciphertext", "padding"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decrypt of RSAPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.decrypt", "name": "decrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ciphertext", "padding"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decrypt of RSAPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of RSAPrivateKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of RSAPrivateKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.private_bytes", "name": "private_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PrivateFormat", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_bytes of RSAPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.private_bytes", "name": "private_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "encoding", "format", "encryption_algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PrivateFormat", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_bytes of RSAPrivateKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.private_numbers", "name": "private_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_numbers of RSAPrivateKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.private_numbers", "name": "private_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "private_numbers of RSAPrivateKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of RSAPrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of RSAPrivateKey", "ret_type": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "padding", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "padding", "algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of RSAPrivate<PERSON>ey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "data", "padding", "algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of RSAPrivate<PERSON>ey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RSAPrivateKeyWithSerialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKeyWithSerialization", "line": 67, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"}}, "RSAPrivateNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateNumbers", "line": 134, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPrivateNumbers"}}, "RSAPublicKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__eq__", 1], ["encrypt", 1], ["key_size", 1], ["public_bytes", 1], ["public_numbers", 1], ["recover_data_from_signature", 1], ["verify", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "name": "RSAPublicKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "cryptography.hazmat.primitives.asymmetric.rsa", "mro": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of RSAPublicKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of RSAPublicKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "encrypt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "plaintext", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.encrypt", "name": "encrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plaintext", "padding"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encrypt of RSAPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.encrypt", "name": "encrypt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "plaintext", "padding"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encrypt of RSAPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of RSAPublicKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.key_size", "name": "key_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_size of RSAPublicKey", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PublicFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of RSAPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "format"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "cryptography.hazmat.primitives._serialization.Encoding", "cryptography.hazmat.primitives._serialization.PublicFormat"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of RSAPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_numbers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of RSAPublicKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.public_numbers", "name": "public_numbers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_numbers of RSAPublicKey", "ret_type": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "recover_data_from_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signature", "padding", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.recover_data_from_signature", "name": "recover_data_from_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signature", "padding", "algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recover_data_from_signature of RSAPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.recover_data_from_signature", "name": "recover_data_from_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "signature", "padding", "algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "recover_data_from_signature of RSAPublicKey", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "signature", "data", "padding", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "signature", "data", "padding", "algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of RSAPublicKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "signature", "data", "padding", "algorithm"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes", "builtins.bytes", "cryptography.hazmat.primitives._asymmetric.AsymmetricPadding", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.utils.Prehashed", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of RSAPublicKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RSAPublicKeyWithSerialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKeyWithSerialization", "line": 131, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey"}}, "RSAPublicNumbers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicNumbers", "line": 135, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.openssl.rsa.RSAPublicNumbers"}}, "_MAX_RECOVERY_ATTEMPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa._MAX_RECOVERY_ATTEMPTS", "name": "_MAX_RECOVERY_ATTEMPTS", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_modinv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["e", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa._modinv", "name": "_modinv", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["e", "m"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_modinv", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialization": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization", "kind": "Gdef"}, "_verify_rsa_parameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["public_exponent", "key_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa._verify_rsa_parameters", "name": "_verify_rsa_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["public_exponent", "key_size"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_rsa_parameters", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asym_utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.utils", "kind": "Gdef"}, "gcd": {".class": "SymbolTableNode", "cross_ref": "math.gcd", "kind": "Gdef"}, "generate_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["public_exponent", "key_size", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.generate_private_key", "name": "generate_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["public_exponent", "key_size", "backend"], "arg_types": ["builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_private_key", "ret_type": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "rsa_crt_dmp1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["private_exponent", "p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_crt_dmp1", "name": "rsa_crt_dmp1", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["private_exponent", "p"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsa_crt_dmp1", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rsa_crt_dmq1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["private_exponent", "q"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_crt_dmq1", "name": "rsa_crt_dmq1", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["private_exponent", "q"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsa_crt_dmq1", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rsa_crt_iqmp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["p", "q"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_crt_iqmp", "name": "rsa_crt_iqmp", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["p", "q"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsa_crt_iqmp", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rsa_recover_prime_factors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["n", "e", "d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_recover_prime_factors", "name": "rsa_recover_prime_factors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["n", "e", "d"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsa_recover_prime_factors", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rsa_recover_private_exponent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["e", "p", "q"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_recover_private_exponent", "name": "rsa_recover_private_exponent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["e", "p", "q"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rsa_recover_private_exponent", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rust_openssl": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.openssl", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py"}