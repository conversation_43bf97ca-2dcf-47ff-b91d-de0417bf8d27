{"data_mtime": 1753783516, "dep_lines": [5, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.openssl", "cryptography.hazmat.bindings._rust", "builtins", "dataclasses", "_frozen_importlib", "abc", "cryptography.hazmat.bindings", "cryptography.hazmat.bindings._rust.openssl.dh", "cryptography.hazmat.bindings._rust.openssl.keys", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat.primitives.asymmetric.dh", "cryptography.hazmat.primitives.asymmetric.dsa", "cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.ed25519", "cryptography.hazmat.primitives.asymmetric.ed448", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.asymmetric.x25519", "cryptography.hazmat.primitives.asymmetric.x448", "typing"], "hash": "7048b383ea6113f66ae41e5288682645f80f0fe9", "id": "cryptography.hazmat.primitives.serialization.base", "ignore_all": true, "interface_hash": "4e62673202008ecf88a89e793e0b509dc49f1dc7", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py", "plugin_data": null, "size": 615, "suppressed": [], "version_id": "1.15.0"}