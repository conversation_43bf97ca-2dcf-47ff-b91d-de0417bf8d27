{".class": "MypyFile", "_fullname": "cryptography.hazmat.primitives.serialization.ssh", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AEADDecryptionContext": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.ciphers.base.AEADDecryptionContext", "kind": "Gdef"}, "Cipher": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher", "kind": "Gdef"}, "Encoding": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.Encoding", "kind": "Gdef"}, "KeySerializationEncryption": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.KeySerializationEncryption", "kind": "Gdef"}, "NoEncryption": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.NoEncryption", "kind": "Gdef"}, "PrivateFormat": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.PrivateFormat", "kind": "Gdef"}, "PublicFormat": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.PublicFormat", "kind": "Gdef"}, "SSHCertPrivateKeyTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPrivateKeyTypes", "line": 1164, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey"], "uses_pep604_syntax": false}}}, "SSHCertPublicKeyTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPublicKeyTypes", "line": 852, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey"], "uses_pep604_syntax": false}}}, "SSHCertificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", "name": "SSHCertificate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "_nonce", "_public_key", "_serial", "_cctype", "_key_id", "_valid_principals", "_valid_after", "_valid_before", "_critical_options", "_extensions", "_sig_type", "_sig_key", "_inner_sig_type", "_signature", "_tbs_cert_body", "_cert_key_type", "_cert_body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "_nonce", "_public_key", "_serial", "_cctype", "_key_id", "_valid_principals", "_valid_after", "_valid_before", "_critical_options", "_extensions", "_sig_type", "_sig_key", "_inner_sig_type", "_signature", "_tbs_cert_body", "_cert_key_type", "_cert_body"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes"}, "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "builtins.bytes", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSHCertificate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cert_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._cert_body", "name": "_cert_body", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_cert_key_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._cert_key_type", "name": "_cert_key_type", "type": "builtins.bytes"}}, "_critical_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._critical_options", "name": "_critical_options", "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._extensions", "name": "_extensions", "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_inner_sig_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._inner_sig_type", "name": "_inner_sig_type", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._key_id", "name": "_key_id", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_nonce": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._nonce", "name": "_nonce", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_public_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._public_key", "name": "_public_key", "type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes"}}}, "_serial": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._serial", "name": "_serial", "type": "builtins.int"}}, "_sig_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._sig_key", "name": "_sig_key", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_sig_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._sig_type", "name": "_sig_type", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_signature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._signature", "name": "_signature", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_tbs_cert_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._tbs_cert_body", "name": "_tbs_cert_body", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._type", "name": "_type", "type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType"}}, "_valid_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._valid_after", "name": "_valid_after", "type": "builtins.int"}}, "_valid_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._valid_before", "name": "_valid_before", "type": "builtins.int"}}, "_valid_principals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate._valid_principals", "name": "_valid_principals", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "critical_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.critical_options", "name": "critical_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "critical_options of SSHCertificate", "ret_type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.critical_options", "name": "critical_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "critical_options of SSHCertificate", "ret_type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "extensions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.extensions", "name": "extensions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extensions of SSHCertificate", "ret_type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.extensions", "name": "extensions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extensions of SSHCertificate", "ret_type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "key_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.key_id", "name": "key_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_id of SSHCertificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.key_id", "name": "key_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_id of SSHCertificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "nonce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.nonce", "name": "nonce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nonce of SSHCertificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.nonce", "name": "nonce", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nonce of SSHCertificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "public_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.public_bytes", "name": "public_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_bytes of SSHCertificate", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of SSHCertificate", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPublicKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serial": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.serial", "name": "serial", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serial of SSHCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.serial", "name": "serial", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serial of SSHCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "signature_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.signature_key", "name": "signature_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature_key of SSHCertificate", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPublicKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of SSHCertificate", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of SSHCertificate", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "valid_after": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.valid_after", "name": "valid_after", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_after of SSHCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.valid_after", "name": "valid_after", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_after of SSHCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "valid_before": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.valid_before", "name": "valid_before", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_before of SSHCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.valid_before", "name": "valid_before", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_before of SSHCertificate", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "valid_principals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.valid_principals", "name": "valid_principals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_principals of SSHCertificate", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.valid_principals", "name": "valid_principals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_principals of SSHCertificate", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "verify_cert_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.verify_cert_signature", "name": "verify_cert_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_cert_signature of SSHCertificate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSHCertificateBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "name": "SSHCertificateBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "_public_key", "_serial", "_type", "_key_id", "_valid_principals", "_valid_for_all_principals", "_valid_before", "_valid_after", "_critical_options", "_extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "_public_key", "_serial", "_type", "_key_id", "_valid_principals", "_valid_for_all_principals", "_valid_before", "_valid_after", "_critical_options", "_extensions"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPublicKeyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSHCertificateBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_critical_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._critical_options", "name": "_critical_options", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._extensions", "name": "_extensions", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._key_id", "name": "_key_id", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_public_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._public_key", "name": "_public_key", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPublicKeyTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_serial": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._serial", "name": "_serial", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._type", "name": "_type", "type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_valid_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._valid_after", "name": "_valid_after", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_valid_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._valid_before", "name": "_valid_before", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_valid_for_all_principals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._valid_for_all_principals", "name": "_valid_for_all_principals", "type": "builtins.bool"}}, "_valid_principals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder._valid_principals", "name": "_valid_principals", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "add_critical_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.add_critical_option", "name": "add_critical_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_critical_option of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.add_extension", "name": "add_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_extension of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.key_id", "name": "key_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key_id"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_id of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "public_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "public_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.public_key", "name": "public_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "public_key"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPublicKeyTypes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "public_key of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "serial": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "serial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.serial", "name": "serial", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "serial"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serial of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "private_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "private_key"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHCertPrivateKeyTypes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of SSHCertificateB<PERSON><PERSON>", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "valid_after": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "valid_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.valid_after", "name": "valid_after", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "valid_after"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_after of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "valid_before": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "valid_before"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.valid_before", "name": "valid_before", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "valid_before"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_before of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "valid_for_all_principals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.valid_for_all_principals", "name": "valid_for_all_principals", "type": null}}, "valid_principals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "valid_principals"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.valid_principals", "name": "valid_principals", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "valid_principals"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "valid_principals of SSHCertificateBuilder", "ret_type": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSHCertificateType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", "name": "SSHCertificateType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "HOST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType.HOST", "name": "HOST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "USER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType.USER", "name": "USER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh.SSHCertificateType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SSHPrivateKeyTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHPrivateKeyTypes", "line": 656, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "cryptography.hazmat.primitives.asymmetric.dsa.DSAPrivateKey", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey"], "uses_pep604_syntax": false}}}, "SSHPublicKeyTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes", "line": 845, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "cryptography.hazmat.primitives.asymmetric.dsa.DSAPublicKey", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey"], "uses_pep604_syntax": false}}}, "UnsupportedAlgorithm": {".class": "SymbolTableNode", "cross_ref": "cryptography.exceptions.UnsupportedAlgorithm", "kind": "Gdef"}, "_BCRYPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._BCRYPT", "name": "_BCRYPT", "type": "builtins.bytes"}}, "_CERT_SUFFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._CERT_SUFFIX", "name": "_CERT_SUFFIX", "type": "builtins.bytes"}}, "_DEFAULT_CIPHER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._DEFAULT_CIPHER", "name": "_DEFAULT_CIPHER", "type": "builtins.bytes"}}, "_DEFAULT_ROUNDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._DEFAULT_ROUNDS", "name": "_DEFAULT_ROUNDS", "type": "builtins.int"}}, "_ECDSA_KEY_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._ECDSA_KEY_TYPE", "name": "_ECDSA_KEY_TYPE", "type": {".class": "Instance", "args": ["builtins.str", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_ECDSA_NISTP256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._ECDSA_NISTP256", "name": "_ECDSA_NISTP256", "type": "builtins.bytes"}}, "_ECDSA_NISTP384": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._ECDSA_NISTP384", "name": "_ECDSA_NISTP384", "type": "builtins.bytes"}}, "_ECDSA_NISTP521": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._ECDSA_NISTP521", "name": "_ECDSA_NISTP521", "type": "builtins.bytes"}}, "_FragList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList", "name": "_FragList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._FragList", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "init"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "init"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _FragList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.flist", "name": "flist", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "put_mpint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.put_mpint", "name": "put_mpint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_mpint of _FragList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.put_raw", "name": "put_raw", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_raw of _FragList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_sshstr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.put_sshstr", "name": "put_sshstr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList", {".class": "UnionType", "items": ["builtins.bytes", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_sshstr of _FragList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_u32": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.put_u32", "name": "put_u32", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_u32 of _FragList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_u64": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.put_u64", "name": "put_u64", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_u64 of _FragList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dstbuf", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.render", "name": "render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dstbuf", "pos"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render of _FragList", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of _FragList", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tobytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.tobytes", "name": "tobytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tobytes of _FragList", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._FragList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._FragList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_KEY_FORMATS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._KEY_FORMATS", "name": "_KEY_FORMATS", "type": {".class": "Instance", "args": ["builtins.bytes", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_KeySerializationEncryption": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization._KeySerializationEncryption", "kind": "Gdef"}, "_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._NONE", "name": "_NONE", "type": "builtins.bytes"}}, "_PADDING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._PADDING", "name": "_PADDING", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "_PEM_RC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._PEM_RC", "name": "_PEM_RC", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_SK_END": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SK_END", "name": "_SK_END", "type": "builtins.bytes"}}, "_SK_MAGIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SK_MAGIC", "name": "_SK_MAGIC", "type": "builtins.bytes"}}, "_SK_SSH_ECDSA_NISTP256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SK_SSH_ECDSA_NISTP256", "name": "_SK_SSH_ECDSA_NISTP256", "type": "builtins.bytes"}}, "_SK_SSH_ED25519": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SK_SSH_ED25519", "name": "_SK_SSH_ED25519", "type": "builtins.bytes"}}, "_SK_START": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SK_START", "name": "_SK_START", "type": "builtins.bytes"}}, "_SSHCipher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher", "name": "_SSHCipher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 94, "name": "alg", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.algorithms.AES"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 95, "name": "key_len", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 96, "name": "mode", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CTR"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CBC"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.GCM"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 97, "name": "block_len", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 98, "name": "iv_len", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 99, "name": "tag_len", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 100, "name": "is_aead", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._SSHCipher", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "alg", "key_len", "mode", "block_len", "iv_len", "tag_len", "is_aead"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "alg", "key_len", "mode", "block_len", "iv_len", "tag_len", "is_aead"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHCipher", {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.algorithms.AES"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CTR"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CBC"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.GCM"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _SSHCipher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "alg"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_len"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "block_len"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "iv_len"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tag_len"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_aead"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["alg", "key_len", "mode", "block_len", "iv_len", "tag_len", "is_aead"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["alg", "key_len", "mode", "block_len", "iv_len", "tag_len", "is_aead"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.algorithms.AES"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CTR"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CBC"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.GCM"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _SSHCipher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["alg", "key_len", "mode", "block_len", "iv_len", "tag_len", "is_aead"], "arg_types": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.algorithms.AES"}, "builtins.int", {".class": "UnionType", "items": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CTR"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CBC"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.GCM"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of _SSHCipher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "alg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.alg", "name": "alg", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.algorithms.AES"}}}, "block_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.block_len", "name": "block_len", "type": "builtins.int"}}, "is_aead": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.is_aead", "name": "is_aead", "type": "builtins.bool"}}, "iv_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.iv_len", "name": "iv_len", "type": "builtins.int"}}, "key_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.key_len", "name": "key_len", "type": "builtins.int"}}, "mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.mode", "name": "mode", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CTR"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.CBC"}, {".class": "TypeType", "item": "cryptography.hazmat.primitives.ciphers.modes.GCM"}], "uses_pep604_syntax": true}}}, "tag_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.tag_len", "name": "tag_len", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._SSHCipher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SSHFormatDSA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", "name": "_SSHFormatDSA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", "builtins.object"], "names": {".class": "SymbolTable", "_validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "public_numbers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA._validate", "name": "_validate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "public_numbers"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", "cryptography.hazmat.bindings._rust.openssl.dsa.DSAPublicNumbers"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate of _SSHFormatDSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA.encode_private", "name": "encode_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", "cryptography.hazmat.primitives.asymmetric.dsa.DSAPrivateKey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_private of _SSHFormatDSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA.encode_public", "name": "encode_public", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", "cryptography.hazmat.primitives.asymmetric.dsa.DSAPublicKey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_public of _SSHFormatDSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA.get_public", "name": "get_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_public of _SSHFormatDSA", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA.load_private", "name": "load_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_private of _SSHFormatDSA", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.dsa.DSAPrivateKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA.load_public", "name": "load_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_public of _SSHFormatDSA", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.dsa.DSAPublicKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatDSA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SSHFormatECDSA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", "name": "_SSHFormatECDSA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ssh_curve_name", "curve"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ssh_curve_name", "curve"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _SSHFormatECDSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "curve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.curve", "name": "curve", "type": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"}}, "encode_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.encode_private", "name": "encode_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_private of _SSHFormatECDSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.encode_public", "name": "encode_public", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_public of _SSHFormatECDSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.get_public", "name": "get_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_public of _SSHFormatECDSA", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.load_private", "name": "load_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_private of _SSHFormatECDSA", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.load_public", "name": "load_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_public of _SSHFormatECDSA", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssh_curve_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.ssh_curve_name", "name": "ssh_curve_name", "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatECDSA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SSHFormatEd25519": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", "name": "_SSHFormatEd25519", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", "builtins.object"], "names": {".class": "SymbolTable", "encode_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519.encode_private", "name": "encode_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_private of _SSHFormatEd25519", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519.encode_public", "name": "encode_public", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_public of _SSHFormatEd25519", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519.get_public", "name": "get_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_public of _SSHFormatEd25519", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519.load_private", "name": "load_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_private of _SSHFormatEd25519", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519.load_public", "name": "load_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_public of _SSHFormatEd25519", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatEd25519", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SSHFormatRSA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", "name": "_SSHFormatRSA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", "builtins.object"], "names": {".class": "SymbolTable", "encode_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA.encode_private", "name": "encode_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "f_priv"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_private of _SSHFormatRSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA.encode_public", "name": "encode_public", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "public_key", "f_pub"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "cryptography.hazmat.primitives.serialization.ssh._FragList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_public of _SSHFormatRSA", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA.get_public", "name": "get_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_public of _SSHFormatRSA", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_private": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA.load_private", "name": "load_private", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "pubfields"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_private of _SSHFormatRSA", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA.load_public", "name": "load_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_public of _SSHFormatRSA", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatRSA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SSHFormatSKECDSA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKECDSA", "name": "_SSHFormatSKECDSA", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKECDSA", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKECDSA", "builtins.object"], "names": {".class": "SymbolTable", "load_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKECDSA.load_public", "name": "load_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKECDSA", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_public of _SSHFormatSKECDSA", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKECDSA.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKECDSA", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SSHFormatSKEd25519": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKEd25519", "name": "_SSHFormatSKEd25519", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKEd25519", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.hazmat.primitives.serialization.ssh", "mro": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKEd25519", "builtins.object"], "names": {".class": "SymbolTable", "load_public": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKEd25519.load_public", "name": "load_public", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKEd25519", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_public of _SSHFormatSKEd25519", "ret_type": {".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKEd25519.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.hazmat.primitives.serialization.ssh._SSHFormatSKEd25519", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SSHKEY_CERT_MAX_PRINCIPALS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSHKEY_CERT_MAX_PRINCIPALS", "name": "_SSHKEY_CERT_MAX_PRINCIPALS", "type": "builtins.int"}}, "_SSH_CIPHERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSH_CIPHERS", "name": "_SSH_CIPHERS", "type": {".class": "Instance", "args": ["builtins.bytes", "cryptography.hazmat.primitives.serialization.ssh._SSHCipher"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_SSH_DSA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSH_DSA", "name": "_SSH_DSA", "type": "builtins.bytes"}}, "_SSH_ED25519": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSH_ED25519", "name": "_SSH_ED25519", "type": "builtins.bytes"}}, "_SSH_PUBKEY_RC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSH_PUBKEY_RC", "name": "_SSH_PUBKEY_RC", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_SSH_RSA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSH_RSA", "name": "_SSH_RSA", "type": "builtins.bytes"}}, "_SSH_RSA_SHA256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSH_RSA_SHA256", "name": "_SSH_RSA_SHA256", "type": "builtins.bytes"}}, "_SSH_RSA_SHA512": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._SSH_RSA_SHA512", "name": "_SSH_RSA_SHA512", "type": "builtins.bytes"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.hazmat.primitives.serialization.ssh.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_base64_encode": {".class": "SymbolTableNode", "cross_ref": "base64.encodebytes", "kind": "Gdef"}, "_bcrypt_kdf": {".class": "SymbolTableNode", "cross_ref": "bcrypt.kdf", "kind": "Gdef"}, "_bcrypt_supported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.hazmat.primitives.serialization.ssh._bcrypt_supported", "name": "_bcrypt_supported", "type": "builtins.bool"}}, "_check_block_size": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["data", "block_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._check_block_size", "name": "_check_block_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["data", "block_len"], "arg_types": ["builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_block_size", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_empty": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._check_empty", "name": "_check_empty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_empty", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ecdsa_key_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["public_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._ecdsa_key_type", "name": "_ecdsa_key_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["public_key"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ecdsa_key_type", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ec_hash_alg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["curve"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._get_ec_hash_alg", "name": "_get_ec_hash_alg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["curve"], "arg_types": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ec_hash_alg", "ret_type": "cryptography.hazmat.primitives.hashes.HashAlgorithm", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_mpint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._get_mpint", "name": "_get_mpint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_mpint", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ssh_key_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._get_ssh_key_type", "name": "_get_ssh_key_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["key"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPrivateKeyTypes"}, {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ssh_key_type", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_sshstr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._get_sshstr", "name": "_get_sshstr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_sshstr", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_u32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._get_u32", "name": "_get_u32", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_u32", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_u64": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._get_u64", "name": "_get_u64", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_u64", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_cipher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["ciphername", "password", "salt", "rounds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._init_cipher", "name": "_init_cipher", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["ciphername", "password", "salt", "rounds"], "arg_types": ["builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_cipher", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["cryptography.hazmat.primitives.ciphers.modes.CBC", "cryptography.hazmat.primitives.ciphers.modes.CTR", "cryptography.hazmat.primitives.ciphers.modes.GCM"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cryptography.hazmat.primitives.ciphers.base.Cipher"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_ssh_public_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "_legacy_dsa_allowed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._load_ssh_public_identity", "name": "_load_ssh_public_identity", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "_legacy_dsa_allowed"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_ssh_public_identity", "ret_type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lookup_kformat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._lookup_kformat", "name": "_lookup_kformat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["key_type"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lookup_kformat", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_exts_opts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["exts_opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._parse_exts_opts", "name": "_parse_exts_opts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["exts_opts"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_exts_opts", "ret_type": {".class": "Instance", "args": ["builtins.bytes", "builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_serialize_ssh_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["private_key", "password", "encryption_algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._serialize_ssh_private_key", "name": "_serialize_ssh_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["private_key", "password", "encryption_algorithm"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPrivateKeyTypes"}, "builtins.bytes", "cryptography.hazmat.primitives._serialization.KeySerializationEncryption"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_serialize_ssh_private_key", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ssh_pem_encode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["data", "prefix", "suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._ssh_pem_encode", "name": "_ssh_pem_encode", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["data", "prefix", "suffix"], "arg_types": ["builtins.bytes", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssh_pem_encode", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_to_mpint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh._to_mpint", "name": "_to_mpint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_mpint", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "algorithms": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.ciphers.algorithms", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "asym_utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.utils", "kind": "Gdef"}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "dsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.dsa", "kind": "Gdef"}, "ec": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec", "kind": "Gdef"}, "ed25519": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ed25519", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}, "load_application": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.load_application", "name": "load_application", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_application", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_ssh_private_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["data", "password", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.load_ssh_private_key", "name": "load_ssh_private_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["data", "password", "backend"], "arg_types": ["builtins.bytes", {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_ssh_private_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPrivateKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_ssh_public_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.load_ssh_public_identity", "name": "load_ssh_public_identity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_ssh_public_identity", "ret_type": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.serialization.ssh.SSHCertificate", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_ssh_public_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.load_ssh_public_key", "name": "load_ssh_public_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "backend"], "arg_types": ["builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_ssh_public_key", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "modes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.ciphers.modes", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "padding": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.padding", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "rsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa", "kind": "Gdef"}, "serialize_ssh_public_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["public_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.hazmat.primitives.serialization.ssh.serialize_ssh_public_key", "name": "serialize_ssh_public_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["public_key"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.serialization.ssh.SSHPublicKeyTypes"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serialize_ssh_public_key", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.utils", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py"}