{"data_mtime": 1753783524, "dep_lines": [7, 7, 8, 31, 80, 91, 96, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.x509.certificate_transparency", "cryptography.x509.verification", "cryptography.x509.base", "cryptography.x509.extensions", "cryptography.x509.general_name", "cryptography.x509.name", "cryptography.x509.oid", "__future__", "builtins", "dataclasses", "_frozen_importlib", "abc", "cryptography.hazmat", "cryptography.hazmat._oid", "cryptography.hazmat.bindings", "cryptography.hazmat.bindings._rust", "typing"], "hash": "361e16dd42f75a09b2d0931b211453b09f31a742", "id": "cryptography.x509", "ignore_all": true, "interface_hash": "587913268aca14eb7d34b0635d18e050fd4d5084", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\__init__.py", "plugin_data": null, "size": 8132, "suppressed": [], "version_id": "1.15.0"}