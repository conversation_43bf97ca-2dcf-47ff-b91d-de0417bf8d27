{"data_mtime": 1753783524, "dep_lines": [14, 16, 16, 16, 16, 16, 16, 16, 16, 26, 14, 15, 16, 15, 30, 36, 37, 13, 5, 7, 8, 9, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 10, 20, 20, 5, 5, 5, 10, 5, 10, 10, 10, 10, 10, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.x509", "cryptography.hazmat.primitives.asymmetric.dsa", "cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.ed448", "cryptography.hazmat.primitives.asymmetric.ed25519", "cryptography.hazmat.primitives.asymmetric.padding", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.asymmetric.x448", "cryptography.hazmat.primitives.asymmetric.x25519", "cryptography.hazmat.primitives.asymmetric.types", "cryptography.hazmat.bindings._rust", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat.primitives", "cryptography.x509.extensions", "cryptography.x509.name", "cryptography.x509.oid", "cryptography.utils", "__future__", "abc", "datetime", "os", "typing", "warnings", "cryptography", "builtins", "dataclasses", "_frozen_importlib", "cryptography.hazmat", "cryptography.hazmat.bindings", "cryptography.hazmat.primitives._asymmetric", "enum"], "hash": "c16148bb01d3629218192c780c8a09b6ae8da88b", "id": "cryptography.x509.base", "ignore_all": true, "interface_hash": "721e8d1c584ac0a2111f8cf66bdafd33e28deba8", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\base.py", "plugin_data": null, "size": 26909, "suppressed": [], "version_id": "1.15.0"}