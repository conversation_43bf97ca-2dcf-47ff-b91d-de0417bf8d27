{"data_mtime": 1753783524, "dep_lines": [14, 14, 15, 13, 5, 7, 8, 9, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 10, 10, 10, 10, 10, 20, 5, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.bindings._rust.x509", "cryptography.hazmat.bindings._rust", "cryptography.x509.oid", "cryptography.utils", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "re", "sys", "typing", "warnings", "cryptography", "builtins", "dataclasses", "_frozen_importlib", "abc", "cryptography.hazmat", "cryptography.hazmat._oid", "cryptography.hazmat.bindings", "enum", "types", "typing_extensions"], "hash": "6471124daea728814a0cd7f85cdf8a66ad1d8d0e", "id": "cryptography.x509.name", "ignore_all": true, "interface_hash": "ff98eaaafd680ccaf55fe5c198fcbafbf4c6d182", "mtime": 1740598183, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\name.py", "plugin_data": null, "size": 14830, "suppressed": [], "version_id": "1.15.0"}