{".class": "MypyFile", "_fullname": "cryptography.x509.ocsp", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CertificateIssuerPrivateKeyTypes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificateIssuerPrivateKeyTypes", "kind": "Gdef"}, "OCSPCertStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.ocsp.OCSPCertStatus", "name": "OCSPCertStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.x509.ocsp.OCSPCertStatus", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.x509.ocsp", "mro": ["cryptography.x509.ocsp.OCSPCertStatus", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "GOOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPCertStatus.GOOD", "name": "GOOD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "REVOKED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPCertStatus.REVOKED", "name": "REVOKED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "UNKNOWN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPCertStatus.UNKNOWN", "name": "UNKNOWN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.ocsp.OCSPCertStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.ocsp.OCSPCertStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OCSPRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.x509.ocsp.OCSPRequest", "line": 129, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.ocsp.OCSPRequest"}}, "OCSPRequestBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder", "name": "OCSPRequestBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.ocsp", "mro": ["cryptography.x509.ocsp.OCSPRequestBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "request", "request_hash", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "request", "request_hash", "extensions"], "arg_types": ["cryptography.x509.ocsp.OCSPRequestBuilder", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.int", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OCSPRequestBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder._extensions", "name": "_extensions", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder._request", "name": "_request", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_request_hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder._request_hash", "name": "_request_hash", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes", "builtins.int", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "add_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cert", "issuer", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder.add_certificate", "name": "add_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cert", "issuer", "algorithm"], "arg_types": ["cryptography.x509.ocsp.OCSPRequestBuilder", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_certificate of OCSPRequestBuilder", "ret_type": "cryptography.x509.ocsp.OCSPRequestBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_certificate_by_hash": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "issuer_name_hash", "issuer_key_hash", "serial_number", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder.add_certificate_by_hash", "name": "add_certificate_by_hash", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "issuer_name_hash", "issuer_key_hash", "serial_number", "algorithm"], "arg_types": ["cryptography.x509.ocsp.OCSPRequestBuilder", "builtins.bytes", "builtins.bytes", "builtins.int", "cryptography.hazmat.primitives.hashes.HashAlgorithm"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_certificate_by_hash of OCSPRequestBuilder", "ret_type": "cryptography.x509.ocsp.OCSPRequestBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder.add_extension", "name": "add_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "arg_types": ["cryptography.x509.ocsp.OCSPRequestBuilder", "cryptography.x509.extensions.ExtensionType", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_extension of OCSPRequestBuilder", "ret_type": "cryptography.x509.ocsp.OCSPRequestBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["cryptography.x509.ocsp.OCSPRequestBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of OCSPRequestBuilder", "ret_type": "cryptography.hazmat.bindings._rust.ocsp.OCSPRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.ocsp.OCSPRequestBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.ocsp.OCSPRequestBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OCSPResponderEncoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.ocsp.OCSPResponderEncoding", "name": "OCSPResponderEncoding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.x509.ocsp.OCSPResponderEncoding", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.x509.ocsp", "mro": ["cryptography.x509.ocsp.OCSPResponderEncoding", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "HASH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponderEncoding.HASH", "name": "HASH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "By Hash"}, "type_ref": "builtins.str"}}}, "NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponderEncoding.NAME", "name": "NAME", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "By Name"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.ocsp.OCSPResponderEncoding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.ocsp.OCSPResponderEncoding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OCSPResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.x509.ocsp.OCSPResponse", "line": 130, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.ocsp.OCSPResponse"}}, "OCSPResponseBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder", "name": "OCSPResponseBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.ocsp", "mro": ["cryptography.x509.ocsp.OCSPResponseBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "response", "responder_id", "certs", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "response", "responder_id", "certs", "extensions"], "arg_types": ["cryptography.x509.ocsp.OCSPResponseBuilder", {".class": "UnionType", "items": ["cryptography.x509.ocsp._SingleResponse", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.x509.ocsp.OCSPResponderEncoding"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.x509.Certificate"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OCSPResponseBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_certs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder._certs", "name": "_certs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.x509.Certificate"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder._extensions", "name": "_extensions", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["cryptography.x509.extensions.ExtensionType"], "extra_attrs": null, "type_ref": "cryptography.x509.extensions.Extension"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_responder_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder._responder_id", "name": "_responder_id", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.x509.ocsp.OCSPResponderEncoding"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder._response", "name": "_response", "type": {".class": "UnionType", "items": ["cryptography.x509.ocsp._SingleResponse", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "add_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.add_extension", "name": "add_extension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "extval", "critical"], "arg_types": ["cryptography.x509.ocsp.OCSPResponseBuilder", "cryptography.x509.extensions.ExtensionType", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_extension of OCSPResponseBuilder", "ret_type": "cryptography.x509.ocsp.OCSPResponseBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cert", "issuer", "algorithm", "cert_status", "this_update", "next_update", "revocation_time", "revocation_reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.add_response", "name": "add_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cert", "issuer", "algorithm", "cert_status", "this_update", "next_update", "revocation_time", "revocation_reason"], "arg_types": ["cryptography.x509.ocsp.OCSPResponseBuilder", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.primitives.hashes.HashAlgorithm", "cryptography.x509.ocsp.OCSPCertStatus", "datetime.datetime", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.x509.extensions.ReasonFlags", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_response of OCSPResponseBuilder", "ret_type": "cryptography.x509.ocsp.OCSPResponseBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_unsuccessful": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "response_status"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.build_unsuccessful", "name": "build_unsuccessful", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response_status"], "arg_types": [{".class": "TypeType", "item": "cryptography.x509.ocsp.OCSPResponseBuilder"}, "cryptography.x509.ocsp.OCSPResponseStatus"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_unsuccessful of OCSPResponseBuilder", "ret_type": "cryptography.hazmat.bindings._rust.ocsp.OCSPResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.build_unsuccessful", "name": "build_unsuccessful", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "response_status"], "arg_types": [{".class": "TypeType", "item": "cryptography.x509.ocsp.OCSPResponseBuilder"}, "cryptography.x509.ocsp.OCSPResponseStatus"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_unsuccessful of OCSPResponseBuilder", "ret_type": "cryptography.hazmat.bindings._rust.ocsp.OCSPResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "certificates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "certs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.certificates", "name": "certificates", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "certs"], "arg_types": ["cryptography.x509.ocsp.OCSPResponseBuilder", {".class": "Instance", "args": ["cryptography.hazmat.bindings._rust.x509.Certificate"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "certificates of OCSPResponseBuilder", "ret_type": "cryptography.x509.ocsp.OCSPResponseBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "responder_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "responder_cert"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.responder_id", "name": "responder_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "encoding", "responder_cert"], "arg_types": ["cryptography.x509.ocsp.OCSPResponseBuilder", "cryptography.x509.ocsp.OCSPResponderEncoding", "cryptography.hazmat.bindings._rust.x509.Certificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "responder_id of OCSPResponseBuilder", "ret_type": "cryptography.x509.ocsp.OCSPResponseBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "private_key", "algorithm"], "arg_types": ["cryptography.x509.ocsp.OCSPResponseBuilder", {".class": "TypeAliasType", "args": [], "type_ref": "cryptography.hazmat.primitives.asymmetric.types.CertificateIssuerPrivateKeyTypes"}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.hashes.HashAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of OCSPResponseBuilder", "ret_type": "cryptography.hazmat.bindings._rust.ocsp.OCSPResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.ocsp.OCSPResponseBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.ocsp.OCSPResponseBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OCSPResponseStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["cryptography.utils.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.ocsp.OCSPResponseStatus", "name": "OCSPResponseStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "cryptography.x509.ocsp.OCSPResponseStatus", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "cryptography.x509.ocsp", "mro": ["cryptography.x509.ocsp.OCSPResponseStatus", "cryptography.utils.Enum", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "INTERNAL_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponseStatus.INTERNAL_ERROR", "name": "INTERNAL_ERROR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "MALFORMED_REQUEST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponseStatus.MALFORMED_REQUEST", "name": "MALFORMED_REQUEST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "SIG_REQUIRED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponseStatus.SIG_REQUIRED", "name": "SIG_REQUIRED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "SUCCESSFUL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponseStatus.SUCCESSFUL", "name": "SUCCESSFUL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "TRY_LATER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponseStatus.TRY_LATER", "name": "TRY_LATER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "UNAUTHORIZED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.OCSPResponseStatus.UNAUTHORIZED", "name": "UNAUTHORIZED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.ocsp.OCSPResponseStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.ocsp.OCSPResponseStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OCSPSingleResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "cryptography.x509.ocsp.OCSPSingleResponse", "line": 131, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "cryptography.hazmat.bindings._rust.ocsp.OCSPSingleResponse"}}, "_ALLOWED_HASHES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp._ALLOWED_HASHES", "name": "_ALLOWED_HASHES", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["cryptography.hazmat.primitives.hashes.SHA1"], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SHA1", "ret_type": "cryptography.hazmat.primitives.hashes.SHA1", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["cryptography.hazmat.primitives.hashes.SHA224"], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SHA224", "ret_type": "cryptography.hazmat.primitives.hashes.SHA224", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["cryptography.hazmat.primitives.hashes.SHA256"], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SHA256", "ret_type": "cryptography.hazmat.primitives.hashes.SHA256", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["cryptography.hazmat.primitives.hashes.SHA384"], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SHA384", "ret_type": "cryptography.hazmat.primitives.hashes.SHA384", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["cryptography.hazmat.primitives.hashes.SHA512"], "def_extras": {"first_arg": "self"}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SHA512", "ret_type": "cryptography.hazmat.primitives.hashes.SHA512", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_EARLIEST_UTC_TIME": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.base._EARLIEST_UTC_TIME", "kind": "Gdef"}, "_SingleResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cryptography.x509.ocsp._SingleResponse", "name": "_SingleResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp._SingleResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cryptography.x509.ocsp", "mro": ["cryptography.x509.ocsp._SingleResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cert", "issuer", "algorithm", "cert_status", "this_update", "next_update", "revocation_time", "revocation_reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp._SingleResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cert", "issuer", "algorithm", "cert_status", "this_update", "next_update", "revocation_time", "revocation_reason"], "arg_types": ["cryptography.x509.ocsp._SingleResponse", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.bindings._rust.x509.Certificate", "cryptography.hazmat.primitives.hashes.HashAlgorithm", "cryptography.x509.ocsp.OCSPCertStatus", "datetime.datetime", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.x509.extensions.ReasonFlags", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _SingleResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._algorithm", "name": "_algorithm", "type": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}, "_cert": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._cert", "name": "_cert", "type": "cryptography.hazmat.bindings._rust.x509.Certificate"}}, "_cert_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._cert_status", "name": "_cert_status", "type": "cryptography.x509.ocsp.OCSPCertStatus"}}, "_issuer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._issuer", "name": "_issuer", "type": "cryptography.hazmat.bindings._rust.x509.Certificate"}}, "_next_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._next_update", "name": "_next_update", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_revocation_reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._revocation_reason", "name": "_revocation_reason", "type": {".class": "UnionType", "items": ["cryptography.x509.extensions.ReasonFlags", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_revocation_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._revocation_time", "name": "_revocation_time", "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_this_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "cryptography.x509.ocsp._SingleResponse._this_update", "name": "_this_update", "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cryptography.x509.ocsp._SingleResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "cryptography.x509.ocsp._SingleResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.ocsp.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.ocsp.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.ocsp.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.ocsp.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.ocsp.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.ocsp.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_convert_to_naive_utc_time": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.base._convert_to_naive_utc_time", "kind": "Gdef"}, "_reject_duplicate_extension": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509.base._reject_duplicate_extension", "kind": "Gdef"}, "_verify_algorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cryptography.x509.ocsp._verify_algorithm", "name": "_verify_algorithm", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["algorithm"], "arg_types": ["cryptography.hazmat.primitives.hashes.HashAlgorithm"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_algorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}, "load_der_ocsp_request": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.load_der_ocsp_request", "name": "load_der_ocsp_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.ocsp.OCSPRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_der_ocsp_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.ocsp.load_der_ocsp_response", "name": "load_der_ocsp_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "cryptography.hazmat.bindings._rust.ocsp.OCSPResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ocsp": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.ocsp", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "cryptography.utils", "kind": "Gdef"}, "x509": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\ocsp.py"}