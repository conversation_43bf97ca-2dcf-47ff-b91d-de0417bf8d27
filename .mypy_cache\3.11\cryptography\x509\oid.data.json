{".class": "MypyFile", "_fullname": "cryptography.x509.oid", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AttributeOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.AttributeOID", "kind": "Gdef"}, "AuthorityInformationAccessOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.AuthorityInformationAccessOID", "kind": "Gdef"}, "CRLEntryExtensionOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.CRLEntryExtensionOID", "kind": "Gdef"}, "CertificatePoliciesOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.CertificatePoliciesOID", "kind": "Gdef"}, "ExtendedKeyUsageOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.ExtendedKeyUsageOID", "kind": "Gdef"}, "ExtensionOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.ExtensionOID", "kind": "Gdef"}, "NameOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.NameOID", "kind": "Gdef"}, "OCSPExtensionOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.OCSPExtensionOID", "kind": "Gdef"}, "ObjectIdentifier": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.bindings._rust.ObjectIdentifier", "kind": "Gdef"}, "PublicKeyAlgorithmOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.PublicKeyAlgorithmOID", "kind": "Gdef"}, "SignatureAlgorithmOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.SignatureAlgorithmOID", "kind": "Gdef"}, "SubjectInformationAccessOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.SubjectInformationAccessOID", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "cryptography.x509.oid.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.oid.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.oid.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.oid.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.oid.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.oid.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cryptography.x509.oid.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\cryptography\\x509\\oid.py"}