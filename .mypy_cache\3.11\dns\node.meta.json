{"data_mtime": 1753783524, "dep_lines": [24, 25, 26, 27, 28, 29, 30, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["dns.immutable", "dns.name", "dns.rdataclass", "dns.rdataset", "dns.rdatatype", "dns.renderer", "dns.rrset", "enum", "io", "typing", "dns", "builtins", "dataclasses", "_frozen_importlib", "abc", "dns._immutable_ctx", "dns.enum", "dns.set"], "hash": "df16c1e8b7fb42e00d593d9170c5f1b763a91fc8", "id": "dns.node", "ignore_all": true, "interface_hash": "323d165279f073cd57035631042093fc60e29e96", "mtime": 1748973046, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\node.py", "plugin_data": null, "size": 12663, "suppressed": [], "version_id": "1.15.0"}