{".class": "MypyFile", "_fullname": "dns.quic._asyncio", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncQuicConnection": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.AsyncQuicConnection", "kind": "Gdef"}, "AsyncQuicManager": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.AsyncQuicManager", "kind": "Gdef"}, "AsyncioQuicConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.AsyncQuicConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._asyncio.AsyncioQuicConnection", "name": "AsyncioQuicConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._asyncio", "mro": ["dns.quic._asyncio.AsyncioQuicConnection", "dns.quic._common.AsyncQuicConnection", "dns.quic._common.BaseQuicConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "connection", "address", "port", "source", "source_port", "manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicConnection.__init__", "name": "__init__", "type": null}}, "_handle_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._handle_events", "name": "_handle_events", "type": null}}, "_handshake_complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._handshake_complete", "name": "_handshake_complete", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_receiver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._receiver", "name": "_receiver", "type": null}}, "_receiver_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._receiver_task", "name": "_receiver_task", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_sender": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._sender", "name": "_sender", "type": null}}, "_sender_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._sender_task", "name": "_sender_task", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_socket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._socket", "name": "_socket", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_socket_created": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._socket_created", "name": "_socket_created", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_wait_for_wake_timer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._wait_for_wake_timer", "name": "_wait_for_wake_timer", "type": null}}, "_wake_pending": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._wake_pending", "name": "_wake_pending", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_wake_timer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._wake_timer", "name": "_wake_timer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_wakeup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection._wakeup", "name": "_wakeup", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection.close", "name": "close", "type": null}}, "make_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection.make_stream", "name": "make_stream", "type": null}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicConnection.run", "name": "run", "type": null}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "stream", "data", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicConnection.write", "name": "write", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._asyncio.AsyncioQuicConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._asyncio.AsyncioQuicConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncioQuicManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.AsyncQuicManager"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._asyncio.AsyncioQuicManager", "name": "<PERSON>yncioQuicManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._asyncio", "mro": ["dns.quic._asyncio.AsyncioQuicManager", "dns.quic._common.AsyncQuicManager", "dns.quic._common.BaseQuicManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicManager.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicManager.__aexit__", "name": "__aexit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "conf", "verify_mode", "server_name", "h3"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicManager.__init__", "name": "__init__", "type": null}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "address", "port", "source", "source_port", "want_session_ticket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicManager.connect", "name": "connect", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._asyncio.AsyncioQuicManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._asyncio.AsyncioQuicManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncioQuicStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.quic._common.BaseQuicStream"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.quic._asyncio.AsyncioQuicStream", "name": "AsyncioQuicStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.quic._asyncio", "mro": ["dns.quic._asyncio.AsyncioQuicStream", "dns.quic._common.BaseQuicStream", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream.__aexit__", "name": "__aexit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "stream_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.quic._asyncio.AsyncioQuicStream.__init__", "name": "__init__", "type": null}}, "_add_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream._add_input", "name": "_add_input", "type": null}}, "_wait_for_wake_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream._wait_for_wake_up", "name": "_wait_for_wake_up", "type": null}}, "_wake_up": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.quic._asyncio.AsyncioQuicStream._wake_up", "name": "_wake_up", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream.close", "name": "close", "type": null}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream.receive", "name": "receive", "type": null}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "datagram", "is_end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream.send", "name": "send", "type": null}}, "wait_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "amount", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream.wait_for", "name": "wait_for", "type": null}}, "wait_for_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expiration"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "dns.quic._asyncio.AsyncioQuicStream.wait_for_end", "name": "wait_for_end", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.quic._asyncio.AsyncioQuicStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.quic._asyncio.AsyncioQuicStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseQuicStream": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.BaseQuicStream", "kind": "Gdef"}, "QUIC_MAX_DATAGRAM": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.QUIC_MAX_DATAGRAM", "kind": "Gdef"}, "UnexpectedEOF": {".class": "SymbolTableNode", "cross_ref": "dns.quic._common.UnexpectedEOF", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._asyncio.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._asyncio.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._asyncio.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._asyncio.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._asyncio.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.quic._asyncio.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aioquic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "dns.quic._asyncio.aioquic", "name": "aioquic", "type": {".class": "AnyType", "missing_import_name": "dns.quic._asyncio.aioquic", "source_any": null, "type_of_any": 3}}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\quic\\_asyncio.py"}