{"data_mtime": 1753783515, "dep_lines": [16, 14, 15, 3, 4, 5, 6, 7, 8, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 11, 12, 10, 10], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 20, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10, 20, 20], "dependencies": ["dns.quic._common", "dns.exception", "dns.inet", "selectors", "socket", "ssl", "struct", "threading", "time", "dns", "builtins", "dataclasses", "_frozen_importlib", "_socket", "_thread", "abc", "enum", "types", "typing"], "hash": "a72da5abb41545a3d60f7b9e19be82521f78aaaa", "id": "dns.quic._sync", "ignore_all": true, "interface_hash": "0730cb1adfdadc369fcec9038d61b9289bceb7e2", "mtime": 1748973047, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\quic\\_sync.py", "plugin_data": null, "size": 10436, "suppressed": ["aioquic.quic.configuration", "aioquic.quic.connection", "aioquic.quic.events", "aioquic.quic", "aioquic"], "version_id": "1.15.0"}