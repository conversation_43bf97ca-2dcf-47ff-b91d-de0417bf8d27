{"data_mtime": 1753783516, "dep_lines": [25, 26, 27, 28, 29, 30, 31, 32, 33, 20, 21, 22, 23, 25, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["dns.exception", "dns.immutable", "dns.name", "dns.rdata", "dns.rdataclass", "dns.rdatatype", "dns.renderer", "dns.set", "dns.ttl", "io", "random", "struct", "typing", "dns", "builtins", "dataclasses", "_frozen_importlib", "abc", "dns._immutable_ctx", "dns.enum", "enum"], "hash": "78110c67e7058f71cc2c6ffe4188c9ebc6e38faf", "id": "dns.rdataset", "ignore_all": true, "interface_hash": "3632ad38c1a370c65b3b8f0ec659ffa5cfac8015", "mtime": 1748973047, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\rdataset.py", "plugin_data": null, "size": 16664, "suppressed": [], "version_id": "1.15.0"}