{".class": "MypyFile", "_fullname": "dns.rdtypes.ANY.ZONEMD", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ZONEMD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.rdata.Rdata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD", "name": "ZONEMD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rdtypes.ANY.ZONEMD", "mro": ["dns.rdtypes.ANY.ZONEMD.ZONEMD", "dns.rdata.Rdata", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "rdclass", "rdtype", "serial", "scheme", "hash_algorithm", "digest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_to_wire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "file", "compress", "origin", "canonicalize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD._to_wire", "name": "_to_wire", "type": null}}, "digest": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.digest", "name": "digest", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "from_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["cls", "rdclass", "rdtype", "tok", "origin", "relativize", "relativize_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.from_text", "name": "from_text", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.from_text", "name": "from_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["cls", "rdclass", "rdtype", "tok", "origin", "relativize", "relativize_to"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.ANY.ZONEMD.ZONEMD"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_text of ZONEMD", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_wire_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "rdclass", "rdtype", "parser", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.from_wire_parser", "name": "from_wire_parser", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.from_wire_parser", "name": "from_wire_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["cls", "rdclass", "rdtype", "parser", "origin"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.ANY.ZONEMD.ZONEMD"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_wire_parser of ZONEMD", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hash_algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.hash_algorithm", "name": "hash_algorithm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.scheme", "name": "scheme", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "serial": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.serial", "name": "serial", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "to_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "origin", "relativize", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.to_text", "name": "to_text", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rdtypes.ANY.ZONEMD.ZONEMD.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rdtypes.ANY.ZONEMD.ZONEMD", "values": [], "variance": 0}, "slots": ["digest", "hash_algorithm", "rdclass", "rdcomment", "rdtype", "scheme", "serial"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.ANY.ZONEMD.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.ANY.ZONEMD.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.ANY.ZONEMD.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.ANY.ZONEMD.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.ANY.ZONEMD.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.ANY.ZONEMD.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "binascii": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\rdtypes\\ANY\\ZONEMD.py"}