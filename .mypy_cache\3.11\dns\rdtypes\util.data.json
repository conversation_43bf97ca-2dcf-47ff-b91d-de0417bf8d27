{".class": "MypyFile", "_fullname": "dns.rdtypes.util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Bitmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rdtypes.util.Bitmap", "name": "Bitmap", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Bitmap", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rdtypes.util", "mro": ["dns.rdtypes.util.Bitmap", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "windows"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Bitmap.__init__", "name": "__init__", "type": null}}, "from_rdtypes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "rdtypes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.util.Bitmap.from_rdtypes", "name": "from_rdtypes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "rdtypes"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Bitmap"}, {".class": "Instance", "args": ["dns.rdatatype.RdataType"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_rdtypes of Bitmap", "ret_type": "dns.rdtypes.util.Bitmap", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.util.Bitmap.from_rdtypes", "name": "from_rdtypes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "rdtypes"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Bitmap"}, {".class": "Instance", "args": ["dns.rdatatype.RdataType"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_rdtypes of Bitmap", "ret_type": "dns.rdtypes.util.Bitmap", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tok"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.util.Bitmap.from_text", "name": "from_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tok"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Bitmap"}, "dns.tokenizer.Tokenizer"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_text of Bitmap", "ret_type": "dns.rdtypes.util.Bitmap", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.util.Bitmap.from_text", "name": "from_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tok"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Bitmap"}, "dns.tokenizer.Tokenizer"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_text of Bitmap", "ret_type": "dns.rdtypes.util.Bitmap", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_wire_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.util.Bitmap.from_wire_parser", "name": "from_wire_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "parser"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Bitmap"}, "dns.wire.Parser"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_wire_parser of Bitmap", "ret_type": "dns.rdtypes.util.Bitmap", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.util.Bitmap.from_wire_parser", "name": "from_wire_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "parser"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Bitmap"}, "dns.wire.Parser"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_wire_parser of Bitmap", "ret_type": "dns.rdtypes.util.Bitmap", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Bitmap.to_text", "name": "to_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.rdtypes.util.Bitmap"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_text of Bitmap", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_wire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Bitmap.to_wire", "name": "to_wire", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file"], "arg_types": ["dns.rdtypes.util.Bitmap", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_wire of Bitmap", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.rdtypes.util.Bitmap.type_name", "name": "type_name", "type": "builtins.str"}}, "windows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdtypes.util.Bitmap.windows", "name": "windows", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rdtypes.util.Bitmap.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rdtypes.util.Bitmap", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Gateway": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rdtypes.util.Gateway", "name": "Gateway", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Gateway", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rdtypes.util", "mro": ["dns.rdtypes.util.Gateway", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "type", "gateway"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Gateway.__init__", "name": "__init__", "type": null}}, "_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Gateway._check", "name": "_check", "type": null}}, "_invalid_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "gateway_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.util.Gateway._invalid_type", "name": "_invalid_type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.util.Gateway._invalid_type", "name": "_invalid_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "gateway_type"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Gateway"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_invalid_type of Gateway", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["cls", "gateway_type", "tok", "origin", "relativize", "relativize_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.util.Gateway.from_text", "name": "from_text", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.util.Gateway.from_text", "name": "from_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["cls", "gateway_type", "tok", "origin", "relativize", "relativize_to"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Gateway"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_text of Gateway", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_wire_parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "gateway_type", "parser", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.rdtypes.util.Gateway.from_wire_parser", "name": "from_wire_parser", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.rdtypes.util.Gateway.from_wire_parser", "name": "from_wire_parser", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "gateway_type", "parser", "origin"], "arg_types": [{".class": "TypeType", "item": "dns.rdtypes.util.Gateway"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_wire_parser of Gateway", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "gateway": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdtypes.util.Gateway.gateway", "name": "gateway", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.rdtypes.util.Gateway.name", "name": "name", "type": "builtins.str"}}, "to_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "origin", "relativize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Gateway.to_text", "name": "to_text", "type": null}}, "to_wire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "file", "compress", "origin", "canonicalize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.Gateway.to_wire", "name": "to_wire", "type": null}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rdtypes.util.Gateway.type", "name": "type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rdtypes.util.Gateway.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rdtypes.util.Gateway", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rdtypes.util.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_no_weight": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "dns.rdtypes.util._no_weight", "name": "_no_weight", "type": "builtins.float"}}, "_priority_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util._priority_table", "name": "_priority_table", "type": null}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "parse_formatted_hex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["formatted", "num_chunks", "chunk_size", "separator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.parse_formatted_hex", "name": "parse_formatted_hex", "type": null}}, "priority_processing_order": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.priority_processing_order", "name": "priority_processing_order", "type": null}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "weighted_processing_order": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rdtypes.util.weighted_processing_order", "name": "weighted_processing_order", "type": null}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\rdtypes\\util.py"}