{".class": "MypyFile", "_fullname": "dns.rrset", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RRset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.rdataset.Rdataset"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.rrset.RRset", "name": "RRset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.rrset", "mro": ["dns.rrset.RRset", "dns.rdataset.Rdataset", "dns.set.Set", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.__eq__", "name": "__eq__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "name", "rdclass", "rdtype", "covers", "deleting"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "name", "rdclass", "rdtype", "covers", "deleting"], "arg_types": ["dns.rrset.RRset", "dns.name.Name", "dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType", {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RRset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "dns.rrset.RRset.__slots__", "name": "__slots__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.__str__", "name": "__str__", "type": null}}, "_clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset._clone", "name": "_clone", "type": null}}, "deleting": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rrset.RRset.deleting", "name": "deleting", "type": {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "full_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "rdclass", "rdtype", "covers", "deleting"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.full_match", "name": "full_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "rdclass", "rdtype", "covers", "deleting"], "arg_types": ["dns.rrset.RRset", "dns.name.Name", "dns.rdataclass.RdataClass", "dns.rdatatype.RdataType", "dns.rdatatype.RdataType", {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_match of RRset", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["dns.rrset.RRset", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of RRset", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.rrset.RRset.name", "name": "name", "type": "dns.name.Name"}}, "to_rdataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.to_rdataset", "name": "to_rdataset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["dns.rrset.RRset"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_rdataset of RRset", "ret_type": "dns.rdataset.Rdataset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "origin", "relativize", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.to_text", "name": "to_text", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "origin", "relativize", "kw"], "arg_types": ["dns.rrset.RRset", {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_text of RRset", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_wire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "file", "compress", "origin", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.RRset.to_wire", "name": "to_wire", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "file", "compress", "origin", "kw"], "arg_types": ["dns.rrset.RRset", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "dns.name.CompressType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_wire of RRset", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.rrset.RRset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.rrset.RRset", "values": [], "variance": 0}, "slots": ["covers", "deleting", "items", "name", "rdclass", "rdtype", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rrset.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rrset.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rrset.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rrset.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rrset.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.rrset.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "from_rdata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["name", "ttl", "rdatas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.from_rdata", "name": "from_rdata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["name", "ttl", "rdatas"], "arg_types": [{".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_rdata", "ret_type": "dns.rrset.RRset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_rdata_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["name", "ttl", "rdatas", "idna_codec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.from_rdata_list", "name": "from_rdata_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["name", "ttl", "rdatas", "idna_codec"], "arg_types": [{".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", {".class": "Instance", "args": ["dns.rdata.Rdata"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "UnionType", "items": ["dns.name.IDNACodec", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_rdata_list", "ret_type": "dns.rrset.RRset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 2], "arg_names": ["name", "ttl", "rdclass", "rdtype", "text_rdatas"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.from_text", "name": "from_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 2], "arg_names": ["name", "ttl", "rdclass", "rdtype", "text_rdatas"], "arg_types": [{".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_text", "ret_type": "dns.rrset.RRset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_text_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["name", "ttl", "rdclass", "rdtype", "text_rdatas", "idna_codec", "origin", "relativize", "relativize_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.rrset.from_text_list", "name": "from_text_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["name", "ttl", "rdclass", "rdtype", "text_rdatas", "idna_codec", "origin", "relativize", "relativize_to"], "arg_types": [{".class": "UnionType", "items": ["dns.name.Name", "builtins.str"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["dns.rdataclass.RdataClass", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.rdatatype.RdataType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "UnionType", "items": ["dns.name.IDNACodec", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["dns.name.Name", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_text_list", "ret_type": "dns.rrset.RRset", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\rrset.py"}