{"data_mtime": 1753783521, "dep_lines": [22, 23, 24, 25, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 20, 5, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["dns.name", "dns.rdataclass", "dns.rdataset", "dns.renderer", "typing", "dns", "builtins", "dataclasses", "_frozen_importlib", "abc", "dns.enum", "dns.rdata", "dns.rdatatype", "dns.set", "enum"], "hash": "b225e2907184b30f9461f26aef7c68cb5d797bae", "id": "dns.rrset", "ignore_all": true, "interface_hash": "ead91960c0e15debcbb2743ffd5bd842eb89c75c", "mtime": 1748973047, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\rrset.py", "plugin_data": null, "size": 9170, "suppressed": [], "version_id": "1.15.0"}