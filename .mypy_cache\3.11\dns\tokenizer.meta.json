{"data_mtime": 1753783515, "dep_lines": [24, 25, 26, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["dns.exception", "dns.name", "dns.ttl", "io", "sys", "typing", "dns", "builtins", "dataclasses", "_frozen_importlib", "_io", "abc", "types", "typing_extensions"], "hash": "52c45513f8b5e7662d073b6f4ae568b2fce05145", "id": "dns.tokenizer", "ignore_all": true, "interface_hash": "2cb78c931818758ce217b5b77a811117354bb1b6", "mtime": 1748973047, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\tokenizer.py", "plugin_data": null, "size": 23583, "suppressed": [], "version_id": "1.15.0"}