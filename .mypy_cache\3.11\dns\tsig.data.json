{".class": "MypyFile", "_fullname": "dns.tsig", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.BadAlgorithm", "name": "BadAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.BadAlgorithm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.BadAlgorithm", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.BadAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.BadAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.<PERSON>", "name": "Bad<PERSON>ey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.<PERSON>", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.<PERSON>Key.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadSignature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.BadSignature", "name": "BadSignature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.BadSignature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.BadSignature", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.BadSignature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.BadSignature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BadTime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.BadTime", "name": "BadTime", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.BadTime", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.BadTime", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.BadTime.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.BadTime", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GSSTSig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.G<PERSON>ig", "name": "GSSTSig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.G<PERSON>ig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.G<PERSON>ig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gssapi_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.GSSTSig.__init__", "name": "__init__", "type": null}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.GSSTSig.data", "name": "data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "gssapi_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.GSSTSig.gssapi_context", "name": "gssapi_context", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.GSSTSig.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.GSSTSig.sign", "name": "sign", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.GSSTSig.update", "name": "update", "type": null}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.GSSTSig.verify", "name": "verify", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.GSSTSig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.G<PERSON>ig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GSSTSigAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.GSSTSigAdapter", "name": "GSSTSigAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.GSSTSigAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.GSSTSigAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "keyname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.GSSTSigAdapter.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keyring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.GSSTSigAdapter.__init__", "name": "__init__", "type": null}}, "keyring": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.GSSTSigAdapter.keyring", "name": "keyring", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "parse_tkey_and_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "key", "message", "keyname"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "dns.tsig.GSSTSigAdapter.parse_tkey_and_step", "name": "parse_tkey_and_step", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "dns.tsig.GSSTSigAdapter.parse_tkey_and_step", "name": "parse_tkey_and_step", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "key", "message", "keyname"], "arg_types": [{".class": "TypeType", "item": "dns.tsig.GSSTSigAdapter"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_tkey_and_step of GSSTSigAdapter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.GSSTSigAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.GSSTSigAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GSS_TSIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.GSS_TSIG", "name": "GSS_TSIG", "type": "dns.name.Name"}}, "HMACTSig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.HMACTSig", "name": "HMACTSig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.HMACTSig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.HMACTSig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.HMACTSig.__init__", "name": "__init__", "type": null}}, "_hashes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMACTSig._hashes", "name": "_hashes", "type": {".class": "Instance", "args": ["dns.name.Name", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "hmac_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.HMACTSig.hmac_context", "name": "hmac_context", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.HMACTSig.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.HMACTSig.sign", "name": "sign", "type": null}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.HMACTSig.size", "name": "size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.HMACTSig.update", "name": "update", "type": null}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.HMACTSig.verify", "name": "verify", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.HMACTSig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.HMACTSig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HMAC_MD5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_MD5", "name": "HMAC_MD5", "type": "dns.name.Name"}}, "HMAC_SHA1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA1", "name": "HMAC_SHA1", "type": "dns.name.Name"}}, "HMAC_SHA224": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA224", "name": "HMAC_SHA224", "type": "dns.name.Name"}}, "HMAC_SHA256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA256", "name": "HMAC_SHA256", "type": "dns.name.Name"}}, "HMAC_SHA256_128": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA256_128", "name": "HMAC_SHA256_128", "type": "dns.name.Name"}}, "HMAC_SHA384": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA384", "name": "HMAC_SHA384", "type": "dns.name.Name"}}, "HMAC_SHA384_192": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA384_192", "name": "HMAC_SHA384_192", "type": "dns.name.Name"}}, "HMAC_SHA512": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA512", "name": "HMAC_SHA512", "type": "dns.name.Name"}}, "HMAC_SHA512_256": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.HMAC_SHA512_256", "name": "HMAC_SHA512_256", "type": "dns.name.Name"}}, "Key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.Key", "name": "Key", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.Key", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.Key", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.Key.__eq__", "name": "__eq__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "secret", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.Key.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.Key.__repr__", "name": "__repr__", "type": null}}, "algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.Key.algorithm", "name": "algorithm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.Key.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "secret": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "dns.tsig.Key.secret", "name": "secret", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.Key.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.Key", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PeerBadKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.tsig.<PERSON>r"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.P<PERSON>ad<PERSON>", "name": "PeerBad<PERSON>ey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.P<PERSON>ad<PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.P<PERSON>ad<PERSON>", "dns.tsig.<PERSON>r", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.PeerBadKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.P<PERSON>ad<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PeerBadSignature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.tsig.<PERSON>r"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.PeerBadSignature", "name": "PeerBadSignature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.PeerBadSignature", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.PeerBadSignature", "dns.tsig.<PERSON>r", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.PeerBadSignature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.PeerBadSignature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PeerBadTime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.tsig.<PERSON>r"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.PeerBadTime", "name": "PeerBadTime", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.PeerBadTime", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.PeerBadTime", "dns.tsig.<PERSON>r", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.PeerBadTime.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.PeerBadTime", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PeerBadTruncation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.tsig.<PERSON>r"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.PeerBadTruncation", "name": "PeerBadTruncation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.PeerBadTruncation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.PeerBadTruncation", "dns.tsig.<PERSON>r", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.PeerBadTruncation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.PeerBadTruncation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PeerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dns.exception.DNSException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dns.tsig.<PERSON>r", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dns.tsig.<PERSON>r", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dns.tsig", "mro": ["dns.tsig.<PERSON>r", "dns.exception.DNSException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dns.tsig.PeerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dns.tsig.<PERSON>r", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.tsig.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.tsig.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.tsig.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.tsig.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.tsig.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dns.tsig.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_digest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["wire", "key", "rdata", "time", "request_mac", "ctx", "multi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig._digest", "name": "_digest", "type": null}}, "_maybe_start_digest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["key", "mac", "multi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig._maybe_start_digest", "name": "_maybe_start_digest", "type": null}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "default_algorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.default_algorithm", "name": "default_algorithm", "type": "dns.name.Name"}}, "dns": {".class": "SymbolTableNode", "cross_ref": "dns", "kind": "Gdef"}, "get_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.get_context", "name": "get_context", "type": null}}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "hmac": {".class": "SymbolTableNode", "cross_ref": "hmac", "kind": "Gdef"}, "mac_sizes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "dns.tsig.mac_sizes", "name": "mac_sizes", "type": {".class": "Instance", "args": ["dns.name.Name", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "sign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["wire", "key", "rdata", "time", "request_mac", "ctx", "multi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.sign", "name": "sign", "type": null}}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["wire", "key", "owner", "rdata", "now", "request_mac", "tsig_start", "ctx", "multi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dns.tsig.validate", "name": "validate", "type": null}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\tsig.py"}