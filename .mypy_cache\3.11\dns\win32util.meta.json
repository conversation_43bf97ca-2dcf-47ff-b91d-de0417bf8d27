{"data_mtime": 1753783515, "dep_lines": [3, 8, 1, 3, 6, 12, 21, 1, 1, 1, 1, 23, 24], "dep_prios": [10, 10, 10, 20, 5, 10, 10, 5, 20, 30, 30, 10, 10], "dependencies": ["dns._features", "dns.name", "sys", "dns", "typing", "winreg", "threading", "builtins", "dataclasses", "_frozen_importlib", "abc"], "hash": "943658e7668425d292f81eaa2e0196f8d250b5b4", "id": "dns.win32util", "ignore_all": true, "interface_hash": "9439a3a5bdb8b63d4608126cb2a93c52b85ba83d", "mtime": 1748973047, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\dns\\win32util.py", "plugin_data": null, "size": 8874, "suppressed": ["pythoncom", "wmi"], "version_id": "1.15.0"}