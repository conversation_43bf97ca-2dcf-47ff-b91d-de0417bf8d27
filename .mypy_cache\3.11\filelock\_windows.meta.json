{"data_mtime": 1753783513, "dep_lines": [10, 11, 1, 3, 4, 5, 6, 7, 8, 14, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 5, 10, 5, 20, 30, 30], "dependencies": ["filelock._api", "filelock._util", "__future__", "os", "sys", "contextlib", "errno", "pathlib", "typing", "msvcrt", "builtins", "dataclasses", "_frozen_importlib", "abc"], "hash": "020b08324520b5ed5c82c27a2e1200b48e1f7b3c", "id": "filelock._windows", "ignore_all": true, "interface_hash": "410421f6e36e047179d2578572a627face1df45a", "mtime": 1740598098, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\filelock\\_windows.py", "plugin_data": null, "size": 2179, "suppressed": [], "version_id": "1.15.0"}