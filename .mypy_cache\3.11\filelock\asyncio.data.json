{".class": "MypyFile", "_fullname": "filelock.asyncio", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AsyncAcquireReturnProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.AsyncAcquireReturnProxy", "name": "AsyncAcquireReturnProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncAcquireReturnProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.AsyncAcquireReturnProxy", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "filelock.asyncio.AsyncAcquireReturnProxy.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.AsyncAcquireReturnProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncAcquireReturnProxy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "filelock.asyncio.BaseAsyncFileLock"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "filelock.asyncio.AsyncAcquireReturnProxy.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": ["filelock.asyncio.AsyncAcquireReturnProxy", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncAcquireReturnProxy", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lock"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncAcquireReturnProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lock"], "arg_types": ["filelock.asyncio.AsyncAcquireReturnProxy", "filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncAcquireReturnProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "filelock.asyncio.AsyncAcquireReturnProxy.lock", "name": "lock", "type": "filelock.asyncio.BaseAsyncFileLock"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.AsyncAcquireReturnProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.AsyncAcquireReturnProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFileLockContext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["filelock._api.FileLockContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.AsyncFileLockContext", "name": "AsyncFileLockContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncFileLockContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 59, "name": "lock_file", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "timeout", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "mode", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "blocking", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 71, "name": "lock_file_fd", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "lock_counter", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "run_in_executor", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "executor", "type": {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "loop", "type": {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.AsyncFileLockContext", "filelock._api.FileLockContext", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "filelock.asyncio.AsyncFileLockContext.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "lock_file", "timeout", "mode", "blocking", "lock_file_fd", "lock_counter", "run_in_executor", "executor", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncFileLockContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "lock_file", "timeout", "mode", "blocking", "lock_file_fd", "lock_counter", "run_in_executor", "executor", "loop"], "arg_types": ["filelock.asyncio.AsyncFileLockContext", "builtins.str", "builtins.float", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFileLockContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "filelock.asyncio.AsyncFileLockContext.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "lock_file"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timeout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blocking"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lock_file_fd"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lock_counter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "run_in_executor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "executor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "loop"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["lock_file", "timeout", "mode", "blocking", "lock_file_fd", "lock_counter", "run_in_executor", "executor", "loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "filelock.asyncio.AsyncFileLockContext.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["lock_file", "timeout", "mode", "blocking", "lock_file_fd", "lock_counter", "run_in_executor", "executor", "loop"], "arg_types": ["builtins.str", "builtins.float", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncFileLockContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "filelock.asyncio.AsyncFileLockContext.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["lock_file", "timeout", "mode", "blocking", "lock_file_fd", "lock_counter", "run_in_executor", "executor", "loop"], "arg_types": ["builtins.str", "builtins.float", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of AsyncFileLockContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "filelock.asyncio.AsyncFileLockContext.executor", "name": "executor", "type": {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "filelock.asyncio.AsyncFileLockContext.loop", "name": "loop", "type": {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "run_in_executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "filelock.asyncio.AsyncFileLockContext.run_in_executor", "name": "run_in_executor", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.AsyncFileLockContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.AsyncFileLockContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFileLockMeta": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["filelock._api.FileLockMeta"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.AsyncFileLockMeta", "name": "AsyncFileLockMeta", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncFileLockMeta", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.AsyncFileLockMeta", "filelock._api.FileLockMeta", "abc.ABCMeta", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["cls", "lock_file", "timeout", "mode", "thread_local", "blocking", "is_singleton", "loop", "run_in_executor", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncFileLockMeta.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["cls", "lock_file", "timeout", "mode", "thread_local", "blocking", "is_singleton", "loop", "run_in_executor", "executor"], "arg_types": ["filelock.asyncio.AsyncFileLockMeta", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of AsyncFileLockMeta", "ret_type": "filelock.asyncio.BaseAsyncFileLock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.AsyncFileLockMeta.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.AsyncFileLockMeta", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncSoftFileLock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["filelock._soft.SoftFileLock", "filelock.asyncio.BaseAsyncFileLock"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.AsyncSoftFileLock", "name": "AsyncSoftFileLock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncSoftFileLock", "has_param_spec_type": false, "metaclass_type": "filelock.asyncio.AsyncFileLockMeta", "metadata": {}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.AsyncSoftFileLock", "filelock._soft.SoftFileLock", "filelock.asyncio.BaseAsyncFileLock", "filelock._api.BaseFileLock", "contextlib.ContextDecorator", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.AsyncSoftFileLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.AsyncSoftFileLock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncThreadLocalFileContext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["filelock.asyncio.AsyncFileLockContext", "_thread._local"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.AsyncThreadLocalFileContext", "name": "AsyncThreadLocalFileContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncThreadLocalFileContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.AsyncThreadLocalFileContext", "filelock.asyncio.AsyncFileLockContext", "filelock._api.FileLockContext", "_thread._local", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.AsyncThreadLocalFileContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.AsyncThreadLocalFileContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncUnixFileLock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["filelock._unix.UnixFileLock", "filelock.asyncio.BaseAsyncFileLock"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.AsyncUnixFileLock", "name": "AsyncUnixFileLock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncUnixFileLock", "has_param_spec_type": false, "metaclass_type": "filelock.asyncio.AsyncFileLockMeta", "metadata": {}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.AsyncUnixFileLock", "filelock._unix.UnixFileLock", "filelock.asyncio.BaseAsyncFileLock", "filelock._api.BaseFileLock", "contextlib.ContextDecorator", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.AsyncUnixFileLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.AsyncUnixFileLock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncWindowsFileLock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["filelock._windows.WindowsFileLock", "filelock.asyncio.BaseAsyncFileLock"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.AsyncWindowsFileLock", "name": "AsyncWindowsFileLock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "filelock.asyncio.AsyncWindowsFileLock", "has_param_spec_type": false, "metaclass_type": "filelock.asyncio.AsyncFileLockMeta", "metadata": {}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.AsyncWindowsFileLock", "filelock._windows.WindowsFileLock", "filelock.asyncio.BaseAsyncFileLock", "filelock._api.BaseFileLock", "contextlib.ContextDecorator", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.AsyncWindowsFileLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.AsyncWindowsFileLock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseAsyncFileLock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_acquire", 1], ["_release", 1]], "alt_promote": null, "bases": ["filelock._api.BaseFileLock"], "dataclass_transform_spec": null, "declared_metaclass": "filelock.asyncio.AsyncFileLockMeta", "defn": {".class": "ClassDef", "fullname": "filelock.asyncio.BaseAsyncFileLock", "name": "BaseAsyncFileLock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "filelock.asyncio.BaseAsyncFileLock", "has_param_spec_type": false, "metaclass_type": "filelock.asyncio.AsyncFileLockMeta", "metadata": {}, "module_name": "filelock.asyncio", "mro": ["filelock.asyncio.BaseAsyncFileLock", "filelock._api.BaseFileLock", "contextlib.ContextDecorator", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "filelock.asyncio.BaseAsyncFileLock.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.BaseAsyncFileLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.BaseAsyncFileLock", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aenter__ of BaseAsyncFileLock", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.BaseAsyncFileLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.BaseAsyncFileLock", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.BaseAsyncFileLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.BaseAsyncFileLock", "values": [], "variance": 0}]}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "filelock.asyncio.BaseAsyncFileLock.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_value", "traceback"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__aexit__ of BaseAsyncFileLock", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "filelock.asyncio.BaseAsyncFileLock.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of BaseAsyncFileLock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "filelock.asyncio.BaseAsyncFileLock.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of BaseAsyncFileLock", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "lock_file", "timeout", "mode", "thread_local", "blocking", "is_singleton", "loop", "run_in_executor", "executor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "filelock.asyncio.BaseAsyncFileLock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "lock_file", "timeout", "mode", "thread_local", "blocking", "is_singleton", "loop", "run_in_executor", "executor"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseAsyncFileLock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "filelock.asyncio.BaseAsyncFileLock._context", "name": "_context", "type": "filelock.asyncio.AsyncFileLockContext"}}, "_run_internal_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "filelock.asyncio.BaseAsyncFileLock._run_internal_method", "name": "_run_internal_method", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_run_internal_method of BaseAsyncFileLock", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acquire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "timeout", "poll_interval", "blocking"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "filelock.asyncio.BaseAsyncFileLock.acquire", "name": "acquire", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5], "arg_names": ["self", "timeout", "poll_interval", "blocking"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acquire of BaseAsyncFileLock", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "filelock.asyncio.AsyncAcquireReturnProxy"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "filelock.asyncio.BaseAsyncFileLock.executor", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "filelock.asyncio.BaseAsyncFileLock.executor", "name": "executor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "executor of BaseAsyncFileLock", "ret_type": {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "filelock.asyncio.BaseAsyncFileLock.executor", "name": "executor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "executor of BaseAsyncFileLock", "ret_type": {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "filelock.asyncio.BaseAsyncFileLock.executor", "name": "executor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock", {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "executor of BaseAsyncFileLock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "executor", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "executor of BaseAsyncFileLock", "ret_type": {".class": "UnionType", "items": ["concurrent.futures._base.Executor", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "filelock.asyncio.BaseAsyncFileLock.loop", "name": "loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loop of BaseAsyncFileLock", "ret_type": {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "filelock.asyncio.BaseAsyncFileLock.loop", "name": "loop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loop of BaseAsyncFileLock", "ret_type": {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "filelock.asyncio.BaseAsyncFileLock.release", "name": "release", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "force"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "release of BaseAsyncFileLock", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_in_executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "filelock.asyncio.BaseAsyncFileLock.run_in_executor", "name": "run_in_executor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_in_executor of BaseAsyncFileLock", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "filelock.asyncio.BaseAsyncFileLock.run_in_executor", "name": "run_in_executor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["filelock.asyncio.BaseAsyncFileLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_in_executor of BaseAsyncFileLock", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "filelock.asyncio.BaseAsyncFileLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "filelock.asyncio.BaseAsyncFileLock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._api.BaseFileLock", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "FileLockContext": {".class": "SymbolTableNode", "cross_ref": "filelock._api.FileLockContext", "kind": "Gdef", "module_public": false}, "FileLockMeta": {".class": "SymbolTableNode", "cross_ref": "filelock._api.FileLockMeta", "kind": "Gdef", "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_public": false}, "SoftFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._soft.SoftFileLock", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "filelock._error.Timeout", "kind": "Gdef", "module_public": false}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_public": false}, "UnixFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._unix.UnixFileLock", "kind": "Gdef", "module_public": false}, "WindowsFileLock": {".class": "SymbolTableNode", "cross_ref": "filelock._windows.WindowsFileLock", "kind": "Gdef", "module_public": false}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filelock.asyncio._LOGGER", "name": "_LOGGER", "type": "logging.Logger"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "filelock.asyncio.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.asyncio.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.asyncio.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.asyncio.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.asyncio.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.asyncio.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "filelock.asyncio.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "futures": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures", "kind": "Gdef", "module_public": false}, "local": {".class": "SymbolTableNode", "cross_ref": "threading.local", "kind": "Gdef", "module_public": false}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\filelock\\asyncio.py"}