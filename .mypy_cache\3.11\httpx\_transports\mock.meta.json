{"data_mtime": 1753783515, "dep_lines": [6, 5, 1, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 20, 30, 30], "dependencies": ["httpx._transports.base", "httpx._models", "__future__", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc"], "hash": "756bb63ba09e596ecdebdc4b60932f3e464d4806", "id": "httpx._transports.mock", "ignore_all": true, "interface_hash": "72cb923887f280b07bf1e6f9cc326528c2c32aa1", "mtime": 1742985022, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\httpx\\_transports\\mock.py", "plugin_data": null, "size": 1232, "suppressed": [], "version_id": "1.15.0"}