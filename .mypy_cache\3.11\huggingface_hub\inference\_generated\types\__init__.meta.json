{"data_mtime": 1753783514, "dep_lines": [6, 12, 13, 21, 22, 56, 57, 63, 64, 65, 71, 77, 78, 85, 91, 97, 98, 104, 111, 117, 123, 138, 145, 146, 153, 154, 160, 161, 167, 173, 178, 183, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 30, 30, 30], "dependencies": ["huggingface_hub.inference._generated.types.audio_classification", "huggingface_hub.inference._generated.types.audio_to_audio", "huggingface_hub.inference._generated.types.automatic_speech_recognition", "huggingface_hub.inference._generated.types.base", "huggingface_hub.inference._generated.types.chat_completion", "huggingface_hub.inference._generated.types.depth_estimation", "huggingface_hub.inference._generated.types.document_question_answering", "huggingface_hub.inference._generated.types.feature_extraction", "huggingface_hub.inference._generated.types.fill_mask", "huggingface_hub.inference._generated.types.image_classification", "huggingface_hub.inference._generated.types.image_segmentation", "huggingface_hub.inference._generated.types.image_to_image", "huggingface_hub.inference._generated.types.image_to_text", "huggingface_hub.inference._generated.types.object_detection", "huggingface_hub.inference._generated.types.question_answering", "huggingface_hub.inference._generated.types.sentence_similarity", "huggingface_hub.inference._generated.types.summarization", "huggingface_hub.inference._generated.types.table_question_answering", "huggingface_hub.inference._generated.types.text2text_generation", "huggingface_hub.inference._generated.types.text_classification", "huggingface_hub.inference._generated.types.text_generation", "huggingface_hub.inference._generated.types.text_to_audio", "huggingface_hub.inference._generated.types.text_to_image", "huggingface_hub.inference._generated.types.text_to_speech", "huggingface_hub.inference._generated.types.text_to_video", "huggingface_hub.inference._generated.types.token_classification", "huggingface_hub.inference._generated.types.translation", "huggingface_hub.inference._generated.types.video_classification", "huggingface_hub.inference._generated.types.visual_question_answering", "huggingface_hub.inference._generated.types.zero_shot_classification", "huggingface_hub.inference._generated.types.zero_shot_image_classification", "huggingface_hub.inference._generated.types.zero_shot_object_detection", "builtins", "dataclasses", "_frozen_importlib", "abc", "typing"], "hash": "381cab7d4ebaded316101f0ff4b559bd312e3880", "id": "huggingface_hub.inference._generated.types", "ignore_all": true, "interface_hash": "99cc7e9a8428ea44ebaef05a83fe084618348eaa", "mtime": 1746815058, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\inference\\_generated\\types\\__init__.py", "plugin_data": null, "size": 6307, "suppressed": [], "version_id": "1.15.0"}