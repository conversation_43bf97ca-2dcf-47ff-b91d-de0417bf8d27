{".class": "MypyFile", "_fullname": "huggingface_hub.inference._generated.types.question_answering", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseInferenceType": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.BaseInferenceType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "QuestionAnsweringInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInput", "name": "QuestionAnsweringInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.question_answering", "mro": ["huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInput", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInput.inputs", "name": "inputs", "type": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData"}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInput.parameters", "name": "parameters", "type": {".class": "UnionType", "items": ["huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuestionAnsweringInputData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData", "name": "QuestionAnsweringInputData", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.question_answering", "mro": ["huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData.context", "name": "context", "type": "builtins.str"}}, "question": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData.question", "name": "question", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringInputData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuestionAnsweringOutputElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement", "name": "QuestionAnsweringOutputElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.question_answering", "mro": ["huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "answer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement.answer", "name": "answer", "type": "builtins.str"}}, "end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement.end", "name": "end", "type": "builtins.int"}}, "score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement.score", "name": "score", "type": "builtins.float"}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement.start", "name": "start", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringOutputElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuestionAnsweringParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters", "name": "QuestionAnsweringParameters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.question_answering", "mro": ["huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "align_to_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.align_to_words", "name": "align_to_words", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "doc_stride": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.doc_stride", "name": "doc_stride", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "handle_impossible_answer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.handle_impossible_answer", "name": "handle_impossible_answer", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_answer_len": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.max_answer_len", "name": "max_answer_len", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_question_len": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.max_question_len", "name": "max_question_len", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_seq_len": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.max_seq_len", "name": "max_seq_len", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "top_k": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.top_k", "name": "top_k", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.question_answering.QuestionAnsweringParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.question_answering.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass_with_extra": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.dataclass_with_extra", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\inference\\_generated\\types\\question_answering.py"}