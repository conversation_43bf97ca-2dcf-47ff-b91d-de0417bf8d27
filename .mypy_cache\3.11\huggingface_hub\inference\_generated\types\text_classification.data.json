{".class": "MypyFile", "_fullname": "huggingface_hub.inference._generated.types.text_classification", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseInferenceType": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.BaseInferenceType", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TextClassificationInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationInput", "name": "TextClassificationInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_classification", "mro": ["huggingface_hub.inference._generated.types.text_classification.TextClassificationInput", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationInput.inputs", "name": "inputs", "type": "builtins.str"}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationInput.parameters", "name": "parameters", "type": {".class": "UnionType", "items": ["huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_classification.TextClassificationInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextClassificationOutputElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputElement", "name": "TextClassificationOutputElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputElement", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_classification", "mro": ["huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputElement", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputElement.label", "name": "label", "type": "builtins.str"}}, "score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputElement.score", "name": "score", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextClassificationOutputTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputTransform", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "sigmoid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "softmax"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": false}}}, "TextClassificationParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters", "name": "TextClassificationParameters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_classification", "mro": ["huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "function_to_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters.function_to_apply", "name": "function_to_apply", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "huggingface_hub.inference._generated.types.text_classification.TextClassificationOutputTransform"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "top_k": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters.top_k", "name": "top_k", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_classification.TextClassificationParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_classification.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass_with_extra": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.dataclass_with_extra", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\inference\\_generated\\types\\text_classification.py"}