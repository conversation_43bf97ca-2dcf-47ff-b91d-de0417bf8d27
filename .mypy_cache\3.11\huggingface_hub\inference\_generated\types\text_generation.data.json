{".class": "MypyFile", "_fullname": "huggingface_hub.inference._generated.types.text_generation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseInferenceType": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.BaseInferenceType", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TextGenerationInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInput", "name": "TextGenerationInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationInput", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInput.inputs", "name": "inputs", "type": "builtins.str"}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInput.parameters", "name": "parameters", "type": {".class": "UnionType", "items": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInput.stream", "name": "stream", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationInputGenerateParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters", "name": "TextGenerationInputGenerateParameters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "adapter_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.adapter_id", "name": "adapter_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "best_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.best_of", "name": "best_of", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "decoder_input_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.decoder_input_details", "name": "decoder_input_details", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.details", "name": "details", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "do_sample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.do_sample", "name": "do_sample", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "frequency_penalty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.frequency_penalty", "name": "frequency_penalty", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "grammar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.grammar", "name": "grammar", "type": {".class": "UnionType", "items": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_new_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.max_new_tokens", "name": "max_new_tokens", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "repetition_penalty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.repetition_penalty", "name": "repetition_penalty", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "return_full_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.return_full_text", "name": "return_full_text", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.seed", "name": "seed", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.stop", "name": "stop", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "temperature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.temperature", "name": "temperature", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "top_k": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.top_k", "name": "top_k", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "top_n_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.top_n_tokens", "name": "top_n_tokens", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "top_p": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.top_p", "name": "top_p", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "truncate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.truncate", "name": "truncate", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "typical_p": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.typical_p", "name": "typical_p", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "watermark": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.watermark", "name": "watermark", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGenerateParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationInputGrammarType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType", "name": "TextGenerationInputGrammarType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType.type", "name": "type", "type": {".class": "TypeAliasType", "args": [], "type_ref": "huggingface_hub.inference._generated.types.text_generation.TypeEnum"}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationInputGrammarType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutput", "name": "TextGenerationOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutput", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutput.details", "name": "details", "type": {".class": "UnionType", "items": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "generated_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutput.generated_text", "name": "generated_text", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationOutputBestOfSequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence", "name": "TextGenerationOutputBestOfSequence", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "finish_reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.finish_reason", "name": "finish_reason", "type": {".class": "TypeAliasType", "args": [], "type_ref": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputFinishReason"}}}, "generated_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.generated_text", "name": "generated_text", "type": "builtins.str"}}, "generated_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.generated_tokens", "name": "generated_tokens", "type": "builtins.int"}}, "prefill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.prefill", "name": "prefill", "type": {".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.seed", "name": "seed", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.tokens", "name": "tokens", "type": {".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "top_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.top_tokens", "name": "top_tokens", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationOutputDetails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails", "name": "TextGenerationOutputDetails", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "best_of_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.best_of_sequences", "name": "best_of_sequences", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputBestOfSequence"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "finish_reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.finish_reason", "name": "finish_reason", "type": {".class": "TypeAliasType", "args": [], "type_ref": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputFinishReason"}}}, "generated_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.generated_tokens", "name": "generated_tokens", "type": "builtins.int"}}, "prefill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.prefill", "name": "prefill", "type": {".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.seed", "name": "seed", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.tokens", "name": "tokens", "type": {".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "top_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.top_tokens", "name": "top_tokens", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputDetails", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationOutputFinishReason": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputFinishReason", "line": 88, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "length"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eos_token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stop_sequence"}], "uses_pep604_syntax": false}}}, "TextGenerationOutputPrefillToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken", "name": "TextGenerationOutputPrefillToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken.id", "name": "id", "type": "builtins.int"}}, "logprob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken.logprob", "name": "logprob", "type": "builtins.float"}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken.text", "name": "text", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputPrefillToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationOutputToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken", "name": "TextGenerationOutputToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken.id", "name": "id", "type": "builtins.int"}}, "logprob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken.logprob", "name": "logprob", "type": "builtins.float"}}, "special": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken.special", "name": "special", "type": "builtins.bool"}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken.text", "name": "text", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationStreamOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput", "name": "TextGenerationStreamOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput.details", "name": "details", "type": {".class": "UnionType", "items": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "generated_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput.generated_text", "name": "generated_text", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput.index", "name": "index", "type": "builtins.int"}}, "token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput.token", "name": "token", "type": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken"}}, "top_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput.top_tokens", "name": "top_tokens", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationStreamOutputStreamDetails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails", "name": "TextGenerationStreamOutputStreamDetails", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "finish_reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails.finish_reason", "name": "finish_reason", "type": {".class": "TypeAliasType", "args": [], "type_ref": "huggingface_hub.inference._generated.types.text_generation.TextGenerationOutputFinishReason"}}}, "generated_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails.generated_tokens", "name": "generated_tokens", "type": "builtins.int"}}, "input_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails.input_length", "name": "input_length", "type": "builtins.int"}}, "seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails.seed", "name": "seed", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputStreamDetails", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextGenerationStreamOutputToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken", "name": "TextGenerationStreamOutputToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.text_generation", "mro": ["huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken.id", "name": "id", "type": "builtins.int"}}, "logprob": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken.logprob", "name": "logprob", "type": "builtins.float"}}, "special": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken.special", "name": "special", "type": "builtins.bool"}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken.text", "name": "text", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.text_generation.TextGenerationStreamOutputToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeEnum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "huggingface_hub.inference._generated.types.text_generation.TypeEnum", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_schema"}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.text_generation.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass_with_extra": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.dataclass_with_extra", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\inference\\_generated\\types\\text_generation.py"}