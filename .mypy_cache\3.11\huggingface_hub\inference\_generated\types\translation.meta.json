{"data_mtime": 1753783512, "dep_lines": [8, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 30, 30, 30], "dependencies": ["huggingface_hub.inference._generated.types.base", "typing", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc"], "hash": "8a15711d6d33d07ad5aaadd39f5cb1890d102005", "id": "huggingface_hub.inference._generated.types.translation", "ignore_all": true, "interface_hash": "2ed4c48af2627a46638e4d392e4967f87941136b", "mtime": 1746815058, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\inference\\_generated\\types\\translation.py", "plugin_data": null, "size": 1763, "suppressed": [], "version_id": "1.15.0"}