{".class": "MypyFile", "_fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseInferenceType": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.BaseInferenceType", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ZeroShotImageClassificationInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationInput", "name": "ZeroShotImageClassificationInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.zero_shot_image_classification", "mro": ["huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationInput", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationInput.inputs", "name": "inputs", "type": "builtins.str"}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationInput.parameters", "name": "parameters", "type": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZeroShotImageClassificationOutputElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationOutputElement", "name": "ZeroShotImageClassificationOutputElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationOutputElement", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.zero_shot_image_classification", "mro": ["huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationOutputElement", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationOutputElement.label", "name": "label", "type": "builtins.str"}}, "score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationOutputElement.score", "name": "score", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationOutputElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationOutputElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZeroShotImageClassificationParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters", "name": "ZeroShotImageClassificationParameters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.zero_shot_image_classification", "mro": ["huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "candidate_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters.candidate_labels", "name": "candidate_labels", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "hypothesis_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters.hypothesis_template", "name": "hypothesis_template", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.zero_shot_image_classification.ZeroShotImageClassificationParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_image_classification.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass_with_extra": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.dataclass_with_extra", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\inference\\_generated\\types\\zero_shot_image_classification.py"}