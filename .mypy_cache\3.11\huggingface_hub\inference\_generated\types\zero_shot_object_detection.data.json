{".class": "MypyFile", "_fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseInferenceType": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.BaseInferenceType", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ZeroShotObjectDetectionBoundingBox": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox", "name": "ZeroShotObjectDetectionBoundingBox", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.zero_shot_object_detection", "mro": ["huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "xmax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox.xmax", "name": "xmax", "type": "builtins.int"}}, "xmin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox.xmin", "name": "xmin", "type": "builtins.int"}}, "ymax": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox.ymax", "name": "ymax", "type": "builtins.int"}}, "ymin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox.ymin", "name": "ymin", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZeroShotObjectDetectionInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionInput", "name": "ZeroShotObjectDetectionInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.zero_shot_object_detection", "mro": ["huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionInput", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionInput.inputs", "name": "inputs", "type": "builtins.str"}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionInput.parameters", "name": "parameters", "type": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionParameters"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZeroShotObjectDetectionOutputElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement", "name": "ZeroShotObjectDetectionOutputElement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.zero_shot_object_detection", "mro": ["huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "box": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement.box", "name": "box", "type": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionBoundingBox"}}, "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement.label", "name": "label", "type": "builtins.str"}}, "score": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement.score", "name": "score", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionOutputElement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ZeroShotObjectDetectionParameters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.inference._generated.types.base.BaseInferenceType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionParameters", "name": "ZeroShotObjectDetectionParameters", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionParameters", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "huggingface_hub.inference._generated.types.zero_shot_object_detection", "mro": ["huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionParameters", "huggingface_hub.inference._generated.types.base.BaseInferenceType", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "candidate_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionParameters.candidate_labels", "name": "candidate_labels", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionParameters.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "huggingface_hub.inference._generated.types.zero_shot_object_detection.ZeroShotObjectDetectionParameters", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "huggingface_hub.inference._generated.types.zero_shot_object_detection.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass_with_extra": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.inference._generated.types.base.dataclass_with_extra", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\inference\\_generated\\types\\zero_shot_object_detection.py"}