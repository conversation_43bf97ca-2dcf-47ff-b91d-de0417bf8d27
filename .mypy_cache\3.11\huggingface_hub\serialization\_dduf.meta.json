{"data_mtime": 1753783513, "dep_lines": [12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["huggingface_hub.errors", "json", "logging", "mmap", "os", "shutil", "zipfile", "contextlib", "dataclasses", "pathlib", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "09b709674b57bc93cb39226c511c115b0f055bcf", "id": "huggingface_hub.serialization._dduf", "ignore_all": true, "interface_hash": "d1781d9448ca032506b76b80529dcbddf456b3c6", "mtime": 1746815058, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\huggingface_hub\\serialization\\_dduf.py", "plugin_data": null, "size": 15424, "suppressed": [], "version_id": "1.15.0"}