{".class": "MypyFile", "_fullname": "jwt.algorithms", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "Algorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["from_jwk", 1], ["prepare_key", 1], ["sign", 1], ["to_jwk", 1], ["verify", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jwt.algorithms.Algorithm", "name": "Algorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "jwt.algorithms.Algorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jwt.algorithms", "mro": ["jwt.algorithms.Algorithm", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "compute_hash_digest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bytestr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.Algorithm.compute_hash_digest", "name": "compute_hash_digest", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bytestr"], "arg_types": ["jwt.algorithms.Algorithm", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compute_hash_digest of Algorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["jwk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated", "is_trivial_body"], "fullname": "jwt.algorithms.Algorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of Algorithm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.Algorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of Algorithm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prepare_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "jwt.algorithms.Algorithm.prepare_key", "name": "prepare_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jwt.algorithms.Algorithm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_key of Algorithm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.Algorithm.prepare_key", "name": "prepare_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jwt.algorithms.Algorithm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_key of Algorithm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "jwt.algorithms.Algorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.Algorithm", "builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of Algorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.Algorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.Algorithm", "builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of Algorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "to_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "jwt.algorithms.Algorithm.to_jwk", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated", "is_trivial_body"], "fullname": "jwt.algorithms.Algorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.Algorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated", "is_trivial_body"], "fullname": "jwt.algorithms.Algorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.Algorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated", "is_trivial_body"], "fullname": "jwt.algorithms.Algorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.Algorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of Algorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "jwt.algorithms.Algorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.Algorithm", "builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of Algorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.Algorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.Algorithm", "builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of Algorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jwt.algorithms.Algorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jwt.algorithms.Algorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AllowedECKeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "jwt.algorithms.AllowedECKeys", "line": 76, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey"], "uses_pep604_syntax": true}}}, "AllowedKeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "jwt.algorithms.AllowedKeys", "line": 80, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}], "uses_pep604_syntax": true}}}, "AllowedOKPKeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "jwt.algorithms.AllowedOKPKeys", "line": 77, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey", "cryptography.hazmat.primitives.asymmetric.ed448.Ed448Private<PERSON>ey", "cryptography.hazmat.primitives.asymmetric.ed448.Ed448Public<PERSON>ey"], "uses_pep604_syntax": true}}}, "AllowedPrivateKeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "jwt.algorithms.AllowedPrivateKeys", "line": 81, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey", "cryptography.hazmat.primitives.asymmetric.ed448.Ed448Private<PERSON>ey"], "uses_pep604_syntax": true}}}, "AllowedPublicKeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "jwt.algorithms.AllowedPublicKeys", "line": 84, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey", "cryptography.hazmat.primitives.asymmetric.ed448.Ed448Public<PERSON>ey"], "uses_pep604_syntax": true}}}, "AllowedRSAKeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "jwt.algorithms.AllowedRSAKeys", "line": 75, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey"], "uses_pep604_syntax": true}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "ECAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jwt.algorithms.Algorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jwt.algorithms.ECAlgorithm", "name": "ECAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jwt.algorithms.ECAlgorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jwt.algorithms", "mro": ["jwt.algorithms.ECAlgorithm", "jwt.algorithms.Algorithm", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "SHA256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.ECAlgorithm.SHA256", "name": "SHA256", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "SHA384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.ECAlgorithm.SHA384", "name": "SHA384", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "SHA512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.ECAlgorithm.SHA512", "name": "SHA512", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hash_alg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.ECAlgorithm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hash_alg"], "arg_types": ["jwt.algorithms.ECAlgorithm", {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ECAlgorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["jwk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.ECAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of ECAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.ECAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of ECAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hash_alg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jwt.algorithms.ECAlgorithm.hash_alg", "name": "hash_alg", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "prepare_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.ECAlgorithm.prepare_key", "name": "prepare_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jwt.algorithms.ECAlgorithm", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_key of ECAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.ECAlgorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.ECAlgorithm", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of ECAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "jwt.algorithms.ECAlgorithm.to_jwk", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.ECAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.ECAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.ECAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.ECAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.ECAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.ECAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of ECAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.ECAlgorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.ECAlgorithm", "builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedECKeys"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of ECAlgorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jwt.algorithms.ECAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jwt.algorithms.ECAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ECDSA": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.ECDSA", "kind": "Gdef"}, "Ed25519PrivateKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey", "kind": "Gdef"}, "Ed25519PublicKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519Public<PERSON>ey", "kind": "Gdef"}, "Ed448PrivateKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ed448.Ed448Private<PERSON>ey", "kind": "Gdef"}, "Ed448PublicKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ed448.Ed448Public<PERSON>ey", "kind": "Gdef"}, "EllipticCurve": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurve", "kind": "Gdef"}, "EllipticCurvePrivateKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateKey", "kind": "Gdef"}, "EllipticCurvePrivateNumbers": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePrivateNumbers", "kind": "Gdef"}, "EllipticCurvePublicKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicKey", "kind": "Gdef"}, "EllipticCurvePublicNumbers": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.EllipticCurvePublicNumbers", "kind": "Gdef"}, "Encoding": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.Encoding", "kind": "Gdef"}, "HMACAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jwt.algorithms.Algorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jwt.algorithms.HMACAlgorithm", "name": "HMACAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jwt.algorithms.HMACAlgorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jwt.algorithms", "mro": ["jwt.algorithms.HMACAlgorithm", "jwt.algorithms.Algorithm", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "SHA256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.HMACAlgorithm.SHA256", "name": "SHA256", "type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.HashlibHash"}}}, "SHA384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.HMACAlgorithm.SHA384", "name": "SHA384", "type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.HashlibHash"}}}, "SHA512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.HMACAlgorithm.SHA512", "name": "SHA512", "type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.HashlibHash"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hash_alg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.HMACAlgorithm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hash_alg"], "arg_types": ["jwt.algorithms.HMACAlgorithm", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.HashlibHash"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HMACAlgorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["jwk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.HMACAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of HMACAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.HMACAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of HMACAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hash_alg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jwt.algorithms.HMACAlgorithm.hash_alg", "name": "hash_alg", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.HMACAlgorithm.prepare_key", "name": "prepare_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jwt.algorithms.HMACAlgorithm", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_key of HMACAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.HMACAlgorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.HMACAlgorithm", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of HMACAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "jwt.algorithms.HMACAlgorithm.to_jwk", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.HMACAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.HMACAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.HMACAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.HMACAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.HMACAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.HMACAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of HMACAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.HMACAlgorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.HMACAlgorithm", "builtins.bytes", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of HMACAlgorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jwt.algorithms.HMACAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jwt.algorithms.HMACAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HashlibHash": {".class": "SymbolTableNode", "cross_ref": "jwt.types.HashlibHash", "kind": "Gdef"}, "InvalidKeyError": {".class": "SymbolTableNode", "cross_ref": "jwt.exceptions.InvalidKeyError", "kind": "Gdef"}, "InvalidSignature": {".class": "SymbolTableNode", "cross_ref": "cryptography.exceptions.InvalidSignature", "kind": "Gdef"}, "JWKDict": {".class": "SymbolTableNode", "cross_ref": "jwt.types.JWKDict", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NoEncryption": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.NoEncryption", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "NoneAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jwt.algorithms.Algorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jwt.algorithms.NoneAlgorithm", "name": "NoneAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jwt.algorithms.NoneAlgorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jwt.algorithms", "mro": ["jwt.algorithms.NoneAlgorithm", "jwt.algorithms.Algorithm", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "from_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["jwk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.NoneAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of NoneAlgorithm", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.NoneAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of NoneAlgorithm", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prepare_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.NoneAlgorithm.prepare_key", "name": "prepare_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jwt.algorithms.NoneAlgorithm", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_key of NoneAlgorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.NoneAlgorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.NoneAlgorithm", "builtins.bytes", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of NoneAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.NoneAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of NoneAlgorithm", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.NoneAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of NoneAlgorithm", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.NoneAlgorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.NoneAlgorithm", "builtins.bytes", {".class": "NoneType"}, "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of NoneAlgorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jwt.algorithms.NoneAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jwt.algorithms.NoneAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OKPAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jwt.algorithms.Algorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jwt.algorithms.OKPAlgorithm", "name": "OKPAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jwt.algorithms.OKPAlgorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jwt.algorithms", "mro": ["jwt.algorithms.OKPAlgorithm", "jwt.algorithms.Algorithm", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.OKPAlgorithm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["jwt.algorithms.OKPAlgorithm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OKPAlgorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["jwk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.OKPAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of OKPAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.OKPAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of OKPAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prepare_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.OKPAlgorithm.prepare_key", "name": "prepare_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jwt.algorithms.OKPAlgorithm", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_key of OKPAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.OKPAlgorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.OKPAlgorithm", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.ed25519.Ed25519PrivateKey", "cryptography.hazmat.primitives.asymmetric.ed448.Ed448Private<PERSON>ey"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of OKPAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "jwt.algorithms.OKPAlgorithm.to_jwk", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.OKPAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.OKPAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.OKPAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.OKPAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.OKPAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.OKPAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of OKPAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.OKPAlgorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.OKPAlgorithm", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedOKPKeys"}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of OKPAlgorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jwt.algorithms.OKPAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jwt.algorithms.OKPAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PrivateFormat": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.PrivateFormat", "kind": "Gdef"}, "PublicFormat": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives._serialization.PublicFormat", "kind": "Gdef"}, "RSAAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jwt.algorithms.Algorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jwt.algorithms.RSAAlgorithm", "name": "RSAAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAAlgorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jwt.algorithms", "mro": ["jwt.algorithms.RSAAlgorithm", "jwt.algorithms.Algorithm", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "SHA256": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.RSAAlgorithm.SHA256", "name": "SHA256", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "SHA384": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.RSAAlgorithm.SHA384", "name": "SHA384", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "SHA512": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "jwt.algorithms.RSAAlgorithm.SHA512", "name": "SHA512", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hash_alg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAAlgorithm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hash_alg"], "arg_types": ["jwt.algorithms.RSAAlgorithm", {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RSAAlgorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["jwk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.RSAAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of RSAAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.RSAAlgorithm.from_jwk", "name": "from_jwk", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["jwk"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_jwk of RSAAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hash_alg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jwt.algorithms.RSAAlgorithm.hash_alg", "name": "hash_alg", "type": {".class": "TypeType", "item": "cryptography.hazmat.primitives.hashes.HashAlgorithm"}}}, "prepare_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAAlgorithm.prepare_key", "name": "prepare_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["jwt.algorithms.RSAAlgorithm", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prepare_key of RSAAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAAlgorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.RSAAlgorithm", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of RSAAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_jwk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "jwt.algorithms.RSAAlgorithm.to_jwk", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jwt.algorithms.RSAAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.RSAAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.RSAAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.RSAAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "jwt.algorithms.RSAAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready", "is_inferred"], "fullname": "jwt.algorithms.RSAAlgorithm.to_jwk", "name": "to_jwk", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jwt.types.JWKDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["key_obj", "as_dict"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "jwt.algorithms.AllowedRSAKeys"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_jwk of RSAAlgorithm", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAAlgorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.RSAAlgorithm", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of RSAAlgorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jwt.algorithms.RSAAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jwt.algorithms.RSAAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RSAPSSAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jwt.algorithms.RSAAlgorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jwt.algorithms.RSAPSSAlgorithm", "name": "RSAPSSAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAPSSAlgorithm", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "jwt.algorithms", "mro": ["jwt.algorithms.RSAPSSAlgorithm", "jwt.algorithms.RSAAlgorithm", "jwt.algorithms.Algorithm", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAPSSAlgorithm.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "key"], "arg_types": ["jwt.algorithms.RSAPSSAlgorithm", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of RSAPSSAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.RSAPSSAlgorithm.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "msg", "key", "sig"], "arg_types": ["jwt.algorithms.RSAPSSAlgorithm", "builtins.bytes", "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of RSAPSSAlgorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jwt.algorithms.RSAPSSAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jwt.algorithms.RSAPSSAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RSAPrivateKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", "kind": "Gdef"}, "RSAPrivateNumbers": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateNumbers", "kind": "Gdef"}, "RSAPublicKey": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicKey", "kind": "Gdef"}, "RSAPublicNumbers": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.RSAPublicNumbers", "kind": "Gdef"}, "SECP256K1": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.SECP256K1", "kind": "Gdef"}, "SECP256R1": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.SECP256R1", "kind": "Gdef"}, "SECP384R1": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.SECP384R1", "kind": "Gdef"}, "SECP521R1": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.ec.SECP521R1", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "UnsupportedAlgorithm": {".class": "SymbolTableNode", "cross_ref": "cryptography.exceptions.UnsupportedAlgorithm", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jwt.algorithms.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jwt.algorithms.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jwt.algorithms.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jwt.algorithms.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jwt.algorithms.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jwt.algorithms.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base64url_decode": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.base64url_decode", "kind": "Gdef"}, "base64url_encode": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.base64url_encode", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "default_backend": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.backends.default_backend", "kind": "Gdef"}, "der_to_raw_signature": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.der_to_raw_signature", "kind": "Gdef"}, "force_bytes": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.force_bytes", "kind": "Gdef"}, "from_base64url_uint": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.from_base64url_uint", "kind": "Gdef"}, "get_default_algorithms": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jwt.algorithms.get_default_algorithms", "name": "get_default_algorithms", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_algorithms", "ret_type": {".class": "Instance", "args": ["builtins.str", "jwt.algorithms.Algorithm"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_crypto": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "jwt.algorithms.has_crypto", "name": "has_crypto", "type": "builtins.bool"}}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "hmac": {".class": "SymbolTableNode", "cross_ref": "hmac", "kind": "Gdef"}, "is_pem_format": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.is_pem_format", "kind": "Gdef"}, "is_ssh_key": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.is_ssh_key", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_pem_private_key": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.serialization.base.load_pem_private_key", "kind": "Gdef"}, "load_pem_public_key": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.serialization.base.load_pem_public_key", "kind": "Gdef"}, "load_ssh_public_key": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.serialization.ssh.load_ssh_public_key", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "padding": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.padding", "kind": "Gdef"}, "raw_to_der_signature": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.raw_to_der_signature", "kind": "Gdef"}, "requires_cryptography": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "jwt.algorithms.requires_cryptography", "name": "requires_cryptography", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "rsa_crt_dmp1": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_crt_dmp1", "kind": "Gdef"}, "rsa_crt_dmq1": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_crt_dmq1", "kind": "Gdef"}, "rsa_crt_iqmp": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_crt_iqmp", "kind": "Gdef"}, "rsa_recover_prime_factors": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa.rsa_recover_prime_factors", "kind": "Gdef"}, "to_base64url_uint": {".class": "SymbolTableNode", "cross_ref": "jwt.utils.to_base64url_uint", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jwt\\algorithms.py"}