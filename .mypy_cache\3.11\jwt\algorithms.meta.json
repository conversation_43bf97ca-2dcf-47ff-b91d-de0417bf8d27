{"data_mtime": 1753783522, "dep_lines": [27, 28, 40, 44, 48, 26, 27, 58, 25, 26, 9, 10, 11, 24, 1, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 20, 5, 5, 20, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.padding", "cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.ed448", "cryptography.hazmat.primitives.asymmetric.ed25519", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat.primitives.serialization", "cryptography.hazmat.backends", "cryptography.hazmat.primitives", "jwt.exceptions", "jwt.types", "jwt.utils", "cryptography.exceptions", "__future__", "<PERSON><PERSON><PERSON>", "hmac", "json", "abc", "typing", "builtins", "dataclasses", "_frozen_importlib", "_hashlib", "cryptography", "cryptography.hazmat", "types", "typing_extensions"], "hash": "000d909213ff33079f338cbffaf7647609f4fced", "id": "jwt.algorithms", "ignore_all": true, "interface_hash": "d05f041f0e5d9d89d76338ce4aa06d320326a06e", "mtime": 1740597994, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jwt\\algorithms.py", "plugin_data": null, "size": 30409, "suppressed": [], "version_id": "1.15.0"}