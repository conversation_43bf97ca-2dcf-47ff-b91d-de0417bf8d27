{"data_mtime": 1753783516, "dep_lines": [7, 8, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.utils", "base64", "<PERSON><PERSON><PERSON><PERSON>", "re", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc", "cryptography", "cryptography.hazmat", "cryptography.hazmat.primitives", "cryptography.hazmat.primitives.asymmetric", "enum", "typing_extensions"], "hash": "ab59a3d994567c0a89205892de2f9b55d4651428", "id": "jwt.utils", "ignore_all": true, "interface_hash": "016598dc062e734bbf32a6e2aabe73388602cd62", "mtime": 1740597994, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jwt\\utils.py", "plugin_data": null, "size": 3640, "suppressed": [], "version_id": "1.15.0"}