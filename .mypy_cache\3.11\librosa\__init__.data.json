{".class": "MypyFile", "_fullname": "librosa", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "A4_to_tuning": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.A4_to_tuning", "kind": "Gdef"}, "A_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.A_weighting", "kind": "Gdef"}, "B_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.B_weighting", "kind": "Gdef"}, "C_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.C_weighting", "kind": "Gdef"}, "D_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.D_weighting", "kind": "Gdef"}, "LibrosaError": {".class": "SymbolTableNode", "cross_ref": "librosa.util.exceptions.LibrosaError", "kind": "Gdef"}, "ParameterError": {".class": "SymbolTableNode", "cross_ref": "librosa.util.exceptions.ParameterError", "kind": "Gdef"}, "Z_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.Z_weighting", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "amplitude_to_db": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.amplitude_to_db", "kind": "Gdef"}, "autocorrelate": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.autocorrelate", "kind": "Gdef"}, "beat": {".class": "SymbolTableNode", "cross_ref": "librosa.beat", "kind": "Gdef", "module_public": false}, "blocks_to_frames": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.blocks_to_frames", "kind": "Gdef"}, "blocks_to_samples": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.blocks_to_samples", "kind": "Gdef"}, "blocks_to_time": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.blocks_to_time", "kind": "Gdef"}, "cache": {".class": "SymbolTableNode", "cross_ref": "librosa._cache.cache", "kind": "Gdef"}, "chirp": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.chirp", "kind": "Gdef"}, "cite": {".class": "SymbolTableNode", "cross_ref": "librosa.util.files.cite", "kind": "Gdef"}, "clicks": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.clicks", "kind": "Gdef"}, "core": {".class": "SymbolTableNode", "cross_ref": "librosa.core", "kind": "Gdef", "module_public": false}, "cqt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.constantq.cqt", "kind": "Gdef"}, "cqt_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.cqt_frequencies", "kind": "Gdef"}, "db_to_amplitude": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.db_to_amplitude", "kind": "Gdef"}, "db_to_power": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.db_to_power", "kind": "Gdef"}, "decompose": {".class": "SymbolTableNode", "cross_ref": "librosa.decompose", "kind": "Gdef", "module_public": false}, "display": {".class": "SymbolTableNode", "cross_ref": "librosa.display", "kind": "Gdef", "module_public": false}, "effects": {".class": "SymbolTableNode", "cross_ref": "librosa.effects", "kind": "Gdef", "module_public": false}, "estimate_tuning": {".class": "SymbolTableNode", "cross_ref": "librosa.core.pitch.estimate_tuning", "kind": "Gdef"}, "ex": {".class": "SymbolTableNode", "cross_ref": "librosa.util.files.ex", "kind": "Gdef"}, "example": {".class": "SymbolTableNode", "cross_ref": "librosa.util.files.example", "kind": "Gdef"}, "f0_harmonics": {".class": "SymbolTableNode", "cross_ref": "librosa.core.harmonic.f0_harmonics", "kind": "Gdef"}, "feature": {".class": "SymbolTableNode", "cross_ref": "librosa.feature", "kind": "Gdef", "module_public": false}, "fft_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.fft_frequencies", "kind": "Gdef"}, "fifths_to_note": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.fifths_to_note", "kind": "Gdef"}, "filters": {".class": "SymbolTableNode", "cross_ref": "librosa.filters", "kind": "Gdef", "module_public": false}, "fmt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.fmt", "kind": "Gdef"}, "fourier_tempo_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.fourier_tempo_frequencies", "kind": "Gdef"}, "frames_to_samples": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.frames_to_samples", "kind": "Gdef"}, "frames_to_time": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.frames_to_time", "kind": "Gdef"}, "frequency_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.frequency_weighting", "kind": "Gdef"}, "get_duration": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.get_duration", "kind": "Gdef"}, "get_fftlib": {".class": "SymbolTableNode", "cross_ref": "librosa.core.fft.get_fftlib", "kind": "Gdef"}, "get_samplerate": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.get_samplerate", "kind": "Gdef"}, "griffinlim": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.griffinlim", "kind": "Gdef"}, "griffinlim_cqt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.constantq.griffinlim_cqt", "kind": "Gdef"}, "hybrid_cqt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.constantq.hybrid_cqt", "kind": "Gdef"}, "hz_to_fjs": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.hz_to_fjs", "kind": "Gdef"}, "hz_to_mel": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.hz_to_mel", "kind": "Gdef"}, "hz_to_midi": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.hz_to_midi", "kind": "Gdef"}, "hz_to_note": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.hz_to_note", "kind": "Gdef"}, "hz_to_octs": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.hz_to_octs", "kind": "Gdef"}, "hz_to_svara_c": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.hz_to_svara_c", "kind": "Gdef"}, "hz_to_svara_h": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.hz_to_svara_h", "kind": "Gdef"}, "icqt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.constantq.icqt", "kind": "Gdef"}, "iirt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.iirt", "kind": "Gdef"}, "interp_harmonics": {".class": "SymbolTableNode", "cross_ref": "librosa.core.harmonic.interp_harmonics", "kind": "Gdef"}, "interval_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.intervals.interval_frequencies", "kind": "Gdef"}, "interval_to_fjs": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.interval_to_fjs", "kind": "Gdef"}, "istft": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.istft", "kind": "Gdef"}, "key_to_degrees": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.key_to_degrees", "kind": "Gdef"}, "key_to_notes": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.key_to_notes", "kind": "Gdef"}, "list_mela": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.list_mela", "kind": "Gdef"}, "list_thaat": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.list_thaat", "kind": "Gdef"}, "load": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.load", "kind": "Gdef"}, "lpc": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.lpc", "kind": "Gdef"}, "magphase": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.magphase", "kind": "Gdef"}, "mel_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.mel_frequencies", "kind": "Gdef"}, "mel_to_hz": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.mel_to_hz", "kind": "Gdef"}, "mela_to_degrees": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.mela_to_degrees", "kind": "Gdef"}, "mela_to_svara": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.mela_to_svara", "kind": "Gdef"}, "midi_to_hz": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.midi_to_hz", "kind": "Gdef"}, "midi_to_note": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.midi_to_note", "kind": "Gdef"}, "midi_to_svara_c": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.midi_to_svara_c", "kind": "Gdef"}, "midi_to_svara_h": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.midi_to_svara_h", "kind": "Gdef"}, "mu_compress": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.mu_compress", "kind": "Gdef"}, "mu_expand": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.mu_expand", "kind": "Gdef"}, "multi_frequency_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.multi_frequency_weighting", "kind": "Gdef"}, "note_to_hz": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.note_to_hz", "kind": "Gdef"}, "note_to_midi": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.note_to_midi", "kind": "Gdef"}, "note_to_svara_c": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.note_to_svara_c", "kind": "Gdef"}, "note_to_svara_h": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.note_to_svara_h", "kind": "Gdef"}, "octs_to_hz": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.octs_to_hz", "kind": "Gdef"}, "onset": {".class": "SymbolTableNode", "cross_ref": "librosa.onset", "kind": "Gdef", "module_public": false}, "pcen": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.pcen", "kind": "Gdef"}, "perceptual_weighting": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.perceptual_weighting", "kind": "Gdef"}, "phase_vocoder": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.phase_vocoder", "kind": "Gdef"}, "piptrack": {".class": "SymbolTableNode", "cross_ref": "librosa.core.pitch.piptrack", "kind": "Gdef"}, "pitch_tuning": {".class": "SymbolTableNode", "cross_ref": "librosa.core.pitch.pitch_tuning", "kind": "Gdef"}, "plimit_intervals": {".class": "SymbolTableNode", "cross_ref": "librosa.core.intervals.plimit_intervals", "kind": "Gdef"}, "power_to_db": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.power_to_db", "kind": "Gdef"}, "pseudo_cqt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.constantq.pseudo_cqt", "kind": "Gdef"}, "pyin": {".class": "SymbolTableNode", "cross_ref": "librosa.core.pitch.pyin", "kind": "Gdef"}, "pythagorean_intervals": {".class": "SymbolTableNode", "cross_ref": "librosa.core.intervals.pythagorean_intervals", "kind": "Gdef"}, "reassigned_spectrogram": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.reassigned_spectrogram", "kind": "Gdef"}, "resample": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.resample", "kind": "Gdef"}, "salience": {".class": "SymbolTableNode", "cross_ref": "librosa.core.harmonic.salience", "kind": "Gdef"}, "samples_like": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.samples_like", "kind": "Gdef"}, "samples_to_frames": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.samples_to_frames", "kind": "Gdef"}, "samples_to_time": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.samples_to_time", "kind": "Gdef"}, "segment": {".class": "SymbolTableNode", "cross_ref": "librosa.segment", "kind": "Gdef", "module_public": false}, "sequence": {".class": "SymbolTableNode", "cross_ref": "librosa.sequence", "kind": "Gdef", "module_public": false}, "set_fftlib": {".class": "SymbolTableNode", "cross_ref": "librosa.core.fft.set_fftlib", "kind": "Gdef"}, "show_versions": {".class": "SymbolTableNode", "cross_ref": "librosa.version.show_versions", "kind": "Gdef"}, "stft": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.stft", "kind": "Gdef"}, "stream": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.stream", "kind": "Gdef"}, "tempo_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.tempo_frequencies", "kind": "Gdef"}, "thaat_to_degrees": {".class": "SymbolTableNode", "cross_ref": "librosa.core.notation.thaat_to_degrees", "kind": "Gdef"}, "time_to_frames": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.time_to_frames", "kind": "Gdef"}, "time_to_samples": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.time_to_samples", "kind": "Gdef"}, "times_like": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.times_like", "kind": "Gdef"}, "to_mono": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.to_mono", "kind": "Gdef"}, "tone": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.tone", "kind": "Gdef"}, "tuning_to_A4": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.tuning_to_A4", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "librosa.util", "kind": "Gdef", "module_public": false}, "vqt": {".class": "SymbolTableNode", "cross_ref": "librosa.core.constantq.vqt", "kind": "Gdef"}, "yin": {".class": "SymbolTableNode", "cross_ref": "librosa.core.pitch.yin", "kind": "Gdef"}, "zero_crossings": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio.zero_crossings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\__init__.pyi"}