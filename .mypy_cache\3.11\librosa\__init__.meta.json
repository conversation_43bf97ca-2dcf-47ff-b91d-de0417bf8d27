{"data_mtime": 1753781403, "dep_lines": [15, 19, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30], "dependencies": ["librosa.util.exceptions", "librosa.util.files", "librosa.core", "librosa.beat", "librosa.decompose", "librosa.display", "librosa.effects", "librosa.feature", "librosa.filters", "librosa.onset", "librosa.segment", "librosa.sequence", "librosa.util", "librosa._cache", "librosa.version", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "typing", "_frozen_importlib", "abc"], "hash": "f3051972996b1f9ef424662fc8652e5d3f84088b", "id": "librosa", "ignore_all": true, "interface_hash": "c1be687939bb828eb69549072d0db3a7b67a6a3f", "mtime": 1753781252, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\__init__.pyi", "plugin_data": null, "size": 3814, "suppressed": [], "version_id": "1.15.0"}