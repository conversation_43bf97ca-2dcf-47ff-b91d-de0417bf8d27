{"data_mtime": 1753781403, "dep_lines": [19, 20, 23, 24, 25, 21, 22, 26, 29, 4, 6, 7, 8, 12, 22, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 10, 11, 13, 15, 16, 18], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 10, 10, 10, 10, 20, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 10, 10, 10, 10, 10, 10, 5], "dependencies": ["librosa.core.fft", "librosa.core.convert", "librosa.util.exceptions", "librosa.util.decorators", "librosa.util.deprecation", "librosa._cache", "librosa.util", "librosa._typing", "numpy.typing", "__future__", "os", "pathlib", "warnings", "numpy", "librosa", "typing", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "_frozen_importlib", "abc", "numpy._typing", "numpy._typing._dtype_like"], "hash": "6bce1b32a0888fd0149135b1548ca7b4e17ecd0b", "id": "librosa.core.audio", "ignore_all": true, "interface_hash": "3c787fe5219543a3a9dab5da5fa082f5737b1007", "mtime": 1753781252, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\core\\audio.py", "plugin_data": null, "size": 57046, "suppressed": ["scipy.signal", "soundfile", "audioread", "scipy", "soxr", "lazy_loader", "numba"], "version_id": "1.15.0"}