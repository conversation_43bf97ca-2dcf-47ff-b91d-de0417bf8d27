{".class": "MypyFile", "_fullname": "librosa.core.constantq", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "DTypeLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._dtype_like.DTypeLike", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParameterError": {".class": "SymbolTableNode", "cross_ref": "librosa.util.exceptions.ParameterError", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "librosa._typing._FloatLike_co", "kind": "Gdef", "module_public": false}, "_PadMode": {".class": "SymbolTableNode", "cross_ref": "librosa._typing._PadMode", "kind": "Gdef", "module_public": false}, "_WindowSpec": {".class": "SymbolTableNode", "cross_ref": "librosa._typing._WindowSpec", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.core.constantq.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.core.constantq.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__cqt_response": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["y", "n_fft", "hop_length", "fft_basis", "mode", "window", "phase", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.core.constantq.__cqt_response", "name": "__cqt_response", "type": null}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.core.constantq.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__early_downsample": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["y", "sr", "hop_length", "res_type", "n_octaves", "<PERSON><PERSON><PERSON>", "filter_cutoff", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.core.constantq.__early_downsample", "name": "__early_downsample", "type": null}}, "__early_downsample_count": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["<PERSON><PERSON><PERSON>", "filter_cutoff", "hop_length", "n_octaves"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.core.constantq.__early_downsample_count", "name": "__early_downsample_count", "type": null}}, "__et_relative_bw": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bins_per_octave"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.core.constantq.__et_relative_bw", "name": "__et_relative_bw", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["bins_per_octave"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__et_relative_bw", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.core.constantq.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.core.constantq.__name__", "name": "__name__", "type": "builtins.str"}}, "__num_two_factors": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "librosa.core.constantq.__num_two_factors", "name": "__num_two_factors", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "librosa.core.constantq.__num_two_factors", "name": "__num_two_factors", "type": {".class": "AnyType", "missing_import_name": "librosa.core.constantq.jit", "source_any": {".class": "AnyType", "missing_import_name": "librosa.core.constantq.jit", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.core.constantq.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.core.constantq.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__trim_stack": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cqt_resp", "n_bins", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.core.constantq.__trim_stack", "name": "__trim_stack", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cqt_resp", "n_bins", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__trim_stack", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__vqt_filter_fft": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["sr", "freqs", "filter_scale", "norm", "sparsity", "hop_length", "window", "gamma", "dtype", "alpha"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "librosa.core.constantq.__vqt_filter_fft", "name": "__vqt_filter_fft", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "librosa.core.constantq.__vqt_filter_fft", "name": "__vqt_filter_fft", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["sr", "freqs", "filter_scale", "norm", "sparsity", "hop_length", "window", "gamma", "dtype", "alpha"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__vqt_filter_fft", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_ensure_not_reachable": {".class": "SymbolTableNode", "cross_ref": "librosa._typing._ensure_not_reachable", "kind": "Gdef", "module_public": false}, "audio": {".class": "SymbolTableNode", "cross_ref": "librosa.core.audio", "kind": "Gdef", "module_public": false}, "cache": {".class": "SymbolTableNode", "cross_ref": "librosa._cache.cache", "kind": "Gdef", "module_public": false}, "cqt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "librosa.core.constantq.cqt", "name": "cqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "librosa.core.constantq.cqt", "name": "cqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cqt_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.cqt_frequencies", "kind": "Gdef", "module_public": false}, "estimate_tuning": {".class": "SymbolTableNode", "cross_ref": "librosa.core.pitch.estimate_tuning", "kind": "Gdef", "module_public": false}, "filters": {".class": "SymbolTableNode", "cross_ref": "librosa.filters", "kind": "Gdef", "module_public": false}, "get_fftlib": {".class": "SymbolTableNode", "cross_ref": "librosa.core.fft.get_fftlib", "kind": "Gdef", "module_public": false}, "griffinlim_cqt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["C", "n_iter", "sr", "hop_length", "fmin", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype", "length", "momentum", "init", "random_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.core.constantq.griffinlim_cqt", "name": "griffinlim_cqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["C", "n_iter", "sr", "hop_length", "fmin", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype", "length", "momentum", "init", "random_state"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.int", "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "numpy.random.mtrand.RandomState", "numpy.random._generator.Generator", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "griffinlim_cqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hybrid_cqt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "librosa.core.constantq.hybrid_cqt", "name": "hybrid_cqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hybrid_cqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "librosa.core.constantq.hybrid_cqt", "name": "hybrid_cqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hybrid_cqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "icqt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["C", "sr", "hop_length", "fmin", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "length", "res_type", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "librosa.core.constantq.icqt", "name": "icqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["C", "sr", "hop_length", "fmin", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "length", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "librosa.core.constantq.icqt", "name": "icqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["C", "sr", "hop_length", "fmin", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "length", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "interval_frequencies": {".class": "SymbolTableNode", "cross_ref": "librosa.core.intervals.interval_frequencies", "kind": "Gdef", "module_public": false}, "istft": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.istft", "kind": "Gdef", "module_public": false}, "jit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "librosa.core.constantq.jit", "name": "jit", "type": {".class": "AnyType", "missing_import_name": "librosa.core.constantq.jit", "source_any": null, "type_of_any": 3}}}, "note_to_hz": {".class": "SymbolTableNode", "cross_ref": "librosa.core.convert.note_to_hz", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "pseudo_cqt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "librosa.core.constantq.pseudo_cqt", "name": "pseudo_cqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pseudo_cqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "librosa.core.constantq.pseudo_cqt", "name": "pseudo_cqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pseudo_cqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stft": {".class": "SymbolTableNode", "cross_ref": "librosa.core.spectrum.stft", "kind": "Gdef", "module_public": false}, "util": {".class": "SymbolTableNode", "cross_ref": "librosa.util", "kind": "Gdef", "module_public": false}, "vqt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "intervals", "gamma", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "librosa.core.constantq.vqt", "name": "vqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "intervals", "gamma", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "librosa.core.constantq.vqt", "name": "vqt", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["y", "sr", "hop_length", "fmin", "n_bins", "intervals", "gamma", "bins_per_octave", "tuning", "filter_scale", "norm", "sparsity", "window", "scale", "pad_mode", "res_type", "dtype"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._WindowSpec"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._PadMode"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._dtype_like.DTypeLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vqt", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\core\\constantq.py"}