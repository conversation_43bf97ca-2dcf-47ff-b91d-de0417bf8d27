{"data_mtime": 1753781403, "dep_lines": [11, 12, 13, 18, 12, 14, 15, 16, 19, 21, 5, 6, 15, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 8], "dep_prios": [5, 10, 10, 5, 20, 5, 5, 10, 5, 5, 10, 10, 20, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["librosa.core.spectrum", "librosa.core.convert", "librosa.core.audio", "librosa.util.exceptions", "librosa.core", "librosa._cache", "librosa.util", "librosa.sequence", "numpy.typing", "librosa._typing", "warnings", "numpy", "librosa", "typing", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "_frozen_importlib", "abc", "librosa.util.deprecation", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence"], "hash": "eea3fcbcc7dd73b095f5643441da6905f55e60f9", "id": "librosa.core.pitch", "ignore_all": true, "interface_hash": "ee63b2ecb82f23ac15de4611d183aacf45a69b41", "mtime": 1753781252, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\core\\pitch.py", "plugin_data": null, "size": 34438, "suppressed": ["scipy", "numba"], "version_id": "1.15.0"}