{"data_mtime": 1753781403, "dep_lines": [15, 16, 17, 20, 15, 18, 19, 21, 23, 26, 4, 5, 7, 19, 24, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 10, 11, 8, 13], "dep_prios": [10, 5, 5, 5, 20, 5, 10, 5, 5, 5, 5, 10, 10, 20, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10, 10, 5], "dependencies": ["librosa.core.convert", "librosa.core.fft", "librosa.core.audio", "librosa.util.exceptions", "librosa.core", "librosa._cache", "librosa.util", "librosa.filters", "numpy.typing", "librosa._typing", "__future__", "warnings", "numpy", "librosa", "typing", "typing_extensions", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "_frozen_importlib", "abc", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy.random", "numpy.random._generator", "numpy.random.mtrand"], "hash": "119bdd4794c73ee83b6840d997bdcccbc5314748", "id": "librosa.core.spectrum", "ignore_all": true, "interface_hash": "1f63b48698d5c5397d95e6e1c81748880717d56c", "mtime": 1753781252, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\core\\spectrum.py", "plugin_data": null, "size": 100040, "suppressed": ["scipy.ndimage", "scipy.signal", "scipy.interpolate", "scipy", "numba"], "version_id": "1.15.0"}