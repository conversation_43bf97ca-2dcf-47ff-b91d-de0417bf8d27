{".class": "MypyFile", "_fullname": "librosa.display", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdaptiveWaveplot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.AdaptiveWaveplot", "name": "AdaptiveWaveplot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.AdaptiveWaveplot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.AdaptiveWaveplot", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.AdaptiveWaveplot.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["librosa.display.AdaptiveWaveplot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of AdaptiveWaveplot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "times", "y", "steps", "envelope", "sr", "max_samples", "transpose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.AdaptiveWaveplot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "times", "y", "steps", "envelope", "sr", "max_samples", "transpose"], "arg_types": ["librosa.display.AdaptiveWaveplot", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "matplotlib.lines.Line2D", "matplotlib.collections.PolyCollection", "builtins.float", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AdaptiveWaveplot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.ax", "name": "ax", "type": {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.cid", "name": "cid", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "ax", "signal"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.AdaptiveWaveplot.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "ax", "signal"], "arg_types": ["librosa.display.AdaptiveWaveplot", "matplotlib.axes._axes.Axes", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of AdaptiveWaveplot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.AdaptiveWaveplot.disconnect", "name": "disconnect", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "strict"], "arg_types": ["librosa.display.AdaptiveWaveplot", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disconnect of AdaptiveWaveplot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "envelope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.envelope", "name": "envelope", "type": "matplotlib.collections.PolyCollection"}}, "max_samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.max_samples", "name": "max_samples", "type": "builtins.int"}}, "samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.samples", "name": "samples", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "sr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.sr", "name": "sr", "type": "builtins.float"}}, "steps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.steps", "name": "steps", "type": "matplotlib.lines.Line2D"}}, "times": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.times", "name": "times", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "transpose": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.AdaptiveWaveplot.transpose", "name": "transpose", "type": "builtins.bool"}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ax"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.AdaptiveWaveplot.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ax"], "arg_types": ["librosa.display.AdaptiveWaveplot", "matplotlib.axes._axes.Axes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of AdaptiveWaveplot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.AdaptiveWaveplot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.AdaptiveWaveplot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ChromaFJSFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.ChromaFJSFormatter", "name": "ChromaFJSFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaFJSFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.ChromaFJSFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaFJSFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.ChromaFJSFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ChromaFJSFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "intervals", "unison", "unicode", "bins_per_octave"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaFJSFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5], "arg_names": ["self", "intervals", "unison", "unicode", "bins_per_octave"], "arg_types": ["librosa.display.ChromaFJSFormatter", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChromaFJSFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bins_per_octave": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "librosa.display.ChromaFJSFormatter.bins_per_octave", "name": "bins_per_octave", "type": "builtins.int"}}, "intervals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaFJSFormatter.intervals", "name": "intervals", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": false}}}, "intervals_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaFJSFormatter.intervals_", "name": "intervals_", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "unicode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaFJSFormatter.unicode", "name": "unicode", "type": "builtins.bool"}}, "unison": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaFJSFormatter.unison", "name": "unison", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.ChromaFJSFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.ChromaFJSFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChromaFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.ChromaFormatter", "name": "ChromaFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.ChromaFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.ChromaFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ChromaFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "key", "unicode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "key", "unicode"], "arg_types": ["librosa.display.ChromaFormatter", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChromaFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaFormatter.key", "name": "key", "type": "builtins.str"}}, "unicode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaFormatter.unicode", "name": "unicode", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.ChromaFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.ChromaFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChromaSvaraFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.ChromaSvaraFormatter", "name": "ChromaSvara<PERSON><PERSON>atter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaSvaraFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.ChromaSvaraFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "Sa": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaSvaraFormatter.Sa", "name": "Sa", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.float"], "uses_pep604_syntax": false}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaSvaraFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.ChromaSvaraFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ChromaSvaraFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "Sa", "mela", "abbr", "unicode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.ChromaSvaraFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "Sa", "mela", "abbr", "unicode"], "arg_types": ["librosa.display.ChromaSvaraFormatter", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChromaSvaraFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abbr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaSvaraFormatter.abbr", "name": "abbr", "type": "builtins.bool"}}, "mela": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaSvaraFormatter.mela", "name": "mela", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "unicode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.ChromaSvaraFormatter.unicode", "name": "unicode", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.ChromaSvaraFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.ChromaSvaraFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "Colormap": {".class": "SymbolTableNode", "cross_ref": "matplotlib.colors.Colormap", "kind": "Gdef", "module_public": false}, "Deprecated": {".class": "SymbolTableNode", "cross_ref": "librosa.util.deprecation.Deprecated", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "FJSFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.FJSFormatter", "name": "FJSFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.FJSFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.FJSFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.FJSFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.FJSFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of FJSFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "fmin", "n_bins", "bins_per_octave", "intervals", "major", "unison", "unicode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.FJSFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "fmin", "n_bins", "bins_per_octave", "intervals", "major", "unison", "unicode"], "arg_types": ["librosa.display.FJSFormatter", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FJSFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bins_per_octave": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.bins_per_octave", "name": "bins_per_octave", "type": "builtins.int"}}, "fmin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.fmin", "name": "fmin", "type": "builtins.int"}}, "frequencies_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.frequencies_", "name": "frequencies_", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}}}, "intervals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.intervals", "name": "intervals", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Collection"}], "uses_pep604_syntax": false}}}, "major": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.major", "name": "major", "type": "builtins.bool"}}, "n_bins": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.n_bins", "name": "n_bins", "type": "builtins.int"}}, "unicode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.unicode", "name": "unicode", "type": "builtins.bool"}}, "unison": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.FJSFormatter.unison", "name": "unison", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.FJSFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.FJSFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Line2D": {".class": "SymbolTableNode", "cross_ref": "matplotlib.lines.Line2D", "kind": "Gdef", "module_public": false}, "LogHzFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.LogHzFormatter", "name": "LogHzFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.LogHzFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.LogHzFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.LogHzFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.LogHzFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of LogHzFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "major"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.LogHzFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "major"], "arg_types": ["librosa.display.LogHzFormatter", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LogHzFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "major": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.LogHzFormatter.major", "name": "major", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.LogHzFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.LogHzFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MarkerStyle": {".class": "SymbolTableNode", "cross_ref": "matplotlib.markers.MarkerStyle", "kind": "Gdef", "module_public": false}, "MplPath": {".class": "SymbolTableNode", "cross_ref": "matplotlib.path.Path", "kind": "Gdef", "module_public": false}, "NoteFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.NoteFormatter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.NoteFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.NoteFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.NoteFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.NoteFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of NoteFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "octave", "major", "key", "unicode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.NoteFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "octave", "major", "key", "unicode"], "arg_types": ["librosa.display.NoteFormatter", "builtins.bool", "builtins.bool", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoteFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.NoteFormatter.key", "name": "key", "type": "builtins.str"}}, "major": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.NoteFormatter.major", "name": "major", "type": "builtins.bool"}}, "octave": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.NoteFormatter.octave", "name": "octave", "type": "builtins.bool"}}, "unicode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.NoteFormatter.unicode", "name": "unicode", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.NoteFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.NoteFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParameterError": {".class": "SymbolTableNode", "cross_ref": "librosa.util.exceptions.ParameterError", "kind": "Gdef", "module_public": false}, "PolyCollection": {".class": "SymbolTableNode", "cross_ref": "matplotlib.collections.PolyCollection", "kind": "Gdef", "module_public": false}, "QuadMesh": {".class": "SymbolTableNode", "cross_ref": "matplotlib.collections.QuadMesh", "kind": "Gdef", "module_public": false}, "SvaraFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.SvaraFormatter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.SvaraFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.SvaraFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "Sa": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.SvaraFormatter.Sa", "name": "Sa", "type": "builtins.float"}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.SvaraFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.SvaraFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "Sa", "octave", "major", "abbr", "mela", "unicode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.SvaraFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "Sa", "octave", "major", "abbr", "mela", "unicode"], "arg_types": ["librosa.display.SvaraFormatter", "builtins.float", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abbr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.SvaraFormatter.abbr", "name": "abbr", "type": "builtins.bool"}}, "major": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.SvaraFormatter.major", "name": "major", "type": "builtins.bool"}}, "mela": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.SvaraFormatter.mela", "name": "mela", "type": {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "octave": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.SvaraFormatter.octave", "name": "octave", "type": "builtins.bool"}}, "unicode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.SvaraFormatter.unicode", "name": "unicode", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.SvaraFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.SvaraFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TimeFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.TimeFormatter", "name": "Time<PERSON>ormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.TimeFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.TimeFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.TimeFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.TimeFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of TimeFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "lag", "unit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.TimeFormatter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "lag", "unit"], "arg_types": ["librosa.display.TimeFormatter", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimeFormatter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.TimeFormatter.lag", "name": "lag", "type": "builtins.bool"}}, "unit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "librosa.display.TimeFormatter.unit", "name": "unit", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.TimeFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.TimeFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TonnetzFormatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.ticker.Formatter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "librosa.display.TonnetzFormatter", "name": "TonnetzFormatter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "librosa.display.TonnetzFormatter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "librosa.display", "mro": ["librosa.display.TonnetzFormatter", "matplotlib.ticker.Formatter", "matplotlib.ticker.TickHelper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.TonnetzFormatter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "pos"], "arg_types": ["librosa.display.TonnetzFormatter", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of TonnetzFormatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "librosa.display.TonnetzFormatter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "librosa.display.TonnetzFormatter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_AXIS_COMPAT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display._AXIS_COMPAT", "name": "_AXIS_COMPAT", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_FloatLike_co": {".class": "SymbolTableNode", "cross_ref": "librosa._typing._FloatLike_co", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.display.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__check_axes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["axes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__check_axes", "name": "__check_axes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["axes"], "arg_types": [{".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__check_axes", "ret_type": "matplotlib.axes._axes.Axes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_chroma": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["n", "bins_per_octave", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_chroma", "name": "__coord_chroma", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["n", "bins_per_octave", "_kwargs"], "arg_types": ["builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_chroma", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_cqt_hz": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["n", "fmin", "bins_per_octave", "sr", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_cqt_hz", "name": "__coord_cqt_hz", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["n", "fmin", "bins_per_octave", "sr", "_kwargs"], "arg_types": ["builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_cqt_hz", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_fft_hz": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["n", "sr", "n_fft", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_fft_hz", "name": "__coord_fft_hz", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["n", "sr", "n_fft", "_kwargs"], "arg_types": ["builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_fft_hz", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_fourier_tempo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["n", "sr", "hop_length", "win_length", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_fourier_tempo", "name": "__coord_fourier_tempo", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["n", "sr", "hop_length", "win_length", "_kwargs"], "arg_types": ["builtins.int", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_fourier_tempo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_mel_hz": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["n", "fmin", "fmax", "sr", "htk", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_mel_hz", "name": "__coord_mel_hz", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["n", "fmin", "fmax", "sr", "htk", "_kwargs"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_mel_hz", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_n": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["n", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_n", "name": "__coord_n", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["n", "_kwargs"], "arg_types": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_n", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_tempo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["n", "sr", "hop_length", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_tempo", "name": "__coord_tempo", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["n", "sr", "hop_length", "_kwargs"], "arg_types": ["builtins.int", "builtins.float", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_tempo", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_time": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["n", "sr", "hop_length", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_time", "name": "__coord_time", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["n", "sr", "hop_length", "_kwargs"], "arg_types": ["builtins.int", "builtins.float", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_time", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__coord_vqt_hz": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["n", "fmin", "bins_per_octave", "sr", "intervals", "unison", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__coord_vqt_hz", "name": "__coord_vqt_hz", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["n", "fmin", "bins_per_octave", "sr", "intervals", "unison", "_kwargs"], "arg_types": ["builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "librosa._typing._FloatLike_co"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__coord_vqt_hz", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__decorate_axis": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["axis", "ax_type", "key", "Sa", "mela", "thaat", "unicode", "fmin", "unison", "intervals", "bins_per_octave", "n_bins"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__decorate_axis", "name": "__decorate_axis", "type": null}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.display.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__envelope": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "hop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__envelope", "name": "__envelope", "type": null}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.display.__file__", "name": "__file__", "type": "builtins.str"}}, "__mesh_coords": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["ax_type", "coords", "n", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__mesh_coords", "name": "__mesh_coords", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.display.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.display.__package__", "name": "__package__", "type": "builtins.str"}}, "__same_axes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["x_axis", "y_axis", "xlim", "ylim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__same_axes", "name": "__same_axes", "type": null}}, "__scale_axes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["axes", "ax_type", "which", "tempo_min", "tempo_max"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__scale_axes", "name": "__scale_axes", "type": null}}, "__set_current_image": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ax", "img"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.__set_current_image", "name": "__set_current_image", "type": null}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.display.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_chroma_ax_types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display._chroma_ax_types", "name": "_chroma_ax_types", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_cqt_ax_types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display._cqt_ax_types", "name": "_cqt_ax_types", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_freq_ax_types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display._freq_ax_types", "name": "_freq_ax_types", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_lag_ax_types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display._lag_ax_types", "name": "_lag_ax_types", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_misc_ax_types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display._misc_ax_types", "name": "_misc_ax_types", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_time_ax_types": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "librosa.display._time_ax_types", "name": "_time_ax_types", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["data", "robust", "cmap_seq", "cmap_bool", "cmap_div"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.cmap", "name": "cmap", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["data", "robust", "cmap_seq", "cmap_bool", "cmap_div"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.bool", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cmap", "ret_type": "matplotlib.colors.Colormap", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "core": {".class": "SymbolTableNode", "cross_ref": "librosa.core", "kind": "Gdef", "module_public": false}, "matplotlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "mcm": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cm._colormaps", "kind": "Gdef", "module_public": false}, "mplaxes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes", "kind": "Gdef", "module_public": false}, "mplticker": {".class": "SymbolTableNode", "cross_ref": "matplotlib.ticker", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "plt": {".class": "SymbolTableNode", "cross_ref": "matplotlib.pyplot", "kind": "Gdef", "module_public": false}, "product": {".class": "SymbolTableNode", "cross_ref": "itertools.product", "kind": "Gdef", "module_public": false}, "rename_kw": {".class": "SymbolTableNode", "cross_ref": "librosa.util.deprecation.rename_kw", "kind": "Gdef", "module_public": false}, "specshow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["data", "x_coords", "y_coords", "x_axis", "y_axis", "sr", "hop_length", "n_fft", "win_length", "fmin", "fmax", "tempo_min", "tempo_max", "tuning", "bins_per_octave", "key", "Sa", "mela", "thaat", "auto_aspect", "htk", "unicode", "intervals", "unison", "ax", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.specshow", "name": "specshow", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["data", "x_coords", "y_coords", "x_axis", "y_axis", "sr", "hop_length", "n_fft", "win_length", "fmin", "fmax", "tempo_min", "tempo_max", "tuning", "bins_per_octave", "key", "Sa", "mela", "thaat", "auto_aspect", "htk", "unicode", "intervals", "unison", "ax", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.float", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "specshow", "ret_type": "matplotlib.collections.QuadMesh", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "util": {".class": "SymbolTableNode", "cross_ref": "librosa.util", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}, "waveshow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["y", "sr", "max_points", "axis", "offset", "marker", "where", "label", "transpose", "ax", "x_axis", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "librosa.display.waveshow", "name": "waveshow", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["y", "sr", "max_points", "axis", "offset", "marker", "where", "label", "transpose", "ax", "x_axis", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", "matplotlib.path.Path", "matplotlib.markers.MarkerStyle"], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["matplotlib.axes._axes.Axes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "librosa.util.deprecation.Deprecated", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waveshow", "ret_type": "librosa.display.AdaptiveWaveplot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\display.py"}