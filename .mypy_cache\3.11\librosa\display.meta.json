{"data_mtime": 1753781403, "dep_lines": [51, 52, 45, 46, 47, 49, 50, 54, 58, 59, 60, 61, 62, 39, 40, 41, 43, 44, 49, 53, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 5, 25, 25, 25, 25, 25, 5, 5, 10, 10, 5, 20, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["librosa.util.deprecation", "librosa.util.exceptions", "matplotlib.axes", "matplotlib.ticker", "matplotlib.pyplot", "librosa.core", "librosa.util", "librosa._typing", "matplotlib.collections", "matplotlib.lines", "matplotlib.path", "matplotlib.markers", "matplotlib.colors", "__future__", "itertools", "warnings", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "librosa", "typing", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "_frozen_importlib", "_typeshed", "abc", "librosa.core.intervals", "matplotlib.artist", "matplotlib.axes._axes", "matplotlib.axes._base", "matplotlib.cbook", "matplotlib.colorizer", "typing_extensions"], "hash": "82064adf3ac1a6161e55b7416af91a40cfced243", "id": "librosa.display", "ignore_all": true, "interface_hash": "042ad79167badbf59666f7d9b21da8631ce4649f", "mtime": 1753781252, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\display.py", "plugin_data": null, "size": 65536, "suppressed": [], "version_id": "1.15.0"}