{".class": "MypyFile", "_fullname": "librosa.feature", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.feature.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.feature.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.feature.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.feature.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.feature.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.feature.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "librosa.feature.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "chroma_cens": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.chroma_cens", "kind": "Gdef"}, "chroma_cqt": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.chroma_cqt", "kind": "Gdef"}, "chroma_stft": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.chroma_stft", "kind": "Gdef"}, "chroma_vqt": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.chroma_vqt", "kind": "Gdef"}, "delta": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.utils.delta", "kind": "Gdef"}, "fourier_tempogram": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.rhythm.fourier_tempogram", "kind": "Gdef"}, "inverse": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.inverse", "kind": "Gdef"}, "melspectrogram": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.melspectrogram", "kind": "Gdef"}, "mfcc": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.mfcc", "kind": "Gdef"}, "poly_features": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.poly_features", "kind": "Gdef"}, "rms": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.rms", "kind": "Gdef"}, "spectral_bandwidth": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.spectral_bandwidth", "kind": "Gdef"}, "spectral_centroid": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.spectral_centroid", "kind": "Gdef"}, "spectral_contrast": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.spectral_contrast", "kind": "Gdef"}, "spectral_flatness": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.spectral_flatness", "kind": "Gdef"}, "spectral_rolloff": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.spectral_rolloff", "kind": "Gdef"}, "stack_memory": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.utils.stack_memory", "kind": "Gdef"}, "tempo": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.rhythm.tempo", "kind": "Gdef"}, "tempogram": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.rhythm.tempogram", "kind": "Gdef"}, "tempogram_ratio": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.rhythm.tempogram_ratio", "kind": "Gdef"}, "tonnetz": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.tonnetz", "kind": "Gdef"}, "zero_crossing_rate": {".class": "SymbolTableNode", "cross_ref": "librosa.feature.spectral.zero_crossing_rate", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\feature\\__init__.pyi"}