{"data_mtime": 1753781403, "dep_lines": [56, 57, 59, 54, 55, 61, 64, 45, 47, 55, 62, 63, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 49, 50, 48, 52], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 10, 10, 20, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 10, 10, 10, 5], "dependencies": ["librosa.util.exceptions", "librosa.util.decorators", "librosa.core.convert", "librosa._cache", "librosa.util", "numpy.typing", "librosa._typing", "warnings", "numpy", "librosa", "typing", "typing_extensions", "builtins", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "_frozen_importlib", "abc", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence"], "hash": "c7e36473ec794185fd5884242219ba2307c2419f", "id": "librosa.filters", "ignore_all": true, "interface_hash": "2cf6a7f491bbee9f037b38970509452015835510", "mtime": 1753781252, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\librosa\\filters.py", "plugin_data": null, "size": 51205, "suppressed": ["scipy.signal", "scipy.ndimage", "scipy", "numba"], "version_id": "1.15.0"}