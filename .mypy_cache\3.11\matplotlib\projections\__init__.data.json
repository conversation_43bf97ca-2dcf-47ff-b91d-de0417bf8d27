{".class": "MypyFile", "_fullname": "matplotlib.projections", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "AitoffAxes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.projections.geo.AitoffAxes", "kind": "Gdef"}, "Axes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.axes._axes.Axes", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HammerAxes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.projections.geo.HammerAxes", "kind": "Gdef"}, "LambertAxes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.projections.geo.LambertAxes", "kind": "Gdef"}, "MollweideAxes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.projections.geo.MollweideAxes", "kind": "Gdef"}, "PolarAxes": {".class": "SymbolTableNode", "cross_ref": "matplotlib.projections.polar.PolarAxes", "kind": "Gdef"}, "ProjectionRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.projections.ProjectionRegistry", "name": "ProjectionRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.projections.ProjectionRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.projections", "mro": ["matplotlib.projections.ProjectionRegistry", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.ProjectionRegistry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.ProjectionRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProjectionRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_projection_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.ProjectionRegistry.get_projection_class", "name": "get_projection_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["matplotlib.projections.ProjectionRegistry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_projection_class of ProjectionRegistry", "ret_type": {".class": "TypeType", "item": "matplotlib.axes._axes.Axes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_projection_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.ProjectionRegistry.get_projection_names", "name": "get_projection_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.projections.ProjectionRegistry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_projection_names of ProjectionRegistry", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "projections"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.ProjectionRegistry.register", "name": "register", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "projections"], "arg_types": ["matplotlib.projections.ProjectionRegistry", {".class": "TypeType", "item": "matplotlib.axes._axes.Axes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register of ProjectionRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.projections.ProjectionRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.projections.ProjectionRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "get_projection_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["projection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.get_projection_class", "name": "get_projection_class", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["projection"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_projection_class", "ret_type": {".class": "TypeType", "item": "matplotlib.axes._axes.Axes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_projection_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.get_projection_names", "name": "get_projection_names", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_projection_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "projection_registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.projections.projection_registry", "name": "projection_registry", "type": "matplotlib.projections.ProjectionRegistry"}}, "register_projection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.projections.register_projection", "name": "register_projection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "matplotlib.axes._axes.Axes"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_projection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\projections\\__init__.pyi"}