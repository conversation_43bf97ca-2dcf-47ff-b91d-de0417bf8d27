{"data_mtime": 1753781401, "dep_lines": [102, 57, 58, 59, 62, 63, 64, 66, 67, 68, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 83, 88, 95, 96, 101, 105, 106, 116, 122, 125, 126, 128, 135, 144, 145, 150, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 85, 89, 90, 91, 93, 95, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 10, 10, 10, 5, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 5, 25, 25, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 25, 25, 20, 25, 25, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["matplotlib.axes._base", "matplotlib.colorbar", "matplotlib.image", "matplotlib._api", "matplotlib.cm", "matplotlib.style", "matplotlib._pylab_helpers", "matplotlib.cbook", "matplotlib._docstring", "matplotlib.backend_bases", "matplotlib.figure", "matplotlib.gridspec", "matplotlib.rcsetup", "matplotlib.artist", "matplotlib.axes", "matplotlib.backends", "matplotlib.projections", "matplotlib.colorizer", "matplotlib.mlab", "matplotlib.scale", "matplotlib.colors", "collections.abc", "PIL.Image", "numpy.typing", "matplotlib.axis", "matplotlib.contour", "matplotlib.collections", "matplotlib.container", "matplotlib.legend", "matplotlib.patches", "matplotlib.quiver", "matplotlib.typing", "matplotlib.widgets", "matplotlib.lines", "matplotlib.text", "matplotlib.ticker", "__future__", "contextlib", "enum", "functools", "importlib", "inspect", "logging", "sys", "threading", "time", "typing", "cycler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "numpy", "datetime", "pathlib", "os", "typing_extensions", "PIL", "builtins", "warnings", "pprint", "operator", "collections", "string", "json", "copy", "itertools", "html", "traitlets.utils.warnings", "re", "types", "traceback", "_frozen_importlib", "abc", "matplotlib._api.deprecation", "matplotlib.axes._axes", "matplotlib.backends.registry", "matplotlib.layout_engine", "matplotlib.markers", "matplotlib.path", "matplotlib.stackplot", "matplotlib.streamplot", "matplotlib.table", "matplotlib.transforms", "matplotlib.tri", "matplotlib.tri._triangulation", "matplotlib.tri._tricontour", "matplotlib.tri._tripcolor", "matplotlib.tri._triplot", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence"], "hash": "9d66acd176f4b9c0779272d87d36117b536377c3", "id": "matplotlib.pyplot", "ignore_all": true, "interface_hash": "e704d9069f059615c3516ab4afa035a7a8686418", "mtime": 1746804123, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\pyplot.py", "plugin_data": null, "size": 149704, "suppressed": [], "version_id": "1.15.0"}