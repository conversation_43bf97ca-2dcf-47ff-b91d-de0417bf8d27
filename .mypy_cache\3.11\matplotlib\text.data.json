{".class": "MypyFile", "_fullname": "matplotlib.text", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.text.Text", "matplotlib.text._AnnotationBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.text.Annotation", "name": "Annotation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.text.Annotation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.text", "mro": ["matplotlib.text.Annotation", "matplotlib.text.Text", "matplotlib.artist.Artist", "matplotlib.text._AnnotationBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "xy", "xytext", "xycoords", "textcoords", "arrowprops", "annotation_clip", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Annotation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "xy", "xytext", "xycoords", "textcoords", "arrowprops", "annotation_clip", "kwargs"], "arg_types": ["matplotlib.text.Annotation", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Annotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "anncoords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.text.Annotation.anncoords", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.text.Annotation.anncoords", "name": "an<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of Annotation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.text.Annotation.anncoords", "name": "an<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of Annotation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.text.Annotation.anncoords", "name": "an<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "arg_types": ["matplotlib.text.Annotation", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of Annotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "an<PERSON><PERSON><PERSON>", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "anncoords of Annotation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "arrow_patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.text.Annotation.arrow_patch", "name": "arrow_patch", "type": {".class": "UnionType", "items": ["matplotlib.patches.FancyArrowPatch", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "arrowprops": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.text.Annotation.arrowprops", "name": "arrowprops", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_anncoords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Annotation.get_anncoords", "name": "get_anncoords", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_anncoords of Annotation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_window_extent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Annotation.get_window_extent", "name": "get_window_extent", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.text.Annotation", {".class": "UnionType", "items": ["matplotlib.backend_bases.RendererBase", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_extent of Annotation", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_anncoords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Annotation.set_anncoords", "name": "set_anncoords", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "coords"], "arg_types": ["matplotlib.text.Annotation", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_anncoords of Annotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Annotation.update_positions", "name": "update_positions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.text.Annotation", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_positions of Annotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xyann": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.text.Annotation.xyann", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.text.Annotation.xyann", "name": "xyann", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of Annotation", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.text.Annotation.xyann", "name": "xyann", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of Annotation", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xytext"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.text.Annotation.xyann", "name": "xyann", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xytext"], "arg_types": ["matplotlib.text.Annotation", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of Annotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "xyann", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xyann of Annotation", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "xycoords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "matplotlib.text.Annotation.xycoords", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "matplotlib.text.Annotation.xycoords", "name": "xycoords", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xycoords of Annotation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "matplotlib.text.Annotation.xycoords", "name": "xycoords", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xycoords of Annotation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xycoords"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "matplotlib.text.Annotation.xycoords", "name": "xycoords", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xycoords"], "arg_types": ["matplotlib.text.Annotation", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xycoords of Annotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "xycoords", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Annotation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xycoords of Annotation", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.text.Annotation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.text.Annotation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Artist": {".class": "SymbolTableNode", "cross_ref": "matplotlib.artist.Artist", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Bbox": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Bbox", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BboxBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.BboxBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ColorType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.ColorType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CoordsType": {".class": "SymbolTableNode", "cross_ref": "matplotlib.typing.CoordsType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DraggableAnnotation": {".class": "SymbolTableNode", "cross_ref": "matplotlib.offsetbox.DraggableAnnotation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FancyArrowPatch": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches.FancyArrowPatch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FancyBboxPatch": {".class": "SymbolTableNode", "cross_ref": "matplotlib.patches.FancyBboxPatch", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FontProperties": {".class": "SymbolTableNode", "cross_ref": "matplotlib.font_manager.FontProperties", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OffsetFrom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.text.OffsetFrom", "name": "OffsetFrom", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.text.OffsetFrom", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.text", "mro": ["matplotlib.text.OffsetFrom", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.OffsetFrom.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.text.OffsetFrom", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of OffsetFrom", "ret_type": "matplotlib.transforms.Transform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "artist", "ref_coord", "unit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.OffsetFrom.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "artist", "ref_coord", "unit"], "arg_types": ["matplotlib.text.OffsetFrom", {".class": "UnionType", "items": ["matplotlib.artist.Artist", "matplotlib.transforms.BboxBase", "matplotlib.transforms.Transform"], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "points"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pixels"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OffsetFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.OffsetFrom.get_unit", "name": "get_unit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.OffsetFrom"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unit of OffsetFrom", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "points"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pixels"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_unit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "unit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.OffsetFrom.set_unit", "name": "set_unit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unit"], "arg_types": ["matplotlib.text.OffsetFrom", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "points"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pixels"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_unit of OffsetFrom", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.text.OffsetFrom.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.text.OffsetFrom", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "matplotlib.path.Path", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RendererBase": {".class": "SymbolTableNode", "cross_ref": "matplotlib.backend_bases.RendererBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.artist.Artist"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.text.Text", "name": "Text", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.text", "mro": ["matplotlib.text.Text", "matplotlib.artist.Artist", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "x", "y", "text", "color", "verticalalignment", "horizontalalignment", "multialignment", "fontproperties", "rotation", "linespacing", "rotation_mode", "usetex", "wrap", "transform_rotates_text", "parse_math", "antialiased", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "x", "y", "text", "color", "verticalalignment", "horizontalalignment", "multialignment", "fontproperties", "rotation", "linespacing", "rotation_mode", "usetex", "wrap", "transform_rotates_text", "parse_math", "antialiased", "kwargs"], "arg_types": ["matplotlib.text.Text", "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "matplotlib.path.Path", "matplotlib.font_manager.FontProperties", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "LiteralType", "fallback": "builtins.str", "value": "vertical"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "horizontal"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "anchor"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_antialiased": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_antialiased", "name": "get_antialiased", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_antialiased of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_bbox_patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_bbox_patch", "name": "get_bbox_patch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bbox_patch of Text", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "matplotlib.patches.FancyBboxPatch"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_color", "name": "get_color", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_color of Text", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontfamily": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_fontfamily", "name": "get_fontfamily", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontfamily of Text", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_fontname", "name": "get_fontname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontname of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontproperties": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_fontproperties", "name": "get_fontproperties", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontproperties of Text", "ret_type": "matplotlib.font_manager.FontProperties", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_fontsize", "name": "get_fontsize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontsize of Text", "ret_type": {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_fontstyle", "name": "get_fontstyle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontstyle of Text", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "oblique"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontvariant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_fontvariant", "name": "get_fontvariant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontvariant of Text", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "small-caps"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_fontweight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_fontweight", "name": "get_fontweight", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_fontweight of Text", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_horizontalalignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_horizontalalignment", "name": "get_horizontalalignment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_horizontalalignment of Text", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_math_fontfamily": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_math_fontfamily", "name": "get_math_fontfamily", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_math_fontfamily of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_parse_math": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_parse_math", "name": "get_parse_math", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_parse_math of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_position", "name": "get_position", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_position of Text", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_rotation", "name": "get_rotation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rotation of Text", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rotation_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_rotation_mode", "name": "get_rotation_mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rotation_mode of Text", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "anchor"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stretch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_stretch", "name": "get_stretch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stretch of Text", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_text", "name": "get_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_text of Text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_transform_rotates_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_transform_rotates_text", "name": "get_transform_rotates_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transform_rotates_text of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_unitless_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_unitless_position", "name": "get_unitless_position", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_unitless_position of Text", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_usetex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_usetex", "name": "get_usetex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_usetex of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_verticalalignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_verticalalignment", "name": "get_verticalalignment", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_verticalalignment of Text", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_window_extent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "renderer", "dpi"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_window_extent", "name": "get_window_extent", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "renderer", "dpi"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["matplotlib.backend_bases.RendererBase", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_extent of Text", "ret_type": "matplotlib.transforms.Bbox", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.get_wrap", "name": "get_wrap", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text.Text"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_wrap of Text", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_antialiased": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "antialiased"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_antialiased", "name": "set_antialiased", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "antialiased"], "arg_types": ["matplotlib.text.Text", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_antialiased of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_backgroundcolor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_backgroundcolor", "name": "set_backgroundcolor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "color"], "arg_types": ["matplotlib.text.Text", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_backgroundcolor of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_bbox": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rectprops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_bbox", "name": "set_bbox", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rectprops"], "arg_types": ["matplotlib.text.Text", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_bbox of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_color": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_color", "name": "set_color", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "color"], "arg_types": ["matplotlib.text.Text", {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.ColorType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_color of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontfamily": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fontname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontfamily", "name": "set_fontfamily", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fontname"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontfamily of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fontname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontname", "name": "set_fontname", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fontname"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontname of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontproperties": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontproperties", "name": "set_fontproperties", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fp"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["matplotlib.font_manager.FontProperties", "builtins.str", "matplotlib.path.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontproperties of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontsize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fontsize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontsize", "name": "set_fontsize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fontsize"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["builtins.float", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontsize of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontstretch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stretch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontstretch", "name": "set_fontstretch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stretch"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontstretch of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fontstyle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontstyle", "name": "set_fontstyle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fontstyle"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "italic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "oblique"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontstyle of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontvariant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "variant"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontvariant", "name": "set_fontvariant", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "variant"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "normal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "small-caps"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontvariant of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_fontweight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_fontweight", "name": "set_fontweight", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "weight"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_fontweight of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_horizontalalignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "align"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_horizontalalignment", "name": "set_horizontalalignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "align"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_horizontalalignment of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_linespacing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "spacing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_linespacing", "name": "set_linespacing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "spacing"], "arg_types": ["matplotlib.text.Text", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_linespacing of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_math_fontfamily": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fontfamily"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_math_fontfamily", "name": "set_math_fontfamily", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fontfamily"], "arg_types": ["matplotlib.text.Text", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_math_fontfamily of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_multialignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "align"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_multialignment", "name": "set_multialignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "align"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "left"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "right"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_multialignment of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_parse_math": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parse_math"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_parse_math", "name": "set_parse_math", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parse_math"], "arg_types": ["matplotlib.text.Text", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_parse_math of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_position", "name": "set_position", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xy"], "arg_types": ["matplotlib.text.Text", {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_position of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_rotation", "name": "set_rotation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.text.Text", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rotation of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_rotation_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "m"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_rotation_mode", "name": "set_rotation_mode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "m"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "anchor"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_rotation_mode of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_text", "name": "set_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "s"], "arg_types": ["matplotlib.text.Text", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_text of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_transform_rotates_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_transform_rotates_text", "name": "set_transform_rotates_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "t"], "arg_types": ["matplotlib.text.Text", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_transform_rotates_text of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_usetex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "usetex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_usetex", "name": "set_usetex", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "usetex"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_usetex of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_verticalalignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "align"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_verticalalignment", "name": "set_verticalalignment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "align"], "arg_types": ["matplotlib.text.Text", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "bottom"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "center_baseline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "top"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_verticalalignment of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "wrap"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_wrap", "name": "set_wrap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "wrap"], "arg_types": ["matplotlib.text.Text", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_wrap of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_x": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_x", "name": "set_x", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["matplotlib.text.Text", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_x of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_y": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.set_y", "name": "set_y", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "y"], "arg_types": ["matplotlib.text.Text", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_y of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["matplotlib.text.Text", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of Text", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_bbox_position_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text.Text.update_bbox_position_size", "name": "update_bbox_position_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "renderer"], "arg_types": ["matplotlib.text.Text", "matplotlib.backend_bases.RendererBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_bbox_position_size of Text", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zorder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.text.Text.zorder", "name": "zorder", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.text.Text.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.text.Text", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextPath": {".class": "SymbolTableNode", "cross_ref": "matplotlib.textpath.TextPath", "kind": "Gdef"}, "TextToPath": {".class": "SymbolTableNode", "cross_ref": "matplotlib.textpath.TextToPath", "kind": "Gdef"}, "Transform": {".class": "SymbolTableNode", "cross_ref": "matplotlib.transforms.Transform", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_AnnotationBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.text._AnnotationBase", "name": "_AnnotationBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.text._AnnotationBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.text", "mro": ["matplotlib.text._AnnotationBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "xy", "xycoords", "annotation_clip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text._AnnotationBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "xy", "xycoords", "annotation_clip"], "arg_types": ["matplotlib.text._AnnotationBase", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _AnnotationBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "draggable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "state", "use_blit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text._AnnotationBase.draggable", "name": "draggable", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "state", "use_blit"], "arg_types": ["matplotlib.text._AnnotationBase", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "draggable of _AnnotationBase", "ret_type": {".class": "UnionType", "items": ["matplotlib.offsetbox.DraggableAnnotation", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_annotation_clip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text._AnnotationBase.get_annotation_clip", "name": "get_annotation_clip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["matplotlib.text._AnnotationBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_annotation_clip of _AnnotationBase", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_annotation_clip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.text._AnnotationBase.set_annotation_clip", "name": "set_annotation_clip", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["matplotlib.text._AnnotationBase", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_annotation_clip of _AnnotationBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.text._AnnotationBase.xy", "name": "xy", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "xycoords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "matplotlib.text._AnnotationBase.xycoords", "name": "xycoords", "type": {".class": "TypeAliasType", "args": [], "type_ref": "matplotlib.typing.CoordsType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.text._AnnotationBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.text._AnnotationBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.text.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.text.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.text.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.text.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.text.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.text.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\text.pyi"}