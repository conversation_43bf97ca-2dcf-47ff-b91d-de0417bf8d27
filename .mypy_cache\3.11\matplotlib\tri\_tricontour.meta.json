{"data_mtime": 1753781397, "dep_lines": [3, 1, 2, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["matplotlib.tri._triangulation", "matplotlib.axes", "matplotlib.contour", "numpy.typing", "typing", "builtins", "warnings", "pprint", "operator", "logging", "collections", "string", "json", "contextlib", "copy", "itertools", "os", "inspect", "html", "sys", "traitlets.utils.warnings", "functools", "re", "types", "traceback", "_frozen_importlib", "abc", "matplotlib.artist", "matplotlib.axes._axes", "matplotlib.axes._base", "matplotlib.collections", "matplotlib.colorizer", "numpy", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nested_sequence"], "hash": "d29379d855444abed6ecc1da4083d5878654ecae", "id": "matplotlib.tri._tricontour", "ignore_all": true, "interface_hash": "2c928cbbbc7933a0abe9eefb9616244b343ac226", "mtime": 1746804123, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.pyi", "plugin_data": null, "size": 1155, "suppressed": [], "version_id": "1.15.0"}