{".class": "MypyFile", "_fullname": "matplotlib.tri._trirefine", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ArrayLike": {".class": "SymbolTableNode", "cross_ref": "numpy._typing._array_like.ArrayLike", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TriInterpolator": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._triinterpolate.TriInterpolator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TriRefiner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.tri._trirefine.TriRefiner", "name": "TriRefiner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.tri._trirefine.TriRefiner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.tri._trirefine", "mro": ["matplotlib.tri._trirefine.TriRefiner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "triangulation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.tri._trirefine.TriRefiner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "triangulation"], "arg_types": ["matplotlib.tri._trirefine.TriRefiner", "matplotlib.tri._triangulation.Triangulation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TriRefiner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.tri._trirefine.TriRefiner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.tri._trirefine.TriRefiner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Triangulation": {".class": "SymbolTableNode", "cross_ref": "matplotlib.tri._triangulation.Triangulation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "UniformTriRefiner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.tri._trirefine.TriRefiner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.tri._trirefine.UniformTriRefiner", "name": "UniformTriRefiner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.tri._trirefine", "mro": ["matplotlib.tri._trirefine.UniformTriRefiner", "matplotlib.tri._trirefine.TriRefiner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "triangulation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "triangulation"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", "matplotlib.tri._triangulation.Triangulation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UniformTriRefiner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refine_field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "z", "triinterpolator", "subdiv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_field", "name": "refine_field", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "z", "triinterpolator", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", {".class": "TypeAliasType", "args": [], "type_ref": "numpy._typing._array_like.ArrayLike"}, {".class": "UnionType", "items": ["matplotlib.tri._triinterpolate.TriInterpolator", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_field of UniformTriRefiner", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.tri._triangulation.Triangulation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "refine_triangulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_triangulation", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["self", "return_tri_index", "subdiv"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_triangulation", "name": "refine_triangulation", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.tri._triangulation.Triangulation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_triangulation", "name": "refine_triangulation", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.tri._triangulation.Triangulation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_triangulation", "name": "refine_triangulation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": "matplotlib.tri._triangulation.Triangulation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_triangulation", "name": "refine_triangulation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": "matplotlib.tri._triangulation.Triangulation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_triangulation", "name": "refine_triangulation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.tri._triangulation.Triangulation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "matplotlib.tri._triangulation.Triangulation"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.refine_triangulation", "name": "refine_triangulation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.tri._triangulation.Triangulation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "matplotlib.tri._triangulation.Triangulation"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": {".class": "TupleType", "implicit": false, "items": ["matplotlib.tri._triangulation.Triangulation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": "matplotlib.tri._triangulation.Triangulation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "return_tri_index", "subdiv"], "arg_types": ["matplotlib.tri._trirefine.UniformTriRefiner", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refine_triangulation of UniformTriRefiner", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["matplotlib.tri._triangulation.Triangulation", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "matplotlib.tri._triangulation.Triangulation"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.tri._trirefine.UniformTriRefiner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.tri._trirefine.UniformTriRefiner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri._trirefine.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri._trirefine.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri._trirefine.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri._trirefine.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri._trirefine.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.tri._trirefine.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.pyi"}