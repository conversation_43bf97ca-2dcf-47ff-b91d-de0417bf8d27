{".class": "MypyFile", "_fullname": "matplotlib.units", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AxisInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.units.AxisInfo", "name": "AxisInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.units.AxisInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.units", "mro": ["matplotlib.units.AxisInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "maj<PERSON>", "minloc", "majfmt", "minfmt", "label", "default_limits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.units.AxisInfo.__init__", "name": "__init__", "type": null}}, "default_limits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.units.AxisInfo.default_limits", "name": "default_limits", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.units.AxisInfo.label", "name": "label", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "majfmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.units.AxisInfo.majfmt", "name": "majfmt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "majloc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.units.AxisInfo.majloc", "name": "maj<PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "minfmt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.units.AxisInfo.minfmt", "name": "minfmt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "minloc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "matplotlib.units.AxisInfo.minloc", "name": "minloc", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.units.AxisInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.units.AxisInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConversionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.units.ConversionError", "name": "ConversionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.units.ConversionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.units", "mro": ["matplotlib.units.ConversionError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.units.ConversionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.units.ConversionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConversionInterface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.units.ConversionInterface", "name": "ConversionInterface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.units.ConversionInterface", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.units", "mro": ["matplotlib.units.ConversionInterface", "builtins.object"], "names": {".class": "SymbolTable", "axisinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["unit", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.units.ConversionInterface.axisinfo", "name": "axisinfo", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.units.ConversionInterface.axisinfo", "name": "axisinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["unit", "axis"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "axisinfo of ConversionInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["obj", "unit", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.units.ConversionInterface.convert", "name": "convert", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.units.ConversionInterface.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["obj", "unit", "axis"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of ConversionInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_units": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["x", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.units.ConversionInterface.default_units", "name": "default_units", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.units.ConversionInterface.default_units", "name": "default_units", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["x", "axis"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_units of ConversionInterface", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.units.ConversionInterface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.units.ConversionInterface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "DecimalConverter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["matplotlib.units.ConversionInterface"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.units.DecimalConverter", "name": "DecimalConverter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.units.DecimalConverter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "matplotlib.units", "mro": ["matplotlib.units.DecimalConverter", "matplotlib.units.ConversionInterface", "builtins.object"], "names": {".class": "SymbolTable", "convert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["value", "unit", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "matplotlib.units.DecimalConverter.convert", "name": "convert", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "matplotlib.units.DecimalConverter.convert", "name": "convert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["value", "unit", "axis"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert of DecimalConverter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.units.DecimalConverter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.units.DecimalConverter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Number": {".class": "SymbolTableNode", "cross_ref": "numbers.Number", "kind": "Gdef"}, "Registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "matplotlib.units.Registry", "name": "Registry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "matplotlib.units.Registry", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "matplotlib.units", "mro": ["matplotlib.units.Registry", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "get_converter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.units.Registry.get_converter", "name": "get_converter", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "matplotlib.units.Registry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "matplotlib.units.Registry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.units.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.units.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.units.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.units.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.units.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "matplotlib.units.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_is_natively_supported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "matplotlib.units._is_natively_supported", "name": "_is_natively_supported", "type": null}}, "cbook": {".class": "SymbolTableNode", "cross_ref": "matplotlib.cbook", "kind": "Gdef"}, "ma": {".class": "SymbolTableNode", "cross_ref": "numpy.ma", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "matplotlib.units.registry", "name": "registry", "type": "matplotlib.units.Registry"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\units.py"}