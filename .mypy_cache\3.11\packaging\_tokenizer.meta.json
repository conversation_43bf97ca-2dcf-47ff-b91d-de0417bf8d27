{"data_mtime": 1753783516, "dep_lines": [8, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["packaging.specifiers", "__future__", "contextlib", "re", "dataclasses", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum"], "hash": "575e0d0d9f6bae73faccb96833494626bd81fbef", "id": "packaging._tokenizer", "ignore_all": true, "interface_hash": "f754ac9929e6f1342bfc9bd4159a47e5c2597f2b", "mtime": 1741286791, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\packaging\\_tokenizer.py", "plugin_data": null, "size": 5273, "suppressed": [], "version_id": "1.15.0"}