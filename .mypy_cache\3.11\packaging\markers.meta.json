{"data_mtime": 1753783521, "dep_lines": [13, 15, 16, 17, 5, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["packaging._parser", "packaging._tokenizer", "packaging.specifiers", "packaging.utils", "__future__", "operator", "os", "platform", "sys", "typing", "builtins", "dataclasses", "_frozen_importlib", "_operator", "_typeshed", "abc", "typing_extensions"], "hash": "a33df2510a351b7d15c64b751007a742068602b2", "id": "packaging.markers", "ignore_all": true, "interface_hash": "c7b15596f359d588154b218625e6f14c42eaa16d", "mtime": 1741286791, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\packaging\\markers.py", "plugin_data": null, "size": 10561, "suppressed": [], "version_id": "1.15.0"}