{"data_mtime": 1753783515, "dep_lines": [18, 19, 11, 13, 14, 15, 16, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 20, 30, 30, 30, 30], "dependencies": ["packaging.utils", "packaging.version", "__future__", "abc", "itertools", "re", "typing", "builtins", "dataclasses", "_frozen_importlib", "enum", "types", "typing_extensions"], "hash": "58e79d3fe1e6a5d58a6a6d0bda2b47472685b244", "id": "packaging.specifiers", "ignore_all": true, "interface_hash": "e7d941b95e06f41d3d6760b0410e2d3de8881f9c", "mtime": 1741286791, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\packaging\\specifiers.py", "plugin_data": null, "size": 40074, "suppressed": [], "version_id": "1.15.0"}