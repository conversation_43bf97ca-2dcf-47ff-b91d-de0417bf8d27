{".class": "MypyFile", "_fullname": "paddle.base.trainer_desc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DistMultiTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["paddle.base.trainer_desc.TrainerDesc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "paddle.base.trainer_desc.DistMultiTrainer", "name": "DistMultiTrainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.DistMultiTrainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "paddle.base.trainer_desc", "mro": ["paddle.base.trainer_desc.DistMultiTrainer", "paddle.base.trainer_desc.TrainerDesc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.DistMultiTrainer.__init__", "name": "__init__", "type": null}}, "_gen_trainer_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.DistMultiTrainer._gen_trainer_desc", "name": "_gen_trainer_desc", "type": null}}, "_set_program": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.DistMultiTrainer._set_program", "name": "_set_program", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "paddle.base.trainer_desc.DistMultiTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "paddle.base.trainer_desc.DistMultiTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeterPipelineTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["paddle.base.trainer_desc.TrainerDesc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "paddle.base.trainer_desc.<PERSON><PERSON><PERSON><PERSON><PERSON>eTrainer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.<PERSON><PERSON><PERSON><PERSON><PERSON>eTrainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "paddle.base.trainer_desc", "mro": ["paddle.base.trainer_desc.<PERSON><PERSON><PERSON><PERSON><PERSON>eTrainer", "paddle.base.trainer_desc.TrainerDesc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.He<PERSON><PERSON>ipelineTrainer.__init__", "name": "__init__", "type": null}}, "_gen_trainer_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.<PERSON><PERSON><PERSON><PERSON>elineTrainer._gen_trainer_desc", "name": "_gen_trainer_desc", "type": null}}, "_set_program": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.Heter<PERSON>ipelineTrainer._set_program", "name": "_set_program", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "paddle.base.trainer_desc.HeterPipelineTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "paddle.base.trainer_desc.<PERSON><PERSON><PERSON><PERSON><PERSON>eTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeterXpuTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["paddle.base.trainer_desc.TrainerDesc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "paddle.base.trainer_desc.HeterXpuTrainer", "name": "HeterXpuTrain<PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.HeterXpuTrainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "paddle.base.trainer_desc", "mro": ["paddle.base.trainer_desc.HeterXpuTrainer", "paddle.base.trainer_desc.TrainerDesc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.HeterXpuTrainer.__init__", "name": "__init__", "type": null}}, "_gen_trainer_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.HeterXpuTrainer._gen_trainer_desc", "name": "_gen_trainer_desc", "type": null}}, "_set_program": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.HeterXpuTrainer._set_program", "name": "_set_program", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "paddle.base.trainer_desc.HeterXpuTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "paddle.base.trainer_desc.HeterXpuTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultiTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["paddle.base.trainer_desc.TrainerDesc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "paddle.base.trainer_desc.MultiTrainer", "name": "MultiTrainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.MultiTrainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "paddle.base.trainer_desc", "mro": ["paddle.base.trainer_desc.MultiTrainer", "paddle.base.trainer_desc.TrainerDesc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.MultiTrainer.__init__", "name": "__init__", "type": null}}, "_gen_trainer_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.MultiTrainer._gen_trainer_desc", "name": "_gen_trainer_desc", "type": null}}, "_set_program": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.MultiTrainer._set_program", "name": "_set_program", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "paddle.base.trainer_desc.MultiTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "paddle.base.trainer_desc.MultiTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PSGPUTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["paddle.base.trainer_desc.TrainerDesc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "paddle.base.trainer_desc.PSGPUTrainer", "name": "PSGPUTrainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PSGPUTrainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "paddle.base.trainer_desc", "mro": ["paddle.base.trainer_desc.PSGPUTrainer", "paddle.base.trainer_desc.TrainerDesc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PSGPUTrainer.__init__", "name": "__init__", "type": null}}, "_gen_trainer_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PSGPUTrainer._gen_trainer_desc", "name": "_gen_trainer_desc", "type": null}}, "_set_program": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PSGPUTrainer._set_program", "name": "_set_program", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "paddle.base.trainer_desc.PSGPUTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "paddle.base.trainer_desc.PSGPUTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PipelineTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["paddle.base.trainer_desc.TrainerDesc"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "paddle.base.trainer_desc.PipelineTrainer", "name": "PipelineTrainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PipelineTrainer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "paddle.base.trainer_desc", "mro": ["paddle.base.trainer_desc.PipelineTrainer", "paddle.base.trainer_desc.TrainerDesc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PipelineTrainer.__init__", "name": "__init__", "type": null}}, "_gen_trainer_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PipelineTrainer._gen_trainer_desc", "name": "_gen_trainer_desc", "type": null}}, "_set_program": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.PipelineTrainer._set_program", "name": "_set_program", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "paddle.base.trainer_desc.PipelineTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "paddle.base.trainer_desc.PipelineTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrainerDesc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "paddle.base.trainer_desc.TrainerDesc", "name": "TrainerDesc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "paddle.base.trainer_desc", "mro": ["paddle.base.trainer_desc.TrainerDesc", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc.__str__", "name": "__str__", "type": null}}, "_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._desc", "name": "_desc", "type": null}}, "_device_worker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "paddle.base.trainer_desc.TrainerDesc._device_worker", "name": "_device_worker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_fleet_desc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "paddle.base.trainer_desc.TrainerDesc._fleet_desc", "name": "_fleet_desc", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_gen_trainer_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._gen_trainer_desc", "name": "_gen_trainer_desc", "type": null}}, "_infer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "paddle.base.trainer_desc.TrainerDesc._infer", "name": "_infer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_program": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "paddle.base.trainer_desc.TrainerDesc._program", "name": "_program", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_set_adjust_ins_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_adjust_ins_weight", "name": "_set_adjust_ins_weight", "type": null}}, "_set_check_nan_var_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "check_nan_var_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_check_nan_var_names", "name": "_set_check_nan_var_names", "type": null}}, "_set_copy_table_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_copy_table_config", "name": "_set_copy_table_config", "type": null}}, "_set_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "debug"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_debug", "name": "_set_debug", "type": null}}, "_set_device_worker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_worker"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_device_worker", "name": "_set_device_worker", "type": null}}, "_set_dump_converter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "converter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_converter", "name": "_set_dump_converter", "type": null}}, "_set_dump_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dump_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_fields", "name": "_set_dump_fields", "type": null}}, "_set_dump_fields_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_fields_mode", "name": "_set_dump_fields_mode", "type": null}}, "_set_dump_fields_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_fields_path", "name": "_set_dump_fields_path", "type": null}}, "_set_dump_file_num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dump_file_num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_file_num", "name": "_set_dump_file_num", "type": null}}, "_set_dump_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dump_interval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_interval", "name": "_set_dump_interval", "type": null}}, "_set_dump_num_decimals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dump_num_decimals"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_num_decimals", "name": "_set_dump_num_decimals", "type": null}}, "_set_dump_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dump_param"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_param", "name": "_set_dump_param", "type": null}}, "_set_dump_slot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dump_slot"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_dump_slot", "name": "_set_dump_slot", "type": null}}, "_set_enable_random_dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "enable_random_dump"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_enable_random_dump", "name": "_set_enable_random_dump", "type": null}}, "_set_fetch_var_and_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "fetch_vars", "fetch_info", "print_period"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_fetch_var_and_info", "name": "_set_fetch_var_and_info", "type": null}}, "_set_fleet_desc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fleet_desc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_fleet_desc", "name": "_set_fleet_desc", "type": null}}, "_set_heter_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ret"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_heter_info", "name": "_set_heter_info", "type": null}}, "_set_infer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "infer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_infer", "name": "_set_infer", "type": null}}, "_set_is_dump_in_simple_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "is_dump_in_simple_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_is_dump_in_simple_mode", "name": "_set_is_dump_in_simple_mode", "type": null}}, "_set_loss_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loss_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_loss_names", "name": "_set_loss_names", "type": null}}, "_set_mpi_rank": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mpi_rank"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_mpi_rank", "name": "_set_mpi_rank", "type": null}}, "_set_mpi_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mpi_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_mpi_size", "name": "_set_mpi_size", "type": null}}, "_set_no_cvm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "no_cvm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_no_cvm", "name": "_set_no_cvm", "type": null}}, "_set_program": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "program"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_program", "name": "_set_program", "type": null}}, "_set_random_with_lineid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "random_with_lineid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_random_with_lineid", "name": "_set_random_with_lineid", "type": null}}, "_set_scale_datanorm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "scale_datanorm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_scale_datanorm", "name": "_set_scale_datanorm", "type": null}}, "_set_scale_sparse_grad_with_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "scale_sparse_gradient_with_batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_scale_sparse_grad_with_batch_size", "name": "_set_scale_sparse_grad_with_batch_size", "type": null}}, "_set_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thread_num"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_thread", "name": "_set_thread", "type": null}}, "_set_thread_barrier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thread_barrier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_thread_barrier", "name": "_set_thread_barrier", "type": null}}, "_set_trainer_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "trainer_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_trainer_id", "name": "_set_trainer_id", "type": null}}, "_set_trainers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "trainers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_trainers", "name": "_set_trainers", "type": null}}, "_set_use_cvm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "use_cvm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_use_cvm", "name": "_set_use_cvm", "type": null}}, "_set_use_gpu_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "use_gpu_graph"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_use_gpu_graph", "name": "_set_use_gpu_graph", "type": null}}, "_set_use_ps_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "use_ps_gpu"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_use_ps_gpu", "name": "_set_use_ps_gpu", "type": null}}, "_set_user_define_dump_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_define_dump_filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_user_define_dump_filename", "name": "_set_user_define_dump_filename", "type": null}}, "_set_worker_places": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "worker_places"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "paddle.base.trainer_desc.TrainerDesc._set_worker_places", "name": "_set_worker_places", "type": null}}, "proto_desc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "paddle.base.trainer_desc.TrainerDesc.proto_desc", "name": "proto_desc", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "paddle.base.trainer_desc.TrainerDesc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "paddle.base.trainer_desc.TrainerDesc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "paddle.base.trainer_desc.__all__", "name": "__all__", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "paddle.base.trainer_desc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "paddle.base.trainer_desc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "paddle.base.trainer_desc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "paddle.base.trainer_desc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "paddle.base.trainer_desc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "paddle.base.trainer_desc.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\paddle\\base\\trainer_desc.py"}