{"data_mtime": 1753783515, "dep_lines": [16, 17, 45, 1, 1, 1, 1, 1, 1, 116, 42], "dep_prios": [10, 10, 20, 5, 20, 30, 30, 30, 30, 20, 20], "dependencies": ["os", "sys", "multiprocessing", "builtins", "dataclasses", "_frozen_importlib", "abc", "types", "typing"], "hash": "5b6320a5620fadc8a5f333c19cc891840bfc513e", "id": "paddle.base.trainer_desc", "ignore_all": true, "interface_hash": "bec142a3d82f05b669d39abdf348a347b74c71da", "mtime": 1749228269, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\paddle\\base\\trainer_desc.py", "plugin_data": null, "size": 16128, "suppressed": ["google.protobuf", "proto"], "version_id": "1.15.0"}