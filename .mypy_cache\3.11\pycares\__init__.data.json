{".class": "MypyFile", "_fullname": "pycares", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARES_FLAG_EDNS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_EDNS", "name": "ARES_FLAG_EDNS", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_IGNTC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_IGNTC", "name": "ARES_FLAG_IGNTC", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_NOALIASES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_NOALIASES", "name": "ARES_FLAG_NOALIASES", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_NOCHECKRESP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_NOCHECKRESP", "name": "ARES_FLAG_NOCHECKRESP", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_NORECURSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_NORECURSE", "name": "ARES_FLAG_NORECURSE", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_NOSEARCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_NOSEARCH", "name": "ARES_FLAG_NOSEARCH", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_NO_DFLT_SVR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_NO_DFLT_SVR", "name": "ARES_FLAG_NO_DFLT_SVR", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_PRIMARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_PRIMARY", "name": "ARES_FLAG_PRIMARY", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_STAYOPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_STAYOPEN", "name": "ARES_FLAG_STAYOPEN", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_FLAG_USEVC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_FLAG_USEVC", "name": "ARES_FLAG_USEVC", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_DCCP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_DCCP", "name": "ARES_NI_DCCP", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_DGRAM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_DGRAM", "name": "ARES_NI_DGRAM", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_IDN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_IDN", "name": "ARES_NI_IDN", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_IDN_ALLOW_UNASSIGNED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_IDN_ALLOW_UNASSIGNED", "name": "ARES_NI_IDN_ALLOW_UNASSIGNED", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_IDN_USE_STD3_ASCII_RULES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_IDN_USE_STD3_ASCII_RULES", "name": "ARES_NI_IDN_USE_STD3_ASCII_RULES", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_LOOKUPHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_LOOKUPHOST", "name": "ARES_NI_LOOKUPHOST", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_LOOKUPSERVICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_LOOKUPSERVICE", "name": "ARES_NI_LOOKUPSERVICE", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_NAMEREQD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_NAMEREQD", "name": "ARES_NI_NAMEREQD", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_NOFQDN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_NOFQDN", "name": "ARES_NI_NOFQDN", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_NUMERICHOST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_NUMERICHOST", "name": "ARES_NI_NUMERICHOST", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_NUMERICSCOPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_NUMERICSCOPE", "name": "ARES_NI_NUMERICSCOPE", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_NUMERICSERV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_NUMERICSERV", "name": "ARES_NI_NUMERICSERV", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_SCTP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_SCTP", "name": "ARES_NI_SCTP", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_TCP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_TCP", "name": "ARES_NI_TCP", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_NI_UDP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_NI_UDP", "name": "ARES_NI_UDP", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_SOCKET_BAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_SOCKET_BAD", "name": "ARES_SOCKET_BAD", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ARES_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.ARES_VERSION", "name": "ARES_VERSION", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AresError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.AresError", "name": "AresError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.AresError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.AresError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.AresError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.AresError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AresResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.AresResult", "name": "AresResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.AresResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.AresResult.__repr__", "name": "__repr__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.AresResult.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.AresResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.AresResult", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Channel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.Channel", "name": "Channel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.Channel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.Channel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "flags", "timeout", "tries", "ndots", "tcp_port", "udp_port", "servers", "domains", "lookups", "sock_state_cb", "socket_send_buffer_size", "socket_receive_buffer_size", "rotate", "local_ip", "local_dev", "resolvconf_path", "event_thread"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "flags", "timeout", "tries", "ndots", "tcp_port", "udp_port", "servers", "domains", "lookups", "sock_state_cb", "socket_send_buffer_size", "socket_receive_buffer_size", "rotate", "local_ip", "local_dev", "resolvconf_path", "event_thread"], "arg_types": ["pycares.Channel", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__qclasses__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pycares.Channel.__qclasses__", "name": "__qclasses__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__qtypes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pycares.Channel.__qtypes__", "name": "__qtypes__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_channel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.Channel._channel", "name": "_channel", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "_do_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "func", "name", "query_type", "callback", "query_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel._do_query", "name": "_do_query", "type": null}}, "_sock_state_cb_handle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.Channel._sock_state_cb_handle", "name": "_sock_state_cb_handle", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.cancel", "name": "cancel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pycares.Channel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getaddrinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "host", "port", "callback", "family", "type", "proto", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.getaddrinfo", "name": "getaddrinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "host", "port", "callback", "family", "type", "proto", "flags"], "arg_types": ["pycares.Channel", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "socket.AddressFamily", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get<PERSON><PERSON>in<PERSON> of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyaddr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "addr", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.gethostbyaddr", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "addr", "callback"], "arg_types": ["pycares.Channel", "builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gethostby<PERSON>dr of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gethostbyname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "family", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.gethostbyname", "name": "gethostbyname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "family", "callback"], "arg_types": ["pycares.Channel", "builtins.str", "socket.AddressFamily", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gethostbyname of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getnameinfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "address", "flags", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.getnameinfo", "name": "getnameinfo", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "address", "flags", "callback"], "arg_types": ["pycares.Channel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pycares.IP4"}, {".class": "TypeAliasType", "args": [], "type_ref": "pycares.IP6"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getnameinfo of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getsock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.getsock", "name": "getsock", "type": null}}, "process_fd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_fd", "write_fd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.process_fd", "name": "process_fd", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_fd", "write_fd"], "arg_types": ["pycares.Channel", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_fd of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "query_type", "callback", "query_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.query", "name": "query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "query_type", "callback", "query_class"], "arg_types": ["pycares.Channel", "builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "query of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reinit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.reinit", "name": "reinit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pycares.Channel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reinit of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "name", "query_type", "callback", "query_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.search", "name": "search", "type": null}}, "servers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "pycares.Channel.servers", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "pycares.Channel.servers", "name": "servers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pycares.Channel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "servers of Channel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "pycares.Channel.servers", "name": "servers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pycares.Channel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "servers of Channel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "servers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pycares.Channel.servers", "name": "servers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "servers"], "arg_types": ["pycares.Channel", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "servers of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "servers", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pycares.Channel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "servers of Channel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "set_local_dev": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dev"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.set_local_dev", "name": "set_local_dev", "type": null}}, "set_local_ip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.set_local_ip", "name": "set_local_ip", "type": null}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.Channel.timeout", "name": "timeout", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.Channel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.Channel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IP4": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pycares.IP4", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "IP6": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pycares.IP6", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PYCARES_ADDRTTL_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.PYCARES_ADDRTTL_SIZE", "name": "PYCARES_ADDRTTL_SIZE", "type": "builtins.int"}}, "QUERY_CLASS_ANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_CLASS_ANY", "name": "QUERY_CLASS_ANY", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_CLASS_CHAOS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_CLASS_CHAOS", "name": "QUERY_CLASS_CHAOS", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_CLASS_HS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_CLASS_HS", "name": "QUERY_CLASS_HS", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_CLASS_IN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_CLASS_IN", "name": "QUERY_CLASS_IN", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_CLASS_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_CLASS_NONE", "name": "QUERY_CLASS_NONE", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_A": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_A", "name": "QUERY_TYPE_A", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_AAAA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_AAAA", "name": "QUERY_TYPE_AAAA", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_ANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_ANY", "name": "QUERY_TYPE_ANY", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_CAA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_CAA", "name": "QUERY_TYPE_CAA", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_CNAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_CNAME", "name": "QUERY_TYPE_CNAME", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_MX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_MX", "name": "QUERY_TYPE_MX", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_NAPTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_NAPTR", "name": "QUERY_TYPE_NAPTR", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_NS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_NS", "name": "QUERY_TYPE_NS", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_PTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_PTR", "name": "QUERY_TYPE_PTR", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_SOA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_SOA", "name": "QUERY_TYPE_SOA", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_SRV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_SRV", "name": "QUERY_TYPE_SRV", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "QUERY_TYPE_TXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.QUERY_TYPE_TXT", "name": "QUERY_TYPE_TXT", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pycares.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pycares.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pycares.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pycares.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pycares.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pycares.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pycares.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "pycares._version.__version__", "kind": "Gdef"}, "_addrinfo_cb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["arg", "status", "timeouts", "res"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pycares._addrinfo_cb", "name": "_addrinfo_cb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pycares._addrinfo_cb", "name": "_addrinfo_cb", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "_cffi_backend": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pycares._cffi_backend", "name": "_cffi_backend", "type": {".class": "AnyType", "missing_import_name": "pycares._cffi_backend", "source_any": null, "type_of_any": 3}}}, "_ffi": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pycares._ffi", "name": "_ffi", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}}}, "_global_set": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pycares._global_set", "name": "_global_set", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_host_cb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["arg", "status", "timeouts", "hostent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pycares._host_cb", "name": "_host_cb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pycares._host_cb", "name": "_host_cb", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "_lib": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "pycares._lib", "name": "_lib", "type": {".class": "AnyType", "missing_import_name": "pycares._lib", "source_any": null, "type_of_any": 3}}}, "_nameinfo_cb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["arg", "status", "timeouts", "node", "service"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pycares._nameinfo_cb", "name": "_nameinfo_cb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pycares._nameinfo_cb", "name": "_nameinfo_cb", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "_query_cb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["arg", "status", "timeouts", "abuf", "alen"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pycares._query_cb", "name": "_query_cb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pycares._query_cb", "name": "_query_cb", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "_sock_state_cb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["data", "socket_fd", "readable", "writable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pycares._sock_state_cb", "name": "_sock_state_cb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pycares._sock_state_cb", "name": "_sock_state_cb", "type": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": {".class": "AnyType", "missing_import_name": "pycares._ffi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "ares_addrinfo_cname_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_addrinfo_cname_result", "name": "ares_addrinfo_cname_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_addrinfo_cname_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_addrinfo_cname_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ares_cname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_addrinfo_cname_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_addrinfo_cname_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_cname_result.alias", "name": "alias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_cname_result.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_cname_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_addrinfo_cname_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_addrinfo_cname_result", "values": [], "variance": 0}, "slots": ["alias", "name", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_addrinfo_node_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_addrinfo_node_result", "name": "ares_addrinfo_node_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_addrinfo_node_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_addrinfo_node_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ares_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_addrinfo_node_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_addrinfo_node_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "addr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_node_result.addr", "name": "addr", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "family": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_node_result.family", "name": "family", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_node_result.flags", "name": "flags", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "protocol": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_node_result.protocol", "name": "protocol", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "socktype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_node_result.socktype", "name": "socktype", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_node_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_addrinfo_node_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_addrinfo_node_result", "values": [], "variance": 0}, "slots": ["addr", "family", "flags", "protocol", "socktype", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_addrinfo_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_addrinfo_result", "name": "ares_addrinfo_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_addrinfo_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_addrinfo_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ares_addrinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_addrinfo_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_addrinfo_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "cnames": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_result.cnames", "name": "cnames", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_addrinfo_result.nodes", "name": "nodes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_addrinfo_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_addrinfo_result", "values": [], "variance": 0}, "slots": ["cnames", "nodes"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_host_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_host_result", "name": "ares_host_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_host_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_host_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hostent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_host_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_host_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "addresses": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_host_result.addresses", "name": "addresses", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "aliases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_host_result.aliases", "name": "aliases", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_host_result.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_host_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_host_result", "values": [], "variance": 0}, "slots": ["addresses", "aliases", "name"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_nameinfo_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_nameinfo_result", "name": "ares_nameinfo_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_nameinfo_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_nameinfo_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "service"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_nameinfo_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_nameinfo_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_nameinfo_result.node", "name": "node", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_nameinfo_result.service", "name": "service", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_nameinfo_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_nameinfo_result", "values": [], "variance": 0}, "slots": ["node", "service"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_a_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_a_result", "name": "ares_query_a_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_a_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_a_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ares_addrttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_a_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_a_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_a_result.host", "name": "host", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_a_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_a_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_a_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_a_result", "values": [], "variance": 0}, "slots": ["host", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_aaaa_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_aaaa_result", "name": "ares_query_aaaa_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_aaaa_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_aaaa_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ares_addrttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_aaaa_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_aaaa_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_aaaa_result.host", "name": "host", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_aaaa_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_aaaa_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_aaaa_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_aaaa_result", "values": [], "variance": 0}, "slots": ["host", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_caa_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_caa_result", "name": "ares_query_caa_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_caa_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_caa_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "caa"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_caa_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_caa_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "critical": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_caa_result.critical", "name": "critical", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "property": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_caa_result.property", "name": "property", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_caa_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_caa_result.type", "name": "type", "type": "builtins.str"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_caa_result.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_caa_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_caa_result", "values": [], "variance": 0}, "slots": ["critical", "property", "ttl", "value"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_cname_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_cname_result", "name": "ares_query_cname_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_cname_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_cname_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_cname_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_cname_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "cname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_cname_result.cname", "name": "cname", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_cname_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_cname_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_cname_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_cname_result", "values": [], "variance": 0}, "slots": ["cname", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_mx_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_mx_result", "name": "ares_query_mx_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_mx_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_mx_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_mx_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_mx_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_mx_result.host", "name": "host", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "priority": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_mx_result.priority", "name": "priority", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_mx_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_mx_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_mx_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_mx_result", "values": [], "variance": 0}, "slots": ["host", "priority", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_naptr_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_naptr_result", "name": "ares_query_naptr_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_naptr_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_naptr_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "naptr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_naptr_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_naptr_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_naptr_result.flags", "name": "flags", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "order": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_naptr_result.order", "name": "order", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "preference": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_naptr_result.preference", "name": "preference", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "regex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_naptr_result.regex", "name": "regex", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "replacement": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_naptr_result.replacement", "name": "replacement", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_naptr_result.service", "name": "service", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_naptr_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_naptr_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_naptr_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_naptr_result", "values": [], "variance": 0}, "slots": ["flags", "order", "preference", "regex", "replacement", "service", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_ns_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_ns_result", "name": "ares_query_ns_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_ns_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_ns_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_ns_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_ns_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_ns_result.host", "name": "host", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_ns_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_ns_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_ns_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_ns_result", "values": [], "variance": 0}, "slots": ["host", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_ptr_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_ptr_result", "name": "ares_query_ptr_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_ptr_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_ptr_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hostent", "aliases"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_ptr_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_ptr_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "aliases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_ptr_result.aliases", "name": "aliases", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_ptr_result.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_ptr_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_ptr_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_ptr_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_ptr_result", "values": [], "variance": 0}, "slots": ["aliases", "name", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_soa_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_soa_result", "name": "ares_query_soa_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_soa_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_soa_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "soa"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_soa_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_soa_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "expires": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.expires", "name": "expires", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hostmaster": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.hostmaster", "name": "hostmaster", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "minttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.minttl", "name": "minttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "nsname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.nsname", "name": "nsname", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "refresh": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.refresh", "name": "refresh", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.retry", "name": "retry", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "serial": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.serial", "name": "serial", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_soa_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_soa_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_soa_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_soa_result", "values": [], "variance": 0}, "slots": ["expires", "hostmaster", "minttl", "nsname", "refresh", "retry", "serial", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_srv_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_srv_result", "name": "ares_query_srv_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_srv_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_srv_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "srv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_srv_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_srv_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_srv_result.host", "name": "host", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_srv_result.port", "name": "port", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "priority": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_srv_result.priority", "name": "priority", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_srv_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_srv_result.type", "name": "type", "type": "builtins.str"}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_srv_result.weight", "name": "weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_srv_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_srv_result", "values": [], "variance": 0}, "slots": ["host", "port", "priority", "ttl", "weight"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_txt_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_txt_result", "name": "ares_query_txt_result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_txt_result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_txt_result", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "txt_chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_txt_result.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_txt_result.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_txt_result.text", "name": "text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_txt_result.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_txt_result.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_txt_result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_txt_result", "values": [], "variance": 0}, "slots": ["text", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_query_txt_result_chunk": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pycares.AresResult"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pycares.ares_query_txt_result_chunk", "name": "ares_query_txt_result_chunk", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pycares.ares_query_txt_result_chunk", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pycares", "mro": ["pycares.ares_query_txt_result_chunk", "pycares.AresResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "txt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_query_txt_result_chunk.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pycares.ares_query_txt_result_chunk.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_txt_result_chunk.text", "name": "text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pycares.ares_query_txt_result_chunk.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "pycares.ares_query_txt_result_chunk.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pycares.ares_query_txt_result_chunk.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pycares.ares_query_txt_result_chunk", "values": [], "variance": 0}, "slots": ["text", "ttl"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ares_threadsafety": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.ares_threadsafety", "name": "ares_threadsafety", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ares_threadsafety", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ascii_bytes": {".class": "SymbolTableNode", "cross_ref": "pycares.utils.ascii_bytes", "kind": "Gdef", "module_public": false}, "errno": {".class": "SymbolTableNode", "cross_ref": "pycares.errno", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "maybe_str": {".class": "SymbolTableNode", "cross_ref": "pycares.utils.maybe_str", "kind": "Gdef", "module_public": false}, "parse_name": {".class": "SymbolTableNode", "cross_ref": "pycares.utils.parse_name", "kind": "Gdef", "module_public": false}, "parse_result": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["query_type", "abuf", "alen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pycares.parse_result", "name": "parse_result", "type": null}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycares\\__init__.py"}