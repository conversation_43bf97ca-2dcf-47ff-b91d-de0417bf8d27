{"data_mtime": 1753783515, "dep_lines": [8, 9, 10, 16, 12, 13, 14, 15, 17, 1, 1, 1, 1, 1, 1, 1, 1, 2, 3], "dep_prios": [10, 5, 5, 5, 10, 10, 10, 10, 5, 5, 20, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["pycares.errno", "pycares.utils", "pycares._version", "collections.abc", "socket", "math", "functools", "sys", "typing", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc", "enum", "types", "typing_extensions"], "hash": "f1b41885f8de1e523fd4b18f2e226fd43d5ab53c", "id": "pycares", "ignore_all": true, "interface_hash": "10be1138b653d7dfc2da6ba0d7206c972501ae0d", "mtime": 1749398785, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pycares\\__init__.py", "plugin_data": null, "size": 33758, "suppressed": ["pycares._cares", "_cffi_backend"], "version_id": "1.15.0"}