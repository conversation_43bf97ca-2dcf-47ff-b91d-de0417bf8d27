{".class": "MypyFile", "_fullname": "soupsieve", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "DEBUG": {".class": "SymbolTableNode", "cross_ref": "soupsieve.util.DEBUG", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "SelectorSyntaxError": {".class": "SymbolTableNode", "cross_ref": "soupsieve.util.SelectorSyntaxError", "kind": "Gdef"}, "SoupSieve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "soupsieve.SoupSieve", "line": 43, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "soupsieve.css_match.SoupSieve"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "soupsieve.__meta__.__version__", "kind": "Gdef", "module_public": false}, "__version_info__": {".class": "SymbolTableNode", "cross_ref": "soupsieve.__meta__.__version_info__", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "bs4": {".class": "SymbolTableNode", "cross_ref": "bs4", "kind": "Gdef", "module_public": false}, "closest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "flags", "custom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.closest", "name": "closest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "flags", "custom", "kwargs"], "arg_types": ["builtins.str", "bs4.element.Tag", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closest", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cm": {".class": "SymbolTableNode", "cross_ref": "soupsieve.css_match", "kind": "Gdef", "module_public": false}, "compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["pattern", "namespaces", "flags", "custom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.compile", "name": "compile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 4], "arg_names": ["pattern", "namespaces", "flags", "custom", "kwargs"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile", "ret_type": "soupsieve.css_match.SoupSieve", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cp": {".class": "SymbolTableNode", "cross_ref": "soupsieve.css_parser", "kind": "Gdef", "module_public": false}, "ct": {".class": "SymbolTableNode", "cross_ref": "soupsieve.css_types", "kind": "Gdef", "module_public": false}, "escape": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ident"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.escape", "name": "escape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ident"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "escape", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "iterable", "namespaces", "flags", "custom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.filter", "name": "filter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "iterable", "namespaces", "flags", "custom", "kwargs"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iselect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "limit", "flags", "custom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.iselect", "name": "iselect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "limit", "flags", "custom", "kwargs"], "arg_types": ["builtins.str", "bs4.element.Tag", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iselect", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "flags", "custom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "flags", "custom", "kwargs"], "arg_types": ["builtins.str", "bs4.element.Tag", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "purge": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.purge", "name": "purge", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "purge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "limit", "flags", "custom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.select", "name": "select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "limit", "flags", "custom", "kwargs"], "arg_types": ["builtins.str", "bs4.element.Tag", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select_one": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "flags", "custom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.select_one", "name": "select_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5, 4], "arg_names": ["select", "tag", "namespaces", "flags", "custom", "kwargs"], "arg_types": ["builtins.str", "bs4.element.Tag", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_one", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\soupsieve\\__init__.py"}