{"data_mtime": 1753783523, "dep_lines": [29, 30, 31, 32, 33, 28, 34, 35, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 10, 5, 5, 20, 30, 30, 30], "dependencies": ["soupsieve.__meta__", "soupsieve.css_parser", "soupsieve.css_match", "soupsieve.css_types", "soupsieve.util", "__future__", "bs4", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc", "bs4.element"], "hash": "6b2d7dc11d48a87687da6ebaf8f6cd323d03a964", "id": "soupsieve", "ignore_all": true, "interface_hash": "40f39e13b1731a5edfc7594bf778f552911ad400", "mtime": 1748023117, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\soupsieve\\__init__.py", "plugin_data": null, "size": 4581, "suppressed": [], "version_id": "1.15.0"}