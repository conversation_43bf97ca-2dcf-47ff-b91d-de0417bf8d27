{".class": "MypyFile", "_fullname": "soupsieve.css_match", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CSSMatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["soupsieve.css_match._DocumentNav"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_match.CSSMatch", "name": "CSSMatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_match", "mro": ["soupsieve.css_match.CSSMatch", "soupsieve.css_match._DocumentNav", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "selectors", "scope", "namespaces", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "selectors", "scope", "namespaces", "flags"], "arg_types": ["soupsieve.css_match.CSSMatch", "soupsieve.css_types.SelectorList", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["soupsieve.css_types.Namespaces", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CSSMatch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cached_default_forms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.cached_default_forms", "name": "cached_default_forms", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["bs4.element.Tag", "bs4.element.Tag"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cached_indeterminate_forms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.cached_indeterminate_forms", "name": "cached_indeterminate_forms", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["bs4.element.Tag", "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cached_meta_lang": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.cached_meta_lang", "name": "cached_meta_lang", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "closest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.closest", "name": "closest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["soupsieve.css_match.CSSMatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closest of CSSMatch", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extended_language_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lang_range", "lang_tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.extended_language_filter", "name": "extended_language_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lang_range", "lang_tag"], "arg_types": ["soupsieve.css_match.CSSMatch", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extended_language_filter of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.filter", "name": "filter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["soupsieve.css_match.CSSMatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter of CSSMatch", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_bidi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.find_bidi", "name": "find_bidi", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_bidi of CSSMatch", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.flags", "name": "flags", "type": "builtins.int"}}, "get_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.get_prefix", "name": "get_prefix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_prefix of CSSMatch", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.get_tag", "name": "get_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag of CSSMatch", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tag_ns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.get_tag_ns", "name": "get_tag_ns", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag_ns of CSSMatch", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_html_namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.has_html_namespace", "name": "has_html_namespace", "type": "builtins.bool"}}, "iframe_restrict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.iframe_restrict", "name": "iframe_restrict", "type": "builtins.bool"}}, "is_html": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.is_html", "name": "is_html", "type": "builtins.bool"}}, "is_html_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.is_html_tag", "name": "is_html_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_html_tag of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_xml": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.is_xml", "name": "is_xml", "type": "builtins.bool"}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_attribute_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "el", "attr", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_attribute_name", "name": "match_attribute_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "el", "attr", "prefix"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_attribute_name of CSSMatch", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_attributes", "name": "match_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "attributes"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "Instance", "args": ["soupsieve.css_types.SelectorAttribute"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_attributes of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "classes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_classes", "name": "match_classes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "classes"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_classes of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_contains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "contains"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_contains", "name": "match_contains", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "contains"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "Instance", "args": ["soupsieve.css_types.SelectorContains"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_contains of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_default", "name": "match_default", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_default of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_defined": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_defined", "name": "match_defined", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_defined of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "directionality"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_dir", "name": "match_dir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "directionality"], "arg_types": ["soupsieve.css_match.CSSMatch", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_dir of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_empty", "name": "match_empty", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_empty of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_future_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "parent", "relation", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_future_child", "name": "match_future_child", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "parent", "relation", "recursive"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "soupsieve.css_types.SelectorList", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_future_child of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_future_relations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "relation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_future_relations", "name": "match_future_relations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "relation"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "soupsieve.css_types.SelectorList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_future_relations of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_id", "name": "match_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "ids"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_id of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_indeterminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_indeterminate", "name": "match_indeterminate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_indeterminate of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_lang": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "langs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_lang", "name": "match_lang", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "langs"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "Instance", "args": ["soupsieve.css_types.SelectorLang"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_lang of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_namespace", "name": "match_namespace", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "tag"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "soupsieve.css_types.SelectorTag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_namespace of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_nth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "nth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_nth", "name": "match_nth", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "nth"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "Instance", "args": ["soupsieve.css_types.SelectorNth"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_nth of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_nth_tag_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "child"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_nth_tag_type", "name": "match_nth_tag_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "child"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_nth_tag_type of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_past_relations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "relation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_past_relations", "name": "match_past_relations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "relation"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "soupsieve.css_types.SelectorList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_past_relations of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_placeholder_shown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_placeholder_shown", "name": "match_placeholder_shown", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_placeholder_shown of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_range": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "condition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_range", "name": "match_range", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "condition"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_range of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_relations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "relation"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_relations", "name": "match_relations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "relation"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "soupsieve.css_types.SelectorList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_relations of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_root", "name": "match_root", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_root of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_scope", "name": "match_scope", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_scope of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_selectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "selectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_selectors", "name": "match_selectors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "selectors"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "soupsieve.css_types.SelectorList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_selectors of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_subselectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "selectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_subselectors", "name": "match_subselectors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "selectors"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "Instance", "args": ["soupsieve.css_types.SelectorList"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_subselectors of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_tag", "name": "match_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "tag"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", {".class": "UnionType", "items": ["soupsieve.css_types.SelectorTag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_tag of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_tagname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.match_tagname", "name": "match_tagname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "el", "tag"], "arg_types": ["soupsieve.css_match.CSSMatch", "bs4.element.Tag", "soupsieve.css_types.SelectorTag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_tagname of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "namespaces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.namespaces", "name": "namespaces", "type": {".class": "UnionType", "items": ["soupsieve.css_types.Namespaces", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}}}, "root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.root", "name": "root", "type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.scope", "name": "scope", "type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.select", "name": "select", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "limit"], "arg_types": ["soupsieve.css_match.CSSMatch", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select of CSSMatch", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selectors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.selectors", "name": "selectors", "type": "soupsieve.css_types.SelectorList"}}, "supports_namespaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.CSSMatch.supports_namespaces", "name": "supports_namespaces", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["soupsieve.css_match.CSSMatch"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_namespaces of CSSMatch", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match.CSSMatch.tag", "name": "tag", "type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_match.CSSMatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_match.CSSMatch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DAYS_IN_WEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.DAYS_IN_WEEK", "name": "DAYS_IN_WEEK", "type": "builtins.int"}}, "DIR_FLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.DIR_FLAGS", "name": "DIR_FLAGS", "type": "builtins.int"}}, "DIR_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.DIR_MAP", "name": "DIR_MAP", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "FEB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.FEB", "name": "FEB", "type": "builtins.int"}}, "FEB_LEAP_MONTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.FEB_LEAP_MONTH", "name": "FEB_LEAP_MONTH", "type": "builtins.int"}}, "FEB_MONTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.FEB_MONTH", "name": "FEB_MONTH", "type": "builtins.int"}}, "Inputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_match.Inputs", "name": "Inputs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.Inputs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_match", "mro": ["soupsieve.css_match.Inputs", "builtins.object"], "names": {".class": "SymbolTable", "parse_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "itype", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match.Inputs.parse_value", "name": "parse_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "itype", "value"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match.Inputs"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_value of Inputs", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.Inputs.parse_value", "name": "parse_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "itype", "value"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match.Inputs"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_value of Inputs", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_day": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["year", "month", "day"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match.Inputs.validate_day", "name": "validate_day", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["year", "month", "day"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_day of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.Inputs.validate_day", "name": "validate_day", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["year", "month", "day"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_day of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_hour": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["hour"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match.Inputs.validate_hour", "name": "validate_hour", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["hour"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_hour of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.Inputs.validate_hour", "name": "validate_hour", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["hour"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_hour of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_minutes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["minutes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match.Inputs.validate_minutes", "name": "validate_minutes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["minutes"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_minutes of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.Inputs.validate_minutes", "name": "validate_minutes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["minutes"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_minutes of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_month": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["month"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match.Inputs.validate_month", "name": "validate_month", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["month"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_month of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.Inputs.validate_month", "name": "validate_month", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["month"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_month of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_week": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["year", "week"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match.Inputs.validate_week", "name": "validate_week", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["year", "week"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_week of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.Inputs.validate_week", "name": "validate_week", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["year", "week"], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_week of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate_year": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["year"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match.Inputs.validate_year", "name": "validate_year", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["year"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_year of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match.Inputs.validate_year", "name": "validate_year", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["year"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_year of Inputs", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_match.Inputs.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_match.Inputs", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LONG_MONTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.LONG_MONTH", "name": "LONG_MONTH", "type": "builtins.int"}}, "MONTHS_30": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.MONTHS_30", "name": "MONTHS_30", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "NS_XHTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.NS_XHTML", "name": "NS_XHTML", "type": "builtins.str"}}, "NS_XML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.NS_XML", "name": "NS_XML", "type": "builtins.str"}}, "RANGES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RANGES", "name": "RANGES", "type": "builtins.int"}}, "REL_CLOSE_PARENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_CLOSE_PARENT", "name": "REL_CLOSE_PARENT", "type": "builtins.str"}}, "REL_CLOSE_SIBLING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_CLOSE_SIBLING", "name": "REL_CLOSE_SIBLING", "type": "builtins.str"}}, "REL_HAS_CLOSE_PARENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_HAS_CLOSE_PARENT", "name": "REL_HAS_CLOSE_PARENT", "type": "builtins.str"}}, "REL_HAS_CLOSE_SIBLING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_HAS_CLOSE_SIBLING", "name": "REL_HAS_CLOSE_SIBLING", "type": "builtins.str"}}, "REL_HAS_PARENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_HAS_PARENT", "name": "REL_HAS_PARENT", "type": "builtins.str"}}, "REL_HAS_SIBLING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_HAS_SIBLING", "name": "REL_HAS_SIBLING", "type": "builtins.str"}}, "REL_PARENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_PARENT", "name": "REL_PARENT", "type": "builtins.str"}}, "REL_SIBLING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.REL_SIBLING", "name": "REL_SIBLING", "type": "builtins.str"}}, "RE_DATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_DATE", "name": "RE_DATE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_DATETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_DATETIME", "name": "RE_DATETIME", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_MONTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_MONTH", "name": "RE_MONTH", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_NOT_EMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_NOT_EMPTY", "name": "RE_NOT_EMPTY", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_NOT_WS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_NOT_WS", "name": "RE_NOT_WS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_NUM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_NUM", "name": "RE_NUM", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_TIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_TIME", "name": "RE_TIME", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_WEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_WEEK", "name": "RE_WEEK", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_WILD_STRIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.RE_WILD_STRIP", "name": "RE_WILD_STRIP", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "SHORT_MONTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.SHORT_MONTH", "name": "SHORT_MONTH", "type": "builtins.int"}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SoupSieve": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["soupsieve.css_types.Immutable"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_match.SoupSieve", "name": "SoupSieve", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_match", "mro": ["soupsieve.css_match.SoupSieve", "soupsieve.css_types.Immutable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "pattern", "selectors", "namespaces", "custom", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "pattern", "selectors", "namespaces", "custom", "flags"], "arg_types": ["soupsieve.css_match.SoupSieve", "builtins.str", "soupsieve.css_types.SelectorList", {".class": "UnionType", "items": ["soupsieve.css_types.Namespaces", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["soupsieve.css_types.CustomSelectors", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SoupSieve", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["soupsieve.css_match.SoupSieve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of SoupSieve", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "soupsieve.css_match.SoupSieve.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_match.SoupSieve.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["soupsieve.css_match.SoupSieve"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "closest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.closest", "name": "closest", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["soupsieve.css_match.SoupSieve", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closest of SoupSieve", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "custom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "soupsieve.css_match.SoupSieve.custom", "name": "custom", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.filter", "name": "filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "iterable"], "arg_types": ["soupsieve.css_match.SoupSieve", {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter of SoupSieve", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "soupsieve.css_match.SoupSieve.flags", "name": "flags", "type": "builtins.int"}}, "iselect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tag", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.iselect", "name": "iselect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tag", "limit"], "arg_types": ["soupsieve.css_match.SoupSieve", "bs4.element.Tag", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iselect of SoupSieve", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["soupsieve.css_match.SoupSieve", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of SoupSieve", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "namespaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "soupsieve.css_match.SoupSieve.namespaces", "name": "namespaces", "type": {".class": "UnionType", "items": ["soupsieve.css_types.Namespaces", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "soupsieve.css_match.SoupSieve.pattern", "name": "pattern", "type": "builtins.str"}}, "select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tag", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.select", "name": "select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tag", "limit"], "arg_types": ["soupsieve.css_match.SoupSieve", "bs4.element.Tag", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select of SoupSieve", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match.SoupSieve.select_one", "name": "select_one", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag"], "arg_types": ["soupsieve.css_match.SoupSieve", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_one of SoupSieve", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "soupsieve.css_match.SoupSieve.selectors", "name": "selectors", "type": "soupsieve.css_types.SelectorList"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_match.SoupSieve.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_match.SoupSieve", "values": [], "variance": 0}, "slots": ["_hash", "custom", "flags", "namespaces", "pattern", "selectors"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DocumentNav": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_match._DocumentNav", "name": "_DocumentNav", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_match", "mro": ["soupsieve.css_match._DocumentNav", "builtins.object"], "names": {".class": "SymbolTable", "assert_valid_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.assert_valid_input", "name": "assert_valid_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tag"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_valid_input of _DocumentNav", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.assert_valid_input", "name": "assert_valid_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tag"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_valid_input of _DocumentNav", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_fake_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.create_fake_parent", "name": "create_fake_parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_fake_parent of _DocumentNav", "ret_type": "soupsieve.css_match._FakeParent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.create_fake_parent", "name": "create_fake_parent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_fake_parent of _DocumentNav", "ret_type": "soupsieve.css_match._FakeParent", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_attribute_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "el", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_attribute_by_name", "name": "get_attribute_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "el", "name", "default"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attribute_by_name of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_attribute_by_name", "name": "get_attribute_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "el", "name", "default"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_attribute_by_name of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "el", "start", "reverse", "tags", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_children", "name": "get_children", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "el", "start", "reverse", "tags", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_children of _DocumentNav", "ret_type": {".class": "Instance", "args": ["bs4.element.PageElement"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_classes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_classes", "name": "get_classes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_classes of _DocumentNav", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_classes", "name": "get_classes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_classes of _DocumentNav", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_contents", "name": "get_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_contents of _DocumentNav", "ret_type": {".class": "Instance", "args": ["bs4.element.PageElement"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_descendants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "el", "tags", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_descendants", "name": "get_descendants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "el", "tags", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_descendants of _DocumentNav", "ret_type": {".class": "Instance", "args": ["bs4.element.PageElement"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_next", "name": "get_next", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_next of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_next", "name": "get_next", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_next of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_next_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_next_tag", "name": "get_next_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_next_tag of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_next_tag", "name": "get_next_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_next_tag of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_own_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_own_text", "name": "get_own_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_own_text of _DocumentNav", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_parent", "name": "get_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_parent of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_prefix_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_prefix_name", "name": "get_prefix_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_prefix_name of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_prefix_name", "name": "get_prefix_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": ["bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_prefix_name of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_previous": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_previous", "name": "get_previous", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_previous of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_previous", "name": "get_previous", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_previous of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_previous_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_previous_tag", "name": "get_previous_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_previous_tag of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_previous_tag", "name": "get_previous_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "el", "tags"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_previous_tag of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_tag_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "el", "start", "reverse", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_tag_children", "name": "get_tag_children", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "el", "start", "reverse", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag_children of _DocumentNav", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tag_descendants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_tag_descendants", "name": "get_tag_descendants", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag_descendants of _DocumentNav", "ret_type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tag_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_tag_name", "name": "get_tag_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag_name of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_tag_name", "name": "get_tag_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag_name of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.get_text", "name": "get_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "el", "no_iframe"], "arg_types": ["soupsieve.css_match._DocumentNav", "bs4.element.Tag", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_text of _DocumentNav", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_uri": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.get_uri", "name": "get_uri", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_uri of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.get_uri", "name": "get_uri", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_uri of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_html_ns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.has_html_ns", "name": "has_html_ns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_html_ns of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.has_html_ns", "name": "has_html_ns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_html_ns of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_cdata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_cdata", "name": "is_cdata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_cdata of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_cdata", "name": "is_cdata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_cdata of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_content_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_content_string", "name": "is_content_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_content_string of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_content_string", "name": "is_content_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_content_string of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_declaration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_declaration", "name": "is_declaration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_declaration of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_declaration", "name": "is_declaration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_declaration of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_doc", "name": "is_doc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_doc of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_doc", "name": "is_doc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_doc of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_iframe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.is_iframe", "name": "is_iframe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match._DocumentNav", {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_iframe of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_navigable_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_navigable_string", "name": "is_navigable_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_navigable_string of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_navigable_string", "name": "is_navigable_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_navigable_string of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_processing_instruction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_processing_instruction", "name": "is_processing_instruction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_processing_instruction of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_processing_instruction", "name": "is_processing_instruction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_processing_instruction of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._DocumentNav.is_root", "name": "is_root", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "el"], "arg_types": ["soupsieve.css_match._DocumentNav", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_root of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_special_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_special_string", "name": "is_special_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_special_string of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_special_string", "name": "is_special_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_special_string of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_tag", "name": "is_tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_tag of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_tag", "name": "is_tag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.PageElement", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_tag of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_xml_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.is_xml_tree", "name": "is_xml_tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_xml_tree of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.is_xml_tree", "name": "is_xml_tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["el"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_xml_tree of _DocumentNav", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "iter_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.iter_attributes", "name": "iter_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_attributes of _DocumentNav", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.iter_attributes", "name": "iter_attributes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "el"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_attributes of _DocumentNav", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "normalize_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.normalize_value", "name": "normalize_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_value of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.normalize_value", "name": "normalize_value", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": "soupsieve.css_match._DocumentNav"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_value of _DocumentNav", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "split_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["el", "attr_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "soupsieve.css_match._DocumentNav.split_namespace", "name": "split_namespace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["el", "attr_name"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_namespace of _DocumentNav", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "soupsieve.css_match._DocumentNav.split_namespace", "name": "split_namespace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["el", "attr_name"], "arg_types": [{".class": "UnionType", "items": ["bs4.element.Tag", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_namespace of _DocumentNav", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_match._DocumentNav.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_match._DocumentNav", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FakeParent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_match._FakeParent", "name": "_FakeParent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._FakeParent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_match", "mro": ["soupsieve.css_match._FakeParent", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._FakeParent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": ["soupsieve.css_match._FakeParent", "bs4.element.Tag"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _FakeParent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_match._FakeParent.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["soupsieve.css_match._FakeParent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of _FakeParent", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_match._FakeParent.contents", "name": "contents", "type": {".class": "Instance", "args": ["bs4.element.Tag"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_match._FakeParent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_match._FakeParent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_match.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_match.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_match.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_match.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_match.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_match.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "bs4": {".class": "SymbolTableNode", "cross_ref": "bs4", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "ct": {".class": "SymbolTableNode", "cross_ref": "soupsieve.css_types", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "soupsieve.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\soupsieve\\css_match.py"}