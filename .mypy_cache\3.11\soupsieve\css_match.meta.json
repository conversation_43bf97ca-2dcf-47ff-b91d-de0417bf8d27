{"data_mtime": 1753783523, "dep_lines": [4, 6, 2, 3, 4, 5, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 10, 10, 10, 5, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["soupsieve.util", "soupsieve.css_types", "__future__", "datetime", "soupsieve", "re", "unicodedata", "bs4", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc", "bs4.element", "enum", "types"], "hash": "e459001610a8c8e8a26b009101307c1470b2d33b", "id": "soupsieve.css_match", "ignore_all": true, "interface_hash": "f2ce2c9476eb185f73cc1e4c2aadb79f8909ed13", "mtime": 1748023117, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\soupsieve\\css_match.py", "plugin_data": null, "size": 60794, "suppressed": [], "version_id": "1.15.0"}