{".class": "MypyFile", "_fullname": "soupsieve.css_parser", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.ATTR", "name": "ATTR", "type": "builtins.str"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "COMMA_COMBINATOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.COMMA_COMBINATOR", "name": "COMMA_COMBINATOR", "type": "builtins.str"}}, "COMMENTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.COMMENTS", "name": "COMMENTS", "type": "builtins.str"}}, "CSSParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_parser.CSSParser", "name": "CSSParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_parser", "mro": ["soupsieve.css_parser.CSSParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "selector", "custom", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "selector", "custom", "flags"], "arg_types": ["soupsieve.css_parser.CSSParser", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "soupsieve.css_types.SelectorList"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CSSParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "css_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSSParser.css_tokens", "name": "css_tokens", "type": {".class": "TupleType", "implicit": false, "items": ["soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SpecialPseudoPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern", "soupsieve.css_parser.SelectorPattern"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "custom": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.CSSParser.custom", "name": "custom", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "soupsieve.css_types.SelectorList"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "debug": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.CSSParser.debug", "name": "debug", "type": "builtins.int"}}, "flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.CSSParser.flags", "name": "flags", "type": "builtins.int"}}, "parse_attribute_selector": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_attribute_selector", "name": "parse_attribute_selector", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_attribute_selector of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_class_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_class_id", "name": "parse_class_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_class_id of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_combinator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "selectors", "relations", "is_pseudo", "is_forgive", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_combinator", "name": "parse_combinator", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "selectors", "relations", "is_pseudo", "is_forgive", "index"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool", {".class": "Instance", "args": ["soupsieve.css_parser._Selector"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["soupsieve.css_parser._Selector"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_combinator of CSSParser", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "soupsieve.css_parser._Selector"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_has_combinator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "selectors", "rel_type", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_has_combinator", "name": "parse_has_combinator", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "selectors", "rel_type", "index"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool", {".class": "Instance", "args": ["soupsieve.css_parser._Selector"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_has_combinator of CSSParser", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "soupsieve.css_parser._Selector", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pseudo_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "iselector", "is_html"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_pseudo_class", "name": "parse_pseudo_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "iselector", "is_html"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pseudo_class of CSSParser", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pseudo_class_custom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_pseudo_class_custom", "name": "parse_pseudo_class_custom", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pseudo_class_custom of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pseudo_contains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_pseudo_contains", "name": "parse_pseudo_contains", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pseudo_contains of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pseudo_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_pseudo_dir", "name": "parse_pseudo_dir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pseudo_dir of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pseudo_lang": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_pseudo_lang", "name": "parse_pseudo_lang", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pseudo_lang of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pseudo_nth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "iselector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_pseudo_nth", "name": "parse_pseudo_nth", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector", "iselector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pseudo_nth of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pseudo_open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "name", "has_selector", "iselector", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_pseudo_open", "name": "parse_pseudo_open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "sel", "name", "has_selector", "iselector", "index"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", "builtins.str", "builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pseudo_open of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_selectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "iselector", "index", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_selectors", "name": "parse_selectors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "iselector", "index", "flags"], "arg_types": ["soupsieve.css_parser.CSSParser", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_selectors of CSSParser", "ret_type": "soupsieve.css_types.SelectorList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_tag_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.parse_tag_pattern", "name": "parse_tag_pattern", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "sel", "m", "has_selector"], "arg_types": ["soupsieve.css_parser.CSSParser", "soupsieve.css_parser._Selector", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_tag_pattern of CSSParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.CSSParser.pattern", "name": "pattern", "type": "builtins.str"}}, "process_selectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "index", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.process_selectors", "name": "process_selectors", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "index", "flags"], "arg_types": ["soupsieve.css_parser.CSSParser", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_selectors of CSSParser", "ret_type": "soupsieve.css_types.SelectorList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "selector_iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.CSSParser.selector_iter", "name": "selector_iter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pattern"], "arg_types": ["soupsieve.css_parser.CSSParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "selector_iter of CSSParser", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_parser.CSSParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_parser.CSSParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CSS_CHECKED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_CHECKED", "name": "CSS_CHECKED", "type": "soupsieve.css_types.SelectorList"}}, "CSS_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_DEFAULT", "name": "CSS_DEFAULT", "type": "soupsieve.css_types.SelectorList"}}, "CSS_DISABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_DISABLED", "name": "CSS_DISABLED", "type": "soupsieve.css_types.SelectorList"}}, "CSS_ENABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_ENABLED", "name": "CSS_ENABLED", "type": "soupsieve.css_types.SelectorList"}}, "CSS_ESCAPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_ESCAPES", "name": "CSS_ESCAPES", "type": "builtins.str"}}, "CSS_INDETERMINATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_INDETERMINATE", "name": "CSS_INDETERMINATE", "type": "soupsieve.css_types.SelectorList"}}, "CSS_IN_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_IN_RANGE", "name": "CSS_IN_RANGE", "type": "soupsieve.css_types.SelectorList"}}, "CSS_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_LINK", "name": "CSS_LINK", "type": "soupsieve.css_types.SelectorList"}}, "CSS_MUTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_MUTED", "name": "CSS_MUTED", "type": "soupsieve.css_types.SelectorList"}}, "CSS_NTH_OF_S_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_NTH_OF_S_DEFAULT", "name": "CSS_NTH_OF_S_DEFAULT", "type": "soupsieve.css_types.SelectorList"}}, "CSS_OPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_OPEN", "name": "CSS_OPEN", "type": "soupsieve.css_types.SelectorList"}}, "CSS_OPTIONAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_OPTIONAL", "name": "CSS_OPTIONAL", "type": "soupsieve.css_types.SelectorList"}}, "CSS_OUT_OF_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_OUT_OF_RANGE", "name": "CSS_OUT_OF_RANGE", "type": "soupsieve.css_types.SelectorList"}}, "CSS_PLACEHOLDER_SHOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_PLACEHOLDER_SHOWN", "name": "CSS_PLACEHOLDER_SHOWN", "type": "soupsieve.css_types.SelectorList"}}, "CSS_READ_ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_READ_ONLY", "name": "CSS_READ_ONLY", "type": "soupsieve.css_types.SelectorList"}}, "CSS_READ_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_READ_WRITE", "name": "CSS_READ_WRITE", "type": "soupsieve.css_types.SelectorList"}}, "CSS_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_REQUIRED", "name": "CSS_REQUIRED", "type": "soupsieve.css_types.SelectorList"}}, "CSS_STRING_ESCAPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.CSS_STRING_ESCAPES", "name": "CSS_STRING_ESCAPES", "type": "builtins.str"}}, "FLG_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_DEFAULT", "name": "FLG_DEFAULT", "type": "builtins.int"}}, "FLG_FORGIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_FORGIVE", "name": "FLG_FORGIVE", "type": "builtins.int"}}, "FLG_HTML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_HTML", "name": "FLG_HTML", "type": "builtins.int"}}, "FLG_INDETERMINATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_INDETERMINATE", "name": "FLG_INDETERMINATE", "type": "builtins.int"}}, "FLG_IN_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_IN_RANGE", "name": "FLG_IN_RANGE", "type": "builtins.int"}}, "FLG_NOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_NOT", "name": "FLG_NOT", "type": "builtins.int"}}, "FLG_OPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_OPEN", "name": "FLG_OPEN", "type": "builtins.int"}}, "FLG_OUT_OF_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_OUT_OF_RANGE", "name": "FLG_OUT_OF_RANGE", "type": "builtins.int"}}, "FLG_PLACEHOLDER_SHOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_PLACEHOLDER_SHOWN", "name": "FLG_PLACEHOLDER_SHOWN", "type": "builtins.int"}}, "FLG_PSEUDO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_PSEUDO", "name": "FLG_PSEUDO", "type": "builtins.int"}}, "FLG_RELATIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.FLG_RELATIVE", "name": "FLG_RELATIVE", "type": "builtins.int"}}, "IDENTIFIER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.IDENTIFIER", "name": "IDENTIFIER", "type": "builtins.str"}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "re.Match", "kind": "Gdef"}, "NEWLINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.NEWLINE", "name": "NEWLINE", "type": "builtins.str"}}, "NTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.NTH", "name": "NTH", "type": "builtins.str"}}, "PAT_AMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_AMP", "name": "PAT_AMP", "type": "builtins.str"}}, "PAT_ATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_ATTR", "name": "PAT_ATTR", "type": "builtins.str"}}, "PAT_AT_RULE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_AT_RULE", "name": "PAT_AT_RULE", "type": "builtins.str"}}, "PAT_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_CLASS", "name": "PAT_CLASS", "type": "builtins.str"}}, "PAT_COMBINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_COMBINE", "name": "PAT_COMBINE", "type": "builtins.str"}}, "PAT_ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_ID", "name": "PAT_ID", "type": "builtins.str"}}, "PAT_PSEUDO_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_CLASS", "name": "PAT_PSEUDO_CLASS", "type": "builtins.str"}}, "PAT_PSEUDO_CLASS_CUSTOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_CLASS_CUSTOM", "name": "PAT_PSEUDO_CLASS_CUSTOM", "type": "builtins.str"}}, "PAT_PSEUDO_CLASS_SPECIAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_CLASS_SPECIAL", "name": "PAT_PSEUDO_CLASS_SPECIAL", "type": "builtins.str"}}, "PAT_PSEUDO_CLOSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_CLOSE", "name": "PAT_PSEUDO_CLOSE", "type": "builtins.str"}}, "PAT_PSEUDO_CONTAINS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_CONTAINS", "name": "PAT_PSEUDO_CONTAINS", "type": "builtins.str"}}, "PAT_PSEUDO_DIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_DIR", "name": "PAT_PSEUDO_DIR", "type": "builtins.str"}}, "PAT_PSEUDO_ELEMENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_ELEMENT", "name": "PAT_PSEUDO_ELEMENT", "type": "builtins.str"}}, "PAT_PSEUDO_LANG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_LANG", "name": "PAT_PSEUDO_LANG", "type": "builtins.str"}}, "PAT_PSEUDO_NTH_CHILD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_NTH_CHILD", "name": "PAT_PSEUDO_NTH_CHILD", "type": "builtins.str"}}, "PAT_PSEUDO_NTH_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_PSEUDO_NTH_TYPE", "name": "PAT_PSEUDO_NTH_TYPE", "type": "builtins.str"}}, "PAT_TAG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PAT_TAG", "name": "PAT_TAG", "type": "builtins.str"}}, "PSEUDO_COMPLEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PSEUDO_COMPLEX", "name": "PSEUDO_COMPLEX", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "PSEUDO_COMPLEX_NO_MATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PSEUDO_COMPLEX_NO_MATCH", "name": "PSEUDO_COMPLEX_NO_MATCH", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "PSEUDO_SIMPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PSEUDO_SIMPLE", "name": "PSEUDO_SIMPLE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "PSEUDO_SIMPLE_NO_MATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PSEUDO_SIMPLE_NO_MATCH", "name": "PSEUDO_SIMPLE_NO_MATCH", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "PSEUDO_SPECIAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PSEUDO_SPECIAL", "name": "PSEUDO_SPECIAL", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "PSEUDO_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.PSEUDO_SUPPORTED", "name": "PSEUDO_SUPPORTED", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "RE_CSS_ESC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_CSS_ESC", "name": "RE_CSS_ESC", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_CSS_STR_ESC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_CSS_STR_ESC", "name": "RE_CSS_STR_ESC", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_CUSTOM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_CUSTOM", "name": "RE_CUSTOM", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_NTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_NTH", "name": "RE_NTH", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_VALUES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_VALUES", "name": "RE_VALUES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_WS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_WS", "name": "RE_WS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_WS_BEGIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_WS_BEGIN", "name": "RE_WS_BEGIN", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_WS_END": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.RE_WS_END", "name": "RE_WS_END", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "SelectorPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_parser.SelectorPattern", "name": "SelectorPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SelectorPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_parser", "mro": ["soupsieve.css_parser.SelectorPattern", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SelectorPattern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "pattern"], "arg_types": ["soupsieve.css_parser.SelectorPattern", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SelectorPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SelectorPattern.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["soupsieve.css_parser.SelectorPattern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name of SelectorPattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "selector", "index", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SelectorPattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "selector", "index", "flags"], "arg_types": ["soupsieve.css_parser.SelectorPattern", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of SelectorPattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.SelectorPattern.name", "name": "name", "type": "builtins.str"}}, "re_pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.SelectorPattern.re_pattern", "name": "re_pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_parser.SelectorPattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_parser.SelectorPattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectorSyntaxError": {".class": "SymbolTableNode", "cross_ref": "soupsieve.util.SelectorSyntaxError", "kind": "Gdef"}, "SpecialPseudoPattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["soupsieve.css_parser.SelectorPattern"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_parser.SpecialPseudoPattern", "name": "SpecialPseudoPattern", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SpecialPseudoPattern", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_parser", "mro": ["soupsieve.css_parser.SpecialPseudoPattern", "soupsieve.css_parser.SelectorPattern", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "patterns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SpecialPseudoPattern.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "patterns"], "arg_types": ["soupsieve.css_parser.SpecialPseudoPattern", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str", {".class": "TypeType", "item": "soupsieve.css_parser.SelectorPattern"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SpecialPseudoPattern", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SpecialPseudoPattern.get_name", "name": "get_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["soupsieve.css_parser.SpecialPseudoPattern"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_name of SpecialPseudoPattern", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "selector", "index", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.SpecialPseudoPattern.match", "name": "match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "selector", "index", "flags"], "arg_types": ["soupsieve.css_parser.SpecialPseudoPattern", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match of SpecialPseudoPattern", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matched_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser.SpecialPseudoPattern.matched_name", "name": "matched_name", "type": {".class": "UnionType", "items": ["soupsieve.css_parser.SelectorPattern", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.SpecialPseudoPattern.patterns", "name": "patterns", "type": {".class": "Instance", "args": ["builtins.str", "soupsieve.css_parser.SelectorPattern"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "re_pseudo_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "soupsieve.css_parser.SpecialPseudoPattern.re_pseudo_name", "name": "re_pseudo_name", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_parser.SpecialPseudoPattern.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_parser.SpecialPseudoPattern", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UNICODE_REPLACEMENT_CHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.UNICODE_REPLACEMENT_CHAR", "name": "UNICODE_REPLACEMENT_CHAR", "type": "builtins.int"}}, "VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.VALUE", "name": "VALUE", "type": "builtins.str"}}, "WS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.WS", "name": "WS", "type": "builtins.str"}}, "WSC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.WSC", "name": "WSC", "type": "builtins.str"}}, "WS_COMBINATOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser.WS_COMBINATOR", "name": "WS_COMBINATOR", "type": "builtins.str"}}, "_MAXCACHE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser._MAXCACHE", "name": "_MAXCACHE", "type": "builtins.int"}}, "_Selector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "soupsieve.css_parser._Selector", "name": "_Selector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser._Selector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "soupsieve.css_parser", "mro": ["soupsieve.css_parser._Selector", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser._Selector.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["soupsieve.css_parser._Selector", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Selector", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "soupsieve.css_parser._Selector.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["soupsieve.css_parser._Selector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser._Selector.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["soupsieve.css_parser._Selector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of _Selector", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_freeze_relations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "relations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser._Selector._freeze_relations", "name": "_freeze_relations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "relations"], "arg_types": ["soupsieve.css_parser._Selector", {".class": "Instance", "args": ["soupsieve.css_parser._Selector"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_freeze_relations of _Selector", "ret_type": "soupsieve.css_types.SelectorList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.attributes", "name": "attributes", "type": {".class": "Instance", "args": ["soupsieve.css_types.SelectorAttribute"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "classes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.classes", "name": "classes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "contains": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.contains", "name": "contains", "type": {".class": "Instance", "args": ["soupsieve.css_types.SelectorContains"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.flags", "name": "flags", "type": "builtins.int"}}, "freeze": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser._Selector.freeze", "name": "freeze", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["soupsieve.css_parser._Selector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "freeze of _Selector", "ret_type": {".class": "UnionType", "items": ["soupsieve.css_types.Selector", "soupsieve.css_types.SelectorNull"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.ids", "name": "ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "lang": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.lang", "name": "lang", "type": {".class": "Instance", "args": ["soupsieve.css_types.SelectorLang"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "no_match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.no_match", "name": "no_match", "type": "builtins.bool"}}, "nth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.nth", "name": "nth", "type": {".class": "Instance", "args": ["soupsieve.css_types.SelectorNth"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "rel_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.rel_type", "name": "rel_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "relations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.relations", "name": "relations", "type": {".class": "Instance", "args": ["soupsieve.css_parser._Selector"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "selectors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.selectors", "name": "selectors", "type": {".class": "Instance", "args": ["soupsieve.css_types.SelectorList"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "tag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._Selector.tag", "name": "tag", "type": {".class": "UnionType", "items": ["soupsieve.css_types.SelectorTag", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "soupsieve.css_parser._Selector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "soupsieve.css_parser._Selector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_parser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_parser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_parser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_parser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_parser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "soupsieve.css_parser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cached_css_compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["pattern", "namespaces", "custom", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "soupsieve.css_parser._cached_css_compile", "name": "_cached_css_compile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["pattern", "namespaces", "custom", "flags"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["soupsieve.css_types.Namespaces", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["soupsieve.css_types.CustomSelectors", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cached_css_compile", "ret_type": "soupsieve.css_match.SoupSieve", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "soupsieve.css_parser._cached_css_compile", "name": "_cached_css_compile", "type": {".class": "Instance", "args": ["soupsieve.css_match.SoupSieve"], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_purge_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser._purge_cache", "name": "_purge_cache", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_purge_cache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "cm": {".class": "SymbolTableNode", "cross_ref": "soupsieve.css_match", "kind": "Gdef"}, "css_unescape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["content", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.css_unescape", "name": "css_unescape", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["content", "string"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "css_unescape", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ct": {".class": "SymbolTableNode", "cross_ref": "soupsieve.css_types", "kind": "Gdef"}, "escape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ident"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.escape", "name": "escape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ident"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "escape", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "process_custom": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["custom"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "soupsieve.css_parser.process_custom", "name": "process_custom", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["custom"], "arg_types": [{".class": "UnionType", "items": ["soupsieve.css_types.CustomSelectors", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_custom", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "soupsieve.css_types.SelectorList"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "soupsieve.util", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\soupsieve\\css_parser.py"}