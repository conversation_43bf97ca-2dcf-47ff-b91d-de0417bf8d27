{"data_mtime": 1753783523, "dep_lines": [5, 6, 7, 2, 3, 4, 5, 9, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 5, 20, 10, 5, 5, 20, 30, 30, 30, 30], "dependencies": ["soupsieve.util", "soupsieve.css_match", "soupsieve.css_types", "__future__", "re", "functools", "soupsieve", "warnings", "typing", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc", "enum"], "hash": "32d4f2797167de7e28c603eddb3b93107e4f4c52", "id": "soupsieve.css_parser", "ignore_all": true, "interface_hash": "558b7a7aa455a4a180668f61ec3a56a776645d9b", "mtime": 1748023117, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\soupsieve\\css_parser.py", "plugin_data": null, "size": 47223, "suppressed": [], "version_id": "1.15.0"}