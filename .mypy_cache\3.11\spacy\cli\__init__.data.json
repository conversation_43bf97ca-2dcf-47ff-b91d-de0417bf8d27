{".class": "MypyFile", "_fullname": "spacy.cli", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "app": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.app", "kind": "Gdef"}, "apply": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.apply.apply", "kind": "Gdef"}, "assemble_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.assemble.assemble_cli", "kind": "Gdef"}, "benchmark_speed_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.benchmark_speed.benchmark_speed_cli", "kind": "Gdef"}, "convert": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.convert.convert", "kind": "Gdef"}, "debug_config": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.debug_config.debug_config", "kind": "Gdef"}, "debug_data": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.debug_data.debug_data", "kind": "Gdef"}, "debug_diff": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.debug_diff.debug_diff", "kind": "Gdef"}, "debug_model": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.debug_model.debug_model", "kind": "Gdef"}, "download": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.download.download", "kind": "Gdef"}, "download_module": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.download", "kind": "Gdef"}, "evaluate": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.evaluate.evaluate", "kind": "Gdef"}, "fill_config": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.init_config.fill_config", "kind": "Gdef"}, "find_function": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.find_function.find_function", "kind": "Gdef"}, "find_threshold": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.find_threshold.find_threshold", "kind": "Gdef"}, "info": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.info.info", "kind": "Gdef"}, "init_config": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.init_config.init_config", "kind": "Gdef"}, "init_pipeline_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.init_pipeline.init_pipeline_cli", "kind": "Gdef"}, "link": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.link", "name": "link", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.link", "name": "link", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "link", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "msg": {".class": "SymbolTableNode", "cross_ref": "wasabi.msg", "kind": "Gdef"}, "package": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.package.package", "kind": "Gdef"}, "pretrain": {".class": "SymbolTableNode", "cross_ref": "spacy.training.pretrain.pretrain", "kind": "Gdef"}, "profile": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.profile.profile", "kind": "Gdef"}, "project_assets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.project_assets", "name": "project_assets", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.project_assets", "source_any": null, "type_of_any": 3}}}, "project_clone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.project_clone", "name": "project_clone", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.project_clone", "source_any": null, "type_of_any": 3}}}, "project_document": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.project_document", "name": "project_document", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.project_document", "source_any": null, "type_of_any": 3}}}, "project_pull": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.project_pull", "name": "project_pull", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.project_pull", "source_any": null, "type_of_any": 3}}}, "project_push": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.project_push", "name": "project_push", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.project_push", "source_any": null, "type_of_any": 3}}}, "project_run": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.project_run", "name": "project_run", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.project_run", "source_any": null, "type_of_any": 3}}}, "project_update_dvc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.project_update_dvc", "name": "project_update_dvc", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.project_update_dvc", "source_any": null, "type_of_any": 3}}}, "setup_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.setup_cli", "kind": "Gdef"}, "train_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.train.train_cli", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.validate.validate", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\__init__.py"}