{"data_mtime": 1753783932, "dep_lines": [27, 28, 29, 32, 33, 34, 35, 4, 5, 6, 7, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26, 36, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["spacy.cli.project.assets", "spacy.cli.project.clone", "spacy.cli.project.document", "spacy.cli.project.dvc", "spacy.cli.project.pull", "spacy.cli.project.push", "spacy.cli.project.run", "spacy.cli.download", "spacy.cli._util", "spacy.cli.apply", "spacy.cli.assemble", "spacy.cli.benchmark_speed", "spacy.cli.convert", "spacy.cli.debug_config", "spacy.cli.debug_data", "spacy.cli.debug_diff", "spacy.cli.debug_model", "spacy.cli.evaluate", "spacy.cli.find_function", "spacy.cli.find_threshold", "spacy.cli.info", "spacy.cli.init_config", "spacy.cli.init_pipeline", "spacy.cli.package", "spacy.cli.pretrain", "spacy.cli.profile", "spacy.cli.train", "spacy.cli.validate", "wasabi", "builtins", "torch.distributed", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "click", "click.core", "typer", "typer.core", "typer.main", "typing"], "hash": "0775144263236c7b318b774bd20ab6ac3fc49543", "id": "spacy.cli", "ignore_all": true, "interface_hash": "523d09d9941d9bcc438614241648503ebba87eee", "mtime": 1749318793, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\__init__.py", "plugin_data": null, "size": 2446, "suppressed": [], "version_id": "1.15.0"}