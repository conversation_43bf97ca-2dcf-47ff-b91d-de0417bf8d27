{"data_mtime": 1753783925, "dep_lines": [23, 24, 25, 26, 30, 31, 32, 33, 1, 2, 3, 4, 5, 6, 7, 8, 21, 22, 27, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 20, 28], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 10, 5], "dependencies": ["click.parser", "thinc.api", "thinc.util", "typer.main", "spacy.about", "spacy.compat", "spacy.schemas", "spacy.util", "<PERSON><PERSON><PERSON>", "os", "shutil", "sys", "configparser", "contextlib", "pathlib", "typing", "typer", "click", "wasabi", "spacy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "click.core", "click.shell_completion", "click.types", "typer.core", "typer.params", "types"], "hash": "8b62d548ab403987b8dcfcc27804554862f9a785", "id": "spacy.cli._util", "ignore_all": true, "interface_hash": "08135bde4c1f8e18e552df7582aabe34606502a4", "mtime": 1749318793, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\_util.py", "plugin_data": null, "size": 11172, "suppressed": ["srsly", "weasel"], "version_id": "1.15.0"}