{".class": "MypyFile", "_fullname": "spacy.cli.benchmark_speed", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Arg": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Arg", "kind": "Gdef"}, "Corpus": {".class": "SymbolTableNode", "cross_ref": "spacy.training.corpus.Corpus", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Opt", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Quartiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.cli.benchmark_speed.Quartiles", "name": "Quartiles", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.Quartiles", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.cli.benchmark_speed", "mro": ["spacy.cli.benchmark_speed.Quartiles", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sample"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.Quartiles.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sample"], "arg_types": ["spacy.cli.benchmark_speed.Quartiles", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Quartiles", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iqr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "spacy.cli.benchmark_speed.Quartiles.iqr", "name": "iqr", "type": "builtins.float"}}, "q1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "spacy.cli.benchmark_speed.Quartiles.q1", "name": "q1", "type": "builtins.float"}}, "q2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "spacy.cli.benchmark_speed.Quartiles.q2", "name": "q2", "type": "builtins.float"}}, "q3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "spacy.cli.benchmark_speed.Quartiles.q3", "name": "q3", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.cli.benchmark_speed.Quartiles.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.cli.benchmark_speed.Quartiles", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.benchmark_speed.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.benchmark_speed.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.benchmark_speed.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.benchmark_speed.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.benchmark_speed.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.benchmark_speed.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["nlp", "docs", "batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.annotate", "name": "annotate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["nlp", "docs", "batch_size"], "arg_types": ["spacy.language.Language", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "annotate", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "benchmark": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["nlp", "docs", "n_batches", "batch_size", "shuffle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.benchmark", "name": "benchmark", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["nlp", "docs", "n_batches", "batch_size", "shuffle"], "arg_types": ["spacy.language.Language", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "benchmark", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "benchmark_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.benchmark_cli", "kind": "Gdef"}, "benchmark_speed_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["ctx", "model", "data_path", "batch_size", "no_shuffle", "use_gpu", "n_batches", "warmup_epochs", "code_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.benchmark_speed.benchmark_speed_cli", "name": "benchmark_speed_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["ctx", "model", "data_path", "batch_size", "no_shuffle", "use_gpu", "n_batches", "warmup_epochs", "code_path"], "arg_types": ["typer.models.Context", "builtins.str", "pathlib.Path", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "benchmark_speed_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.benchmark_speed.benchmark_speed_cli", "name": "benchmark_speed_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["ctx", "model", "data_path", "batch_size", "no_shuffle", "use_gpu", "n_batches", "warmup_epochs", "code_path"], "arg_types": ["typer.models.Context", "builtins.str", "pathlib.Path", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "benchmark_speed_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bootstrap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["x", "statistic", "iterations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.bootstrap", "name": "bootstrap", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["x", "statistic", "iterations"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bootstrap", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count_tokens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.count_tokens", "name": "count_tokens", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["docs"], "arg_types": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_tokens", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_code": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.import_code", "kind": "Gdef"}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "msg": {".class": "SymbolTableNode", "cross_ref": "wasabi.msg", "kind": "Gdef"}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "print_mean_with_ci": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sample"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.print_mean_with_ci", "name": "print_mean_with_ci", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sample"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_mean_with_ci", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "print_outliers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sample"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.print_outliers", "name": "print_outliers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sample"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "print_outliers", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "setup_gpu": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.setup_gpu", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "time_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.cli.benchmark_speed.time_context", "name": "time_context", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.time_context", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.cli.benchmark_speed", "mro": ["spacy.cli.benchmark_speed.time_context", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.time_context.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.time_context.__exit__", "name": "__exit__", "type": null}}, "elapsed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.cli.benchmark_speed.time_context.elapsed", "name": "elapsed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "start": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.cli.benchmark_speed.time_context.start", "name": "start", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.cli.benchmark_speed.time_context.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.cli.benchmark_speed.time_context", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.benchmark_speed.tqdm", "name": "tqdm", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.benchmark_speed.tqdm", "source_any": null, "type_of_any": 3}}}, "typer": {".class": "SymbolTableNode", "cross_ref": "typer", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "spacy.util", "kind": "Gdef"}, "warmup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["nlp", "docs", "warmup_epochs", "batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.benchmark_speed.warmup", "name": "warmup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["nlp", "docs", "warmup_epochs", "batch_size"], "arg_types": ["spacy.language.Language", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warmup", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\benchmark_speed.py"}