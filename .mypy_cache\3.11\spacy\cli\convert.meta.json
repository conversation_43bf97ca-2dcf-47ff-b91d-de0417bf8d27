{"data_mtime": 1753783931, "dep_lines": [13, 13, 13, 13, 13, 19, 11, 12, 1, 2, 3, 4, 5, 6, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [10, 10, 10, 10, 20, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["spacy.training.converters.conll_ner_to_docs", "spacy.training.converters.conllu_to_docs", "spacy.training.converters.iob_to_docs", "spacy.training.converters.json_to_docs", "spacy.training.converters", "spacy.cli._util", "spacy.tokens", "spacy.training", "itertools", "re", "sys", "enum", "pathlib", "typing", "wasabi", "builtins", "torch.distributed", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "click", "click.core", "click.shell_completion", "click.types", "spacy.tokens.doc", "typer", "typer.core", "typer.main", "typer.params", "types", "wasabi.printer"], "hash": "be5460a62507e7bfa6e60305cad8952d57394ef4", "id": "spacy.cli.convert", "ignore_all": true, "interface_hash": "61da1f305ccf91b8e0bd1ca7aecfe3796c3254ab", "mtime": 1749318793, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\convert.py", "plugin_data": null, "size": 9643, "suppressed": ["srsly"], "version_id": "1.15.0"}