{".class": "MypyFile", "_fullname": "spacy.cli.debug_config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Arg": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Arg", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "ConfigSchemaInit": {".class": "SymbolTableNode", "cross_ref": "spacy.schemas.ConfigSchemaInit", "kind": "Gdef"}, "ConfigSchemaTraining": {".class": "SymbolTableNode", "cross_ref": "spacy.schemas.ConfigSchemaTraining", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Opt", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VARIABLE_RE": {".class": "SymbolTableNode", "cross_ref": "confection.VARIABLE_RE", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "debug_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.debug_cli", "kind": "Gdef"}, "debug_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["config_path", "overrides", "show_funcs", "show_vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.debug_config.debug_config", "name": "debug_config", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["config_path", "overrides", "show_funcs", "show_vars"], "arg_types": ["pathlib.Path", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_config", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug_config_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "code_path", "show_funcs", "show_vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.debug_config.debug_config_cli", "name": "debug_config_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "code_path", "show_funcs", "show_vars"], "arg_types": ["typer.models.Context", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_config_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.debug_config.debug_config_cli", "name": "debug_config_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "code_path", "show_funcs", "show_vars"], "arg_types": ["typer.models.Context", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_config_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_registered_funcs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.debug_config.get_registered_funcs", "name": "get_registered_funcs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["confection.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_registered_funcs", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_variables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.debug_config.get_variables", "name": "get_variables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["confection.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_variables", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_code": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.import_code", "kind": "Gdef"}, "msg": {".class": "SymbolTableNode", "cross_ref": "wasabi.msg", "kind": "Gdef"}, "parse_config_overrides": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.parse_config_overrides", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "show_validation_error": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.show_validation_error", "kind": "Gdef"}, "table": {".class": "SymbolTableNode", "cross_ref": "wasabi.tables.table", "kind": "Gdef"}, "typer": {".class": "SymbolTableNode", "cross_ref": "typer", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "spacy.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\debug_config.py"}