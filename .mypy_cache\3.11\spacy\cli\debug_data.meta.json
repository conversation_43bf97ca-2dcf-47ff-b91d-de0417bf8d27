{"data_mtime": 1753783927, "dep_lines": [30, 34, 37, 24, 25, 26, 28, 32, 33, 1, 2, 3, 4, 5, 19, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29, 31, 27, 36, 20], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 10, 10, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 10], "dependencies": ["spacy.pipeline._parser_internals", "spacy.training.initialize", "spacy.cli._util", "spacy.util", "spacy.compat", "spacy.language", "spacy.pipeline", "spacy.schemas", "spacy.training", "math", "sys", "collections", "pathlib", "typing", "numpy", "typer", "wasabi", "spacy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "_frozen_importlib", "abc", "click", "click.core", "click.shell_completion", "click.types", "spacy.training.example", "typer.core", "typer.main", "typer.models", "typer.params", "types", "wasabi.printer"], "hash": "c47d354f966b32f418c0c4c5efa204685ef3eabb", "id": "spacy.cli.debug_data", "ignore_all": true, "interface_hash": "c2780de7ad4d81c051886aab4ce1bc50e58c1814", "mtime": 1749318793, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\debug_data.py", "plugin_data": null, "size": 51485, "suppressed": ["spacy.pipeline._edit_tree_internals.edit_trees", "spacy.pipeline._parser_internals.nonproj", "spacy.morphology", "spacy.vectors", "srsly"], "version_id": "1.15.0"}