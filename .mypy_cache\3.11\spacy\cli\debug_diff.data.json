{".class": "MypyFile", "_fullname": "spacy.cli.debug_diff", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Arg": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Arg", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "MarkdownRenderer": {".class": "SymbolTableNode", "cross_ref": "wasabi.markdown.<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Opt", "kind": "Gdef"}, "Optimizations": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.init_config.Optimizations", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Printer": {".class": "SymbolTableNode", "cross_ref": "wasabi.printer.Printer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_diff.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_diff.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_diff.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_diff.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_diff.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.debug_diff.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "debug_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.debug_cli", "kind": "Gdef"}, "debug_diff": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["config_path", "compare_to", "gpu", "optimize", "pretraining", "markdown"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.debug_diff.debug_diff", "name": "debug_diff", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["config_path", "compare_to", "gpu", "optimize", "pretraining", "markdown"], "arg_types": ["pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "spacy.cli.init_config.Optimizations", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_diff", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug_diff_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "compare_to", "optimize", "gpu", "pretraining", "markdown"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.debug_diff.debug_diff_cli", "name": "debug_diff_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "compare_to", "optimize", "gpu", "pretraining", "markdown"], "arg_types": ["typer.models.Context", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "spacy.cli.init_config.Optimizations", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_diff_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.debug_diff.debug_diff_cli", "name": "debug_diff_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "compare_to", "optimize", "gpu", "pretraining", "markdown"], "arg_types": ["typer.models.Context", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "spacy.cli.init_config.Optimizations", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug_diff_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "diff_strings": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.diff_strings", "kind": "Gdef"}, "init_config": {".class": "SymbolTableNode", "cross_ref": "spacy.cli.init_config.init_config", "kind": "Gdef"}, "load_config": {".class": "SymbolTableNode", "cross_ref": "spacy.util.load_config", "kind": "Gdef"}, "parse_config_overrides": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.parse_config_overrides", "kind": "Gdef"}, "show_validation_error": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.show_validation_error", "kind": "Gdef"}, "typer": {".class": "SymbolTableNode", "cross_ref": "typer", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\debug_diff.py"}