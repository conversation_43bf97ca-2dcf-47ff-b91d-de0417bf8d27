{".class": "MypyFile", "_fullname": "spacy.cli.find_threshold", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Arg": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Arg", "kind": "Gdef"}, "Corpus": {".class": "SymbolTableNode", "cross_ref": "spacy.training.corpus.Corpus", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MultiLabel_TextCategorizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.textcat_multilabel.MultiLabel_TextCategorizer", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Opt", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "TextCategorizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.textcat.TextCategorizer", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "_DEFAULTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.cli.find_threshold._DEFAULTS", "name": "_DEFAULTS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.find_threshold.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.find_threshold.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.find_threshold.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.find_threshold.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.find_threshold.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.find_threshold.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "app": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.app", "kind": "Gdef"}, "find_threshold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 5], "arg_names": ["model", "data_path", "pipe_name", "threshold_key", "scores_key", "n_trials", "use_gpu", "gold_preproc", "silent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.find_threshold.find_threshold", "name": "find_threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 5, 5, 5, 5], "arg_names": ["model", "data_path", "pipe_name", "threshold_key", "scores_key", "n_trials", "use_gpu", "gold_preproc", "silent"], "arg_types": ["builtins.str", "pathlib.Path", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_threshold", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", {".class": "Instance", "args": ["builtins.float", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_threshold_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "data_path", "pipe_name", "threshold_key", "scores_key", "n_trials", "code_path", "use_gpu", "gold_preproc", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.find_threshold.find_threshold_cli", "name": "find_threshold_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "data_path", "pipe_name", "threshold_key", "scores_key", "n_trials", "code_path", "use_gpu", "gold_preproc", "verbose"], "arg_types": ["builtins.str", "pathlib.Path", "builtins.str", "builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_threshold_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.find_threshold.find_threshold_cli", "name": "find_threshold_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "data_path", "pipe_name", "threshold_key", "scores_key", "n_trials", "code_path", "use_gpu", "gold_preproc", "verbose"], "arg_types": ["builtins.str", "pathlib.Path", "builtins.str", "builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_threshold_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "import_code": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.import_code", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "setup_gpu": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.setup_gpu", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "spacy.util", "kind": "Gdef"}, "wasabi": {".class": "SymbolTableNode", "cross_ref": "wasabi", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\find_threshold.py"}