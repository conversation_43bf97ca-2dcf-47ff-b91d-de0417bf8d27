{".class": "MypyFile", "_fullname": "spacy.cli.init_config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Arg": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Arg", "kind": "Gdef"}, "COMMAND": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.COMMAND", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "DEFAULT_CONFIG_PRETRAIN_PATH": {".class": "SymbolTableNode", "cross_ref": "spacy.language.DEFAULT_CONFIG_PRETRAIN_PATH", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "InitValues": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.cli.init_config.InitValues", "name": "InitValues", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.cli.init_config.InitValues", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.cli.init_config", "mro": ["spacy.cli.init_config.InitValues", "builtins.object"], "names": {".class": "SymbolTable", "force_overwrite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.InitValues.force_overwrite", "name": "force_overwrite", "type": "builtins.bool"}}, "gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.InitValues.gpu", "name": "gpu", "type": "builtins.bool"}}, "lang": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.InitValues.lang", "name": "lang", "type": "builtins.str"}}, "optimize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.InitValues.optimize", "name": "optimize", "type": "spacy.cli.init_config.Optimizations"}}, "pipeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.InitValues.pipeline", "name": "pipeline", "type": "spacy.util.SimpleFrozenList"}}, "pretraining": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.InitValues.pretraining", "name": "pretraining", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.cli.init_config.InitValues.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.cli.init_config.InitValues", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Opt", "kind": "Gdef"}, "Optimizations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.cli.init_config.Optimizations", "name": "Optimizations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "spacy.cli.init_config.Optimizations", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "spacy.cli.init_config", "mro": ["spacy.cli.init_config.Optimizations", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "accuracy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.Optimizations.accuracy", "name": "accuracy", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "accuracy"}, "type_ref": "builtins.str"}}}, "efficiency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.Optimizations.efficiency", "name": "efficiency", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "efficiency"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.cli.init_config.Optimizations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.cli.init_config.Optimizations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Printer": {".class": "SymbolTableNode", "cross_ref": "wasabi.printer.Printer", "kind": "Gdef"}, "RECOMMENDATIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.RECOMMENDATIONS", "name": "RECOMMENDATIONS", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.init_config.srsly", "source_any": {".class": "AnyType", "missing_import_name": "spacy.cli.init_config.srsly", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ROOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.ROOT", "name": "ROOT", "type": "pathlib.Path"}}, "RecommendationSchema": {".class": "SymbolTableNode", "cross_ref": "spacy.schemas.RecommendationSchema", "kind": "Gdef"}, "SimpleFrozenList": {".class": "SymbolTableNode", "cross_ref": "spacy.util.SimpleFrozenList", "kind": "Gdef"}, "TEMPLATE_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.cli.init_config.TEMPLATE_PATH", "name": "TEMPLATE_PATH", "type": "pathlib.Path"}}, "Template": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Template", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "diff_strings": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.diff_strings", "kind": "Gdef"}, "fill_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["output_file", "base_path", "pretraining", "diff", "silent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.init_config.fill_config", "name": "fill_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["output_file", "base_path", "pretraining", "diff", "silent"], "arg_types": ["pathlib.Path", "pathlib.Path", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fill_config", "ret_type": {".class": "TupleType", "implicit": false, "items": ["confection.Config", "confection.Config"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_spacy_transformers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.init_config.has_spacy_transformers", "name": "has_spacy_transformers", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_spacy_transformers", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "import_code": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.import_code", "kind": "Gdef"}, "init_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.init_cli", "kind": "Gdef"}, "init_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["lang", "pipeline", "optimize", "gpu", "pretraining", "silent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.init_config.init_config", "name": "init_config", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["lang", "pipeline", "optimize", "gpu", "pretraining", "silent"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_config", "ret_type": "confection.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_config_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["output_file", "lang", "pipeline", "optimize", "gpu", "pretraining", "force_overwrite"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.init_config.init_config_cli", "name": "init_config_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["output_file", "lang", "pipeline", "optimize", "gpu", "pretraining", "force_overwrite"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.str", "spacy.cli.init_config.Optimizations", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_config_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.init_config.init_config_cli", "name": "init_config_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["output_file", "lang", "pipeline", "optimize", "gpu", "pretraining", "force_overwrite"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.str", "spacy.cli.init_config.Optimizations", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_config_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init_fill_config_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["base_path", "output_file", "pretraining", "diff", "code_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.init_config.init_fill_config_cli", "name": "init_fill_config_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["base_path", "output_file", "pretraining", "diff", "code_path"], "arg_types": ["pathlib.Path", "pathlib.Path", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_fill_config_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.init_config.init_fill_config_cli", "name": "init_fill_config_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1], "arg_names": ["base_path", "output_file", "pretraining", "diff", "code_path"], "arg_types": ["pathlib.Path", "pathlib.Path", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_fill_config_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "save_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "output_file", "is_stdout", "silent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.init_config.save_config", "name": "save_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["config", "output_file", "is_stdout", "silent"], "arg_types": ["confection.Config", "pathlib.Path", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_config", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "show_validation_error": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.show_validation_error", "kind": "Gdef"}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.init_config.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.init_config.srsly", "source_any": null, "type_of_any": 3}}}, "string_to_list": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.string_to_list", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "spacy.util", "kind": "Gdef"}, "validate_config_for_pretrain": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.init_config.validate_config_for_pretrain", "name": "validate_config_for_pretrain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "msg"], "arg_types": ["confection.Config", "wasabi.printer.Printer"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_config_for_pretrain", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\init_config.py"}