{".class": "MypyFile", "_fullname": "spacy.cli.init_pipeline", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Arg": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Arg", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Opt", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_pipeline.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_pipeline.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_pipeline.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_pipeline.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_pipeline.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.init_pipeline.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_init_labels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nlp", "output_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.init_pipeline._init_labels", "name": "_init_labels", "type": null}}, "convert_vectors": {".class": "SymbolTableNode", "cross_ref": "spacy.training.initialize.convert_vectors", "kind": "Gdef"}, "import_code": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.import_code", "kind": "Gdef"}, "init_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.init_cli", "kind": "Gdef"}, "init_labels_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "output_path", "code_path", "verbose", "use_gpu"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.init_pipeline.init_labels_cli", "name": "init_labels_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "output_path", "code_path", "verbose", "use_gpu"], "arg_types": ["typer.models.Context", "pathlib.Path", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_labels_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.init_pipeline.init_labels_cli", "name": "init_labels_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "output_path", "code_path", "verbose", "use_gpu"], "arg_types": ["typer.models.Context", "pathlib.Path", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_labels_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init_nlp": {".class": "SymbolTableNode", "cross_ref": "spacy.training.initialize.init_nlp", "kind": "Gdef"}, "init_pipeline_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "output_path", "code_path", "verbose", "use_gpu"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.init_pipeline.init_pipeline_cli", "name": "init_pipeline_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "output_path", "code_path", "verbose", "use_gpu"], "arg_types": ["typer.models.Context", "pathlib.Path", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_pipeline_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.init_pipeline.init_pipeline_cli", "name": "init_pipeline_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["ctx", "config_path", "output_path", "code_path", "verbose", "use_gpu"], "arg_types": ["typer.models.Context", "pathlib.Path", "pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_pipeline_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init_vectors_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["lang", "vectors_loc", "output_dir", "prune", "truncate", "mode", "name", "verbose", "jsonl_loc", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.init_pipeline.init_vectors_cli", "name": "init_vectors_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["lang", "vectors_loc", "output_dir", "prune", "truncate", "mode", "name", "verbose", "jsonl_loc", "attr"], "arg_types": ["builtins.str", "pathlib.Path", "pathlib.Path", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_vectors_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.init_pipeline.init_vectors_cli", "name": "init_vectors_cli", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["lang", "vectors_loc", "output_dir", "prune", "truncate", "mode", "name", "verbose", "jsonl_loc", "attr"], "arg_types": ["builtins.str", "pathlib.Path", "pathlib.Path", "builtins.int", "builtins.int", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_vectors_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "msg": {".class": "SymbolTableNode", "cross_ref": "wasabi.msg", "kind": "Gdef"}, "parse_config_overrides": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.parse_config_overrides", "kind": "Gdef"}, "setup_gpu": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.setup_gpu", "kind": "Gdef"}, "show_validation_error": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.show_validation_error", "kind": "Gdef"}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.init_pipeline.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.init_pipeline.srsly", "source_any": null, "type_of_any": 3}}}, "typer": {".class": "SymbolTableNode", "cross_ref": "typer", "kind": "Gdef"}, "update_lexemes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nlp", "jsonl_loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.init_pipeline.update_lexemes", "name": "update_lexemes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["nlp", "jsonl_loc"], "arg_types": ["spacy.language.Language", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_lexemes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "util": {".class": "SymbolTableNode", "cross_ref": "spacy.util", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\init_pipeline.py"}