{".class": "MypyFile", "_fullname": "spacy.cli.profile", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Arg": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Arg", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "NAME": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.NAME", "kind": "Gdef"}, "Opt": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.Opt", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Printer": {".class": "SymbolTableNode", "cross_ref": "wasabi.printer.Printer", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.profile.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.profile.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.profile.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.profile.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.profile.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.cli.profile.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_read_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["loc", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.profile._read_inputs", "name": "_read_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["loc", "msg"], "arg_types": [{".class": "UnionType", "items": ["pathlib.Path", "builtins.str"], "uses_pep604_syntax": false}, "wasabi.printer.Printer"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_inputs", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.app", "kind": "Gdef"}, "cProfile": {".class": "SymbolTableNode", "cross_ref": "cProfile", "kind": "Gdef"}, "debug_cli": {".class": "SymbolTableNode", "cross_ref": "spacy.cli._util.debug_cli", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "load_model": {".class": "SymbolTableNode", "cross_ref": "spacy.util.load_model", "kind": "Gdef"}, "msg": {".class": "SymbolTableNode", "cross_ref": "wasabi.msg", "kind": "Gdef"}, "parse_texts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nlp", "texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.profile.parse_texts", "name": "parse_texts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["nlp", "texts"], "arg_types": ["spacy.language.Language", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_texts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "profile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["model", "inputs", "n_texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.cli.profile.profile", "name": "profile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["model", "inputs", "n_texts"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "profile_cli": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["ctx", "model", "inputs", "n_texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.cli.profile.profile_cli", "name": "profile_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["ctx", "model", "inputs", "n_texts"], "arg_types": ["typer.models.Context", "builtins.str", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.cli.profile.profile_cli", "name": "profile_cli", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["ctx", "model", "inputs", "n_texts"], "arg_types": ["typer.models.Context", "builtins.str", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_cli", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pstats": {".class": "SymbolTableNode", "cross_ref": "pstats", "kind": "Gdef"}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.profile.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.profile.srsly", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.cli.profile.tqdm", "name": "tqdm", "type": {".class": "AnyType", "missing_import_name": "spacy.cli.profile.tqdm", "source_any": null, "type_of_any": 3}}}, "typer": {".class": "SymbolTableNode", "cross_ref": "typer", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\cli\\profile.py"}