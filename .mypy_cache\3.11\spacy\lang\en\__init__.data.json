{".class": "MypyFile", "_fullname": "spacy.lang.en", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseDefaults": {".class": "SymbolTableNode", "cross_ref": "spacy.language.BaseDefaults", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "English": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["spacy.language.Language"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.lang.en.English", "name": "English", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.lang.en.English", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.lang.en", "mro": ["spacy.lang.en.English", "spacy.language.Language", "builtins.object"], "names": {".class": "SymbolTable", "Defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.English.Defaults", "name": "De<PERSON>ults", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["spacy.lang.en.<PERSON>ef<PERSON>s"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "spacy.lang.en.<PERSON>ef<PERSON>s", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lang": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.English.lang", "name": "lang", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.lang.en.English.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.lang.en.English", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EnglishDefaults": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["spacy.language.BaseDefaults"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.lang.en.<PERSON>ef<PERSON>s", "name": "EnglishDefaults", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.lang.en.<PERSON>ef<PERSON>s", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.lang.en", "mro": ["spacy.lang.en.<PERSON>ef<PERSON>s", "spacy.language.BaseDefaults", "builtins.object"], "names": {".class": "SymbolTable", "infixes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.EnglishDefaults.infixes", "name": "infixes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "lex_attr_getters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.EnglishDefaults.lex_attr_getters", "name": "lex_attr_getters", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.lang.en.lex_attrs.LIKE_NUM", "source_any": {".class": "AnyType", "missing_import_name": "spacy.lang.en.lex_attrs.LIKE_NUM", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "like_num", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "stop_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.EnglishDefaults.stop_words", "name": "stop_words", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "syntax_iterators": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.EnglishDefaults.syntax_iterators", "name": "syntax_iterators", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["doclike"], "arg_types": [{".class": "UnionType", "items": ["spacy.tokens.doc.Doc", "spacy.tokens.span.Span"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "noun_chunks", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tokenizer_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.EnglishDefaults.tokenizer_exceptions", "name": "tokenizer_exceptions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.lang.en.EnglishDefaults.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.lang.en.<PERSON>ef<PERSON>s", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EnglishLemmatizer": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.en.lemmatizer.EnglishLemmatizer", "kind": "Gdef", "module_public": false}, "LEX_ATTRS": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.en.lex_attrs.LEX_ATTRS", "kind": "Gdef", "module_public": false}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef", "module_public": false}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "STOP_WORDS": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.en.stop_words.STOP_WORDS", "kind": "Gdef", "module_public": false}, "SYNTAX_ITERATORS": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.en.syntax_iterators.SYNTAX_ITERATORS", "kind": "Gdef", "module_public": false}, "TOKENIZER_EXCEPTIONS": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.en.tokenizer_exceptions.TOKENIZER_EXCEPTIONS", "kind": "Gdef", "module_public": false}, "TOKENIZER_INFIXES": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.en.punctuation.TOKENIZER_INFIXES", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "make_lemmatizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["nlp", "model", "name", "mode", "overwrite", "scorer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.lang.en.make_lemmatizer", "name": "make_lemmatizer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["nlp", "model", "name", "mode", "overwrite", "scorer"], "arg_types": ["spacy.language.Language", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_lemmatizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.lang.en.make_lemmatizer", "name": "make_lemmatizer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\lang\\en\\__init__.py"}