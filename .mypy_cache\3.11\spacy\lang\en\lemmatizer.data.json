{".class": "MypyFile", "_fullname": "spacy.lang.en.lemmatizer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EnglishLemmatizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["spacy.pipeline.lemmatizer.Lemmatizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.lang.en.lemmatizer.EnglishLemmatizer", "name": "EnglishLemmatizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.lang.en.lemmatizer.EnglishLemmatizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.lang.en.lemmatizer", "mro": ["spacy.lang.en.lemmatizer.EnglishLemmatizer", "spacy.pipeline.lemmatizer.Lemmatizer", "spacy.pipeline.pipe.Pipe", "builtins.object"], "names": {".class": "SymbolTable", "is_base_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.lang.en.lemmatizer.EnglishLemmatizer.is_base_form", "name": "is_base_form", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token"], "arg_types": ["spacy.lang.en.lemmatizer.EnglishLemmatizer", "spacy.tokens.token.Token"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_base_form of EnglishLemmatizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.lang.en.lemmatizer.EnglishLemmatizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.lang.en.lemmatizer.EnglishLemmatizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lemmatizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.lemmatizer.Lemmatizer", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.token.Token", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.lemmatizer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.lemmatizer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.lemmatizer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.lemmatizer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.lemmatizer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.lemmatizer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\lang\\en\\lemmatizer.py"}