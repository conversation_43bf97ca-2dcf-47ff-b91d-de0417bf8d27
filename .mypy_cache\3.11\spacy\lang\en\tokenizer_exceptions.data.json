{".class": "MypyFile", "_fullname": "spacy.lang.en.tokenizer_exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BASE_EXCEPTIONS": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.tokenizer_exceptions.BASE_EXCEPTIONS", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NORM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.NORM", "name": "NORM", "type": {".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.NORM", "source_any": null, "type_of_any": 3}}}, "ORTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.ORTH", "name": "ORTH", "type": {".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.ORTH", "source_any": null, "type_of_any": 3}}}, "TOKENIZER_EXCEPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions.TOKENIZER_EXCEPTIONS", "name": "TOKENIZER_EXCEPTIONS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.tokenizer_exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.tokenizer_exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.tokenizer_exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.tokenizer_exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.tokenizer_exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.lang.en.tokenizer_exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_exc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions._exc", "name": "_exc", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_exclude": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions._exclude", "name": "_exclude", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_other_exc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions._other_exc", "name": "_other_exc", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.ORTH", "source_any": {".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.ORTH", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.data", "name": "data", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "data_apos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions.data_apos", "name": "data_apos", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "exc_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.exc_data", "name": "exc_data", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.ORTH", "source_any": {".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.ORTH", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "exc_data_apos": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions.exc_data_apos", "name": "exc_data_apos", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "exc_data_tc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions.exc_data_tc", "name": "exc_data_tc", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "h": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.h", "name": "h", "type": "builtins.int"}}, "morph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions.morph", "name": "morph", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "orth": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.orth", "name": "orth", "type": "builtins.str"}}, "period": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.period", "name": "period", "type": "builtins.str"}}, "pron": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.pron", "name": "pron", "type": "builtins.str"}}, "string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.string", "name": "string", "type": "builtins.str"}}, "update_exc": {".class": "SymbolTableNode", "cross_ref": "spacy.util.update_exc", "kind": "Gdef"}, "verb_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "spacy.lang.en.tokenizer_exceptions.verb_data", "name": "verb_data", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.ORTH", "source_any": {".class": "AnyType", "missing_import_name": "spacy.lang.en.tokenizer_exceptions.ORTH", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "verb_data_tc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions.verb_data_tc", "name": "verb_data_tc", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "word": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.lang.en.tokenizer_exceptions.word", "name": "word", "type": "builtins.str"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\lang\\en\\tokenizer_exceptions.py"}