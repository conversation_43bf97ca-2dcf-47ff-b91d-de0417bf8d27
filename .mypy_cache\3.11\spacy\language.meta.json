{"data_mtime": 1753783925, "dep_lines": [43, 44, 57, 59, 186, 36, 37, 39, 39, 39, 40, 41, 42, 45, 46, 47, 54, 56, 58, 72, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 33, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 35, 55, 71, 34], "dep_prios": [5, 5, 5, 5, 20, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10], "dependencies": ["spacy.lang.punctuation", "spacy.lang.tokenizer_exceptions", "spacy.tokens.underscore", "spacy.training.initialize", "spacy.pipeline.factories", "thinc.api", "thinc.util", "spacy.about", "spacy.ty", "spacy.util", "spacy.compat", "spacy.errors", "spacy.git_info", "spacy.lookups", "spacy.pipe_analysis", "spacy.schemas", "spacy.scorer", "spacy.tokens", "spacy.training", "spacy.vocab", "functools", "inspect", "itertools", "multiprocessing", "random", "traceback", "warnings", "contextlib", "copy", "dataclasses", "pathlib", "timeit", "typing", "numpy", "spacy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "confection", "configparser", "multiprocessing.connection", "multiprocessing.context", "multiprocessing.process", "multiprocessing.queues", "pydantic", "pydantic.v1", "pydantic.v1.main", "pydantic.v1.utils", "re", "spacy.pipeline", "spacy.strings", "spacy.tokens.doc", "spacy.tokens.span", "spacy.tokens.token", "spacy.training.example", "thinc", "thinc.backends", "thinc.backends.ops", "thinc.config", "thinc.model", "thinc.optimizers", "types", "typing_extensions"], "hash": "56c18295c0b1ffbd4f4339301e6db95733176078", "id": "spacy.language", "ignore_all": true, "interface_hash": "6fa318f91a90ca5fd4b5df05e0c0791052ab85a5", "mtime": 1749318793, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\language.py", "plugin_data": null, "size": 109214, "suppressed": ["cymem.cymem", "spacy.tokenizer", "spacy.vectors", "srsly"], "version_id": "1.15.0"}