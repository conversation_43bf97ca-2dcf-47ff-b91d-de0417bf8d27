{".class": "MypyFile", "_fullname": "spacy.ml.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArrayXd": {".class": "SymbolTableNode", "cross_ref": "thinc.types.ArrayXd", "kind": "Gdef"}, "BiLSTMEncoder": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.BiLSTMEncoder", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Candidate": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.Candidate", "kind": "Gdef"}, "CharacterEmbed": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.CharacterEmbed", "kind": "Gdef"}, "CosineDistance": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.CosineDistance", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Dropout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.dropout.Dropout", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "FeatureExtractor": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.featureextractor.FeatureExtractor", "kind": "Gdef"}, "Floats1d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats1d", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "Gelu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.gelu.Gelu", "kind": "Gdef"}, "HashEmbed": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.hashembed.HashEmbed", "kind": "Gdef"}, "ID": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.ID", "kind": "Gdef"}, "InMemoryLookupKB": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.InMemoryLookupKB", "kind": "Gdef"}, "InT": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.span_finder.InT", "kind": "Gdef"}, "Ints1d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ints1d", "kind": "Gdef"}, "Ints2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ints2d", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "KnowledgeBase": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.KnowledgeBase", "kind": "Gdef"}, "L2Distance": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.L2Distance", "kind": "Gdef"}, "LayerNorm": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.layernorm.LayerNorm", "kind": "Gdef"}, "Linear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.linear.Linear", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Logistic": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.logistic.Logistic", "kind": "Gdef"}, "Loss": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.Loss", "kind": "Gdef"}, "Maxout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.maxout.Maxout", "kind": "Gdef"}, "MaxoutWindowEncoder": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.MaxoutWindowEncoder", "kind": "Gdef"}, "Mish": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.mish.Mish", "kind": "Gdef"}, "MishWindowEncoder": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.MishWindowEncoder", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "MultiHashEmbed": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.MultiHashEmbed", "kind": "Gdef"}, "MultiSoftmax": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.multisoftmax.MultiSoftmax", "kind": "Gdef"}, "NEG_VALUE": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.NEG_VALUE", "kind": "Gdef"}, "OOV_RANK": {".class": "SymbolTableNode", "cross_ref": "spacy.util.OOV_RANK", "kind": "Gdef"}, "ORTH": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.ORTH", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OutT": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.span_finder.OutT", "kind": "Gdef"}, "ParametricAttention": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.parametricattention.ParametricAttention", "kind": "Gdef"}, "ParametricAttention_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.parametricattention_v2.ParametricAttention_v2", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PrecomputableAffine": {".class": "SymbolTableNode", "cross_ref": "spacy.ml._precomputable_affine.PrecomputableAffine", "kind": "Gdef"}, "PyTorchLSTM": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.lstm.PyTorchLSTM", "kind": "Gdef"}, "Ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ragged", "kind": "Gdef"}, "Relu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.relu.Relu", "kind": "Gdef"}, "Softmax": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax.Softmax", "kind": "Gdef"}, "Softmax_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax.Softmax_v2", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span.Span", "kind": "Gdef"}, "SparseLinear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.SparseLinear", "kind": "Gdef"}, "SparseLinear_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.SparseLinear_v2", "kind": "Gdef"}, "StaticVectors": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.staticvectors.StaticVectors", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tok2VecListener": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.tok2vec.Tok2VecListener", "kind": "Gdef"}, "TransitionModel": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.tb_framework.TransitionModel", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VectorsMode": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.VectorsMode", "kind": "Gdef"}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_Tok2Vec_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.build_Tok2Vec_model", "kind": "Gdef"}, "build_bow_text_classifier": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.build_bow_text_classifier", "kind": "Gdef"}, "build_bow_text_classifier_v3": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.build_bow_text_classifier_v3", "kind": "Gdef"}, "build_cloze_characters_multi_task_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.build_cloze_characters_multi_task_model", "kind": "Gdef"}, "build_cloze_multi_task_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.build_cloze_multi_task_model", "kind": "Gdef"}, "build_finder_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.span_finder.build_finder_model", "kind": "Gdef"}, "build_hash_embed_cnn_tok2vec": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.build_hash_embed_cnn_tok2vec", "kind": "Gdef"}, "build_linear_logistic": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.spancat.build_linear_logistic", "kind": "Gdef"}, "build_masked_language_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.build_masked_language_model", "kind": "Gdef"}, "build_mean_max_reducer": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.spancat.build_mean_max_reducer", "kind": "Gdef"}, "build_multi_task_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.build_multi_task_model", "kind": "Gdef"}, "build_nel_encoder": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.build_nel_encoder", "kind": "Gdef"}, "build_reduce_text_classifier": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.build_reduce_text_classifier", "kind": "Gdef"}, "build_simple_cnn_text_classifier": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.build_simple_cnn_text_classifier", "kind": "Gdef"}, "build_span_maker": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.build_span_maker", "kind": "Gdef"}, "build_spancat_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.spancat.build_spancat_model", "kind": "Gdef"}, "build_tagger_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tagger.build_tagger_model", "kind": "Gdef"}, "build_tb_parser_model": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.parser.build_tb_parser_model", "kind": "Gdef"}, "build_text_classifier_lowdata": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.build_text_classifier_lowdata", "kind": "Gdef"}, "build_text_classifier_v2": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.build_text_classifier_v2", "kind": "Gdef"}, "build_textcat_parametric_attention_v1": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.build_textcat_parametric_attention_v1", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "clone": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.clone.clone", "kind": "Gdef"}, "concatenate": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.concatenate.concatenate", "kind": "Gdef"}, "create_candidates": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.create_candidates", "kind": "Gdef"}, "create_candidates_batch": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.create_candidates_batch", "kind": "Gdef"}, "create_pretrain_characters": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.create_pretrain_characters", "kind": "Gdef"}, "create_pretrain_vectors": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.create_pretrain_vectors", "kind": "Gdef"}, "empty_kb": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.empty_kb", "kind": "Gdef"}, "empty_kb_for_config": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.empty_kb_for_config", "kind": "Gdef"}, "expand_window": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.expand_window.expand_window", "kind": "Gdef"}, "extract_ngrams": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.extract_ngrams.extract_ngrams", "kind": "Gdef"}, "extract_spans": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.extract_spans.extract_spans", "kind": "Gdef"}, "flattener": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.span_finder.flattener", "kind": "Gdef"}, "get_candidates": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.get_candidates", "kind": "Gdef"}, "get_candidates_batch": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.get_candidates_batch", "kind": "Gdef"}, "get_characters_loss": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.get_characters_loss", "kind": "Gdef"}, "get_tok2vec_width": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.get_tok2vec_width", "kind": "Gdef"}, "get_vectors_loss": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.multi_task.get_vectors_loss", "kind": "Gdef"}, "glorot_uniform_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.glorot_uniform_init", "kind": "Gdef"}, "init_chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.init", "kind": "Gdef"}, "init_ensemble_textcat": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.init_ensemble_textcat", "kind": "Gdef"}, "intify_attr": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.intify_attr", "kind": "Gdef"}, "list2array": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2array.list2array", "kind": "Gdef"}, "list2ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2ragged.list2ragged", "kind": "Gdef"}, "load_kb": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.load_kb", "kind": "Gdef"}, "noop": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.noop.noop", "kind": "Gdef"}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "ragged2list": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.ragged2list.ragged2list", "kind": "Gdef"}, "reduce_first": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_first.reduce_first", "kind": "Gdef"}, "reduce_last": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_last.reduce_last", "kind": "Gdef"}, "reduce_max": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_max.reduce_max", "kind": "Gdef"}, "reduce_mean": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_mean.reduce_mean", "kind": "Gdef"}, "reduce_sum": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_sum.reduce_sum", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "residual": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.residual.residual", "kind": "Gdef"}, "resizable": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.resizable.resizable", "kind": "Gdef"}, "resize_and_set_ref": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.textcat.resize_and_set_ref", "kind": "Gdef"}, "resize_linear_weighted": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.resizable.resize_linear_weighted", "kind": "Gdef"}, "resize_model": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.resizable.resize_model", "kind": "Gdef"}, "resize_output": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.parser.resize_output", "kind": "Gdef"}, "softmax_activation": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax_activation.softmax_activation", "kind": "Gdef"}, "span_maker_forward": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.entity_linker.span_maker_forward", "kind": "Gdef"}, "to_categorical": {".class": "SymbolTableNode", "cross_ref": "thinc.util.to_categorical", "kind": "Gdef"}, "tok2vec_listener_v1": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.tok2vec_listener_v1", "kind": "Gdef"}, "tuplify": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.tuplify.tuplify", "kind": "Gdef"}, "use_ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.use_ops", "kind": "Gdef"}, "with_array": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_array.with_array", "kind": "Gdef"}, "with_cpu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_cpu.with_cpu", "kind": "Gdef"}, "with_getitem": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_getitem.with_getitem", "kind": "Gdef"}, "with_padded": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_padded.with_padded", "kind": "Gdef"}, "zero_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.zero_init", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\models\\__init__.py"}