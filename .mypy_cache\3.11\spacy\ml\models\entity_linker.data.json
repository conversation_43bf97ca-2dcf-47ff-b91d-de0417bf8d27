{".class": "MypyFile", "_fullname": "spacy.ml.models.entity_linker", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Candidate": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.Candidate", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "InMemoryLookupKB": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.InMemoryLookupKB", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "KnowledgeBase": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.KnowledgeBase", "kind": "Gdef"}, "Linear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.linear.Linear", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Maxout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.maxout.Maxout", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ragged", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span.Span", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.entity_linker.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.entity_linker.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.entity_linker.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.entity_linker.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.entity_linker.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.entity_linker.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_nel_encoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["tok2vec", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.build_nel_encoder", "name": "build_nel_encoder", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["tok2vec", "nO"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_nel_encoder", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_span_maker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["n_sents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.build_span_maker", "name": "build_span_maker", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["n_sents"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_span_maker", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "create_candidates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.create_candidates", "name": "create_candidates", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_candidates", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "spacy.tokens.span.Span"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.kb.Candidate", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_candidates_batch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.create_candidates_batch", "name": "create_candidates_batch", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_candidates_batch", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.kb.Candidate", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "empty_kb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["entity_vector_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.empty_kb", "name": "empty_kb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["entity_vector_length"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty_kb", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.vocab.Vocab"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "empty_kb_for_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.empty_kb_for_config", "name": "empty_kb_for_config", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "empty_kb_for_config", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["spacy.vocab.Vocab", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_spans": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.extract_spans.extract_spans", "kind": "Gdef"}, "get_candidates": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.get_candidates", "kind": "Gdef"}, "get_candidates_batch": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.get_candidates_batch", "kind": "Gdef"}, "list2ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2ragged.list2ragged", "kind": "Gdef"}, "load_kb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["kb_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.load_kb", "name": "load_kb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["kb_path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_kb", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.vocab.Vocab"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reduce_mean": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_mean.reduce_mean", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "residual": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.residual.residual", "kind": "Gdef"}, "span_maker_forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "docs", "is_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.entity_linker.span_maker_forward", "name": "span_maker_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "docs", "is_train"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "span_maker_forward", "ret_type": {".class": "TupleType", "implicit": false, "items": ["thinc.types.Ragged", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tuplify": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.tuplify.tuplify", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\models\\entity_linker.py"}