{".class": "MypyFile", "_fullname": "spacy.ml.models.multi_task", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CosineDistance": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.CosineDistance", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "ID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.ml.models.multi_task.ID", "name": "ID", "type": {".class": "AnyType", "missing_import_name": "spacy.ml.models.multi_task.ID", "source_any": null, "type_of_any": 3}}}, "Ints1d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ints1d", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "L2Distance": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.L2Distance", "kind": "Gdef"}, "LayerNorm": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.layernorm.LayerNorm", "kind": "Gdef"}, "Linear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.linear.Linear", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Loss": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.Loss", "kind": "Gdef"}, "Maxout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.maxout.Maxout", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "MultiSoftmax": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.multisoftmax.MultiSoftmax", "kind": "Gdef"}, "OOV_RANK": {".class": "SymbolTableNode", "cross_ref": "spacy.util.OOV_RANK", "kind": "Gdef"}, "ORTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.ml.models.multi_task.ORTH", "name": "ORTH", "type": {".class": "AnyType", "missing_import_name": "spacy.ml.models.multi_task.ORTH", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Softmax": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax.Softmax", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "VectorsMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.ml.models.multi_task.VectorsMode", "name": "VectorsMode", "type": {".class": "AnyType", "missing_import_name": "spacy.ml.models.multi_task.VectorsMode", "source_any": null, "type_of_any": 3}}}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "_RandomWords": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.ml.models.multi_task._RandomWords", "name": "_RandomWords", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task._RandomWords", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.ml.models.multi_task", "mro": ["spacy.ml.models.multi_task._RandomWords", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vocab"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task._RandomWords.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "vocab"], "arg_types": ["spacy.ml.models.multi_task._RandomWords", "spacy.vocab.Vocab"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _RandomWords", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.ml.models.multi_task._RandomWords._cache", "name": "_cache", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "next": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task._RandomWords.next", "name": "next", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.ml.models.multi_task._RandomWords"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next of _RandomWords", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "probs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.ml.models.multi_task._RandomWords.probs", "name": "probs", "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "words": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.ml.models.multi_task._RandomWords.words", "name": "words", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.ml.models.multi_task._RandomWords.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.ml.models.multi_task._RandomWords", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.multi_task.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.multi_task.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.multi_task.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.multi_task.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.multi_task.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.multi_task.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_apply_mask": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["docs", "random_words", "mask_prob"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task._apply_mask", "name": "_apply_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["docs", "random_words", "mask_prob"], "arg_types": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "spacy.ml.models.multi_task._RandomWords", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_mask", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replace_word": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["word", "random_words", "mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task._replace_word", "name": "_replace_word", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["word", "random_words", "mask"], "arg_types": ["builtins.str", "spacy.ml.models.multi_task._RandomWords", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace_word", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_cloze_characters_multi_task_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["vocab", "tok2vec", "maxout_pieces", "hidden_size", "nr_char"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.build_cloze_characters_multi_task_model", "name": "build_cloze_characters_multi_task_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["vocab", "tok2vec", "maxout_pieces", "hidden_size", "nr_char"], "arg_types": ["spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_cloze_characters_multi_task_model", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_cloze_multi_task_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["vocab", "tok2vec", "maxout_pieces", "hidden_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.build_cloze_multi_task_model", "name": "build_cloze_multi_task_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["vocab", "tok2vec", "maxout_pieces", "hidden_size"], "arg_types": ["spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_cloze_multi_task_model", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_masked_language_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["vocab", "wrapped_model", "mask_prob"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.build_masked_language_model", "name": "build_masked_language_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["vocab", "wrapped_model", "mask_prob"], "arg_types": ["spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_masked_language_model", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_multi_task_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["tok2vec", "maxout_pieces", "token_vector_width", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.build_multi_task_model", "name": "build_multi_task_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["tok2vec", "maxout_pieces", "token_vector_width", "nO"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_multi_task_model", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "create_pretrain_characters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["maxout_pieces", "hidden_size", "n_characters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.create_pretrain_characters", "name": "create_pretrain_characters", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["maxout_pieces", "hidden_size", "n_characters"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_pretrain_characters", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_pretrain_vectors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["maxout_pieces", "hidden_size", "loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.create_pretrain_vectors", "name": "create_pretrain_vectors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["maxout_pieces", "hidden_size", "loss"], "arg_types": ["builtins.int", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_pretrain_vectors", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_characters_loss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["ops", "docs", "prediction", "nr_char"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.get_characters_loss", "name": "get_characters_loss", "type": null}}, "get_vectors_loss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["ops", "docs", "prediction", "distance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.multi_task.get_vectors_loss", "name": "get_vectors_loss", "type": null}}, "list2array": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2array.list2array", "kind": "Gdef"}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "to_categorical": {".class": "SymbolTableNode", "cross_ref": "thinc.util.to_categorical", "kind": "Gdef"}, "zero_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.zero_init", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\models\\multi_task.py"}