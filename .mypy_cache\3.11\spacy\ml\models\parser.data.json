{".class": "MypyFile", "_fullname": "spacy.ml.models.parser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "Linear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.linear.Linear", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PrecomputableAffine": {".class": "SymbolTableNode", "cross_ref": "spacy.ml._precomputable_affine.PrecomputableAffine", "kind": "Gdef"}, "TransitionModel": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.tb_framework.TransitionModel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.parser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.parser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.parser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.parser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.parser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.parser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_define_lower": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["nO", "nF", "nI", "nP"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.parser._define_lower", "name": "_define_lower", "type": null}}, "_define_upper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nO", "nI"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.parser._define_upper", "name": "_define_upper", "type": null}}, "_resize_lower": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "new_nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.parser._resize_lower", "name": "_resize_lower", "type": null}}, "_resize_upper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "new_nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.parser._resize_upper", "name": "_resize_upper", "type": null}}, "build_tb_parser_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["tok2vec", "state_type", "extra_state_tokens", "hidden_width", "maxout_pieces", "use_upper", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.parser.build_tb_parser_model", "name": "build_tb_parser_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["tok2vec", "state_type", "extra_state_tokens", "hidden_width", "maxout_pieces", "use_upper", "nO"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "parser"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ner"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_tb_parser_model", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "list2array": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2array.list2array", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "resize_output": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "new_nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.parser.resize_output", "name": "resize_output", "type": null}}, "use_ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.use_ops", "kind": "Gdef"}, "zero_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.zero_init", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\models\\parser.py"}