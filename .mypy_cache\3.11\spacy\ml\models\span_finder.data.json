{".class": "MypyFile", "_fullname": "spacy.ml.models.span_finder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Floats1d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats1d", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "InT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "spacy.ml.models.span_finder.InT", "line": 9, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "OutT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "spacy.ml.models.span_finder.OutT", "line": 10, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "thinc.types.Floats2d"}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.span_finder.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.span_finder.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.span_finder.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.span_finder.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.span_finder.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.span_finder.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_finder_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tok2vec", "scorer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.span_finder.build_finder_model", "name": "build_finder_model", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tok2vec", "scorer"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "spacy.ml.models.span_finder.InT"}, {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "Instance", "args": ["thinc.types.Floats2d", "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_finder_model", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "spacy.ml.models.span_finder.InT"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "flattener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.span_finder.flattener", "name": "flattener", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flattener", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "with_array": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_array.with_array", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\models\\span_finder.py"}