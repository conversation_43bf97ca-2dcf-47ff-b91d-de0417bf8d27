{".class": "MypyFile", "_fullname": "spacy.ml.models.spancat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "Linear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.linear.Linear", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Logistic": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.logistic.Logistic", "kind": "Gdef"}, "Maxout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.maxout.Maxout", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "Ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ragged", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.spancat.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.spancat.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.spancat.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.spancat.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.spancat.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.spancat.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "build_linear_logistic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["nO", "nI"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.spancat.build_linear_logistic", "name": "build_linear_logistic", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["nO", "nI"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_linear_logistic", "ret_type": {".class": "Instance", "args": ["thinc.types.Floats2d", "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_mean_max_reducer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["hidden_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.spancat.build_mean_max_reducer", "name": "build_mean_max_reducer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["hidden_size"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_mean_max_reducer", "ret_type": {".class": "Instance", "args": ["thinc.types.Ragged", "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_spancat_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["tok2vec", "reducer", "scorer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.spancat.build_spancat_model", "name": "build_spancat_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["tok2vec", "reducer", "scorer"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "Instance", "args": ["thinc.types.Ragged", "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "Instance", "args": ["thinc.types.Floats2d", "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_spancat_model", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Ragged"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "concatenate": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.concatenate.concatenate", "kind": "Gdef"}, "extract_spans": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.extract_spans.extract_spans", "kind": "Gdef"}, "glorot_uniform_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.glorot_uniform_init", "kind": "Gdef"}, "list2ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2ragged.list2ragged", "kind": "Gdef"}, "reduce_first": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_first.reduce_first", "kind": "Gdef"}, "reduce_last": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_last.reduce_last", "kind": "Gdef"}, "reduce_max": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_max.reduce_max", "kind": "Gdef"}, "reduce_mean": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_mean.reduce_mean", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "with_getitem": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_getitem.with_getitem", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\models\\spancat.py"}