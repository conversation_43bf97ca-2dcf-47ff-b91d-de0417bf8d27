{".class": "MypyFile", "_fullname": "spacy.ml.models.textcat", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ArrayXd": {".class": "SymbolTableNode", "cross_ref": "thinc.types.ArrayXd", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Dropout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.dropout.Dropout", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "Gelu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.gelu.Gelu", "kind": "Gdef"}, "LayerNorm": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.layernorm.LayerNorm", "kind": "Gdef"}, "Linear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.linear.Linear", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Logistic": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.logistic.Logistic", "kind": "Gdef"}, "Maxout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.maxout.Maxout", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "NEG_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.ml.models.textcat.NEG_VALUE", "name": "NEG_VALUE", "type": "builtins.int"}}, "ORTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.ml.models.textcat.ORTH", "name": "ORTH", "type": {".class": "AnyType", "missing_import_name": "spacy.ml.models.textcat.ORTH", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParametricAttention": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.parametricattention.ParametricAttention", "kind": "Gdef"}, "ParametricAttention_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.parametricattention_v2.ParametricAttention_v2", "kind": "Gdef"}, "Relu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.relu.Relu", "kind": "Gdef"}, "Softmax": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax.Softmax", "kind": "Gdef"}, "SparseLinear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.SparseLinear", "kind": "Gdef"}, "SparseLinear_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.SparseLinear_v2", "kind": "Gdef"}, "StaticVectors": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.staticvectors.StaticVectors", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.textcat.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.textcat.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.textcat.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.textcat.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.textcat.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.models.textcat.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_build_bow_text_classifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["exclusive_classes", "ngram_size", "no_output_layer", "sparse_linear", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat._build_bow_text_classifier", "name": "_build_bow_text_classifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["exclusive_classes", "ngram_size", "no_output_layer", "sparse_linear", "nO"], "arg_types": ["builtins.bool", "builtins.int", "builtins.bool", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "thinc.types.ArrayXd"}, {".class": "TypeAliasType", "args": [], "type_ref": "thinc.types.ArrayXd"}, {".class": "TypeAliasType", "args": [], "type_ref": "thinc.types.ArrayXd"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "thinc.types.ArrayXd"}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_bow_text_classifier", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_parametric_attention_with_residual_nonlinear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 5], "arg_names": ["tok2vec", "nonlinear_layer", "key_transform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat._build_parametric_attention_with_residual_nonlinear", "name": "_build_parametric_attention_with_residual_nonlinear", "type": {".class": "CallableType", "arg_kinds": [3, 3, 5], "arg_names": ["tok2vec", "nonlinear_layer", "key_transform"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "Instance", "args": ["thinc.types.Floats2d", "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["thinc.types.Floats2d", "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_parametric_attention_with_residual_nonlinear", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_parametric_attention_with_residual_nonlinear": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "X", "Y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat._init_parametric_attention_with_residual_nonlinear", "name": "_init_parametric_attention_with_residual_nonlinear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "X", "Y"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_parametric_attention_with_residual_nonlinear", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_bow_text_classifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["exclusive_classes", "ngram_size", "no_output_layer", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.build_bow_text_classifier", "name": "build_bow_text_classifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["exclusive_classes", "ngram_size", "no_output_layer", "nO"], "arg_types": ["builtins.bool", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_bow_text_classifier", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_bow_text_classifier_v3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["exclusive_classes", "ngram_size", "no_output_layer", "length", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.build_bow_text_classifier_v3", "name": "build_bow_text_classifier_v3", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["exclusive_classes", "ngram_size", "no_output_layer", "length", "nO"], "arg_types": ["builtins.bool", "builtins.int", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_bow_text_classifier_v3", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_reduce_text_classifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["tok2vec", "exclusive_classes", "use_reduce_first", "use_reduce_last", "use_reduce_max", "use_reduce_mean", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.build_reduce_text_classifier", "name": "build_reduce_text_classifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["tok2vec", "exclusive_classes", "use_reduce_first", "use_reduce_last", "use_reduce_max", "use_reduce_mean", "nO"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_reduce_text_classifier", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_simple_cnn_text_classifier": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tok2vec", "exclusive_classes", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.build_simple_cnn_text_classifier", "name": "build_simple_cnn_text_classifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["tok2vec", "exclusive_classes", "nO"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_simple_cnn_text_classifier", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_text_classifier_lowdata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["width", "dropout", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.build_text_classifier_lowdata", "name": "build_text_classifier_lowdata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["width", "dropout", "nO"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_text_classifier_lowdata", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_text_classifier_v2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tok2vec", "linear_model", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.build_text_classifier_v2", "name": "build_text_classifier_v2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["tok2vec", "linear_model", "nO"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_text_classifier_v2", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_textcat_parametric_attention_v1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tok2vec", "exclusive_classes", "nO"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.build_textcat_parametric_attention_v1", "name": "build_textcat_parametric_attention_v1", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["tok2vec", "exclusive_classes", "nO"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build_textcat_parametric_attention_v1", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "clone": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.clone.clone", "kind": "Gdef"}, "concatenate": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.concatenate.concatenate", "kind": "Gdef"}, "extract_ngrams": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.extract_ngrams.extract_ngrams", "kind": "Gdef"}, "get_tok2vec_width": {".class": "SymbolTableNode", "cross_ref": "spacy.ml.models.tok2vec.get_tok2vec_width", "kind": "Gdef"}, "init_chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.init", "kind": "Gdef"}, "init_ensemble_textcat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "X", "Y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.init_ensemble_textcat", "name": "init_ensemble_textcat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "X", "Y"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_ensemble_textcat", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list2ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2ragged.list2ragged", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "reduce_first": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_first.reduce_first", "kind": "Gdef"}, "reduce_last": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_last.reduce_last", "kind": "Gdef"}, "reduce_max": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_max.reduce_max", "kind": "Gdef"}, "reduce_mean": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_mean.reduce_mean", "kind": "Gdef"}, "reduce_sum": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_sum.reduce_sum", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "residual": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.residual.residual", "kind": "Gdef"}, "resizable": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.resizable.resizable", "kind": "Gdef"}, "resize_and_set_ref": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "new_nO", "resizable_layer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.models.textcat.resize_and_set_ref", "name": "resize_and_set_ref", "type": null}}, "resize_linear_weighted": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.resizable.resize_linear_weighted", "kind": "Gdef"}, "resize_model": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.resizable.resize_model", "kind": "Gdef"}, "softmax_activation": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax_activation.softmax_activation", "kind": "Gdef"}, "with_cpu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_cpu.with_cpu", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\models\\textcat.py"}