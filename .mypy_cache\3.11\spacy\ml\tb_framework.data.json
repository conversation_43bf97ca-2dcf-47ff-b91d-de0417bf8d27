{".class": "MypyFile", "_fullname": "spacy.ml.tb_framework", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "ParserStepModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.ml.tb_framework.ParserStepModel", "name": "ParserStepModel", "type": {".class": "AnyType", "missing_import_name": "spacy.ml.tb_framework.ParserStepModel", "source_any": null, "type_of_any": 3}}}, "TransitionModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["tok2vec", "lower", "upper", "resize_output", "dropout", "unseen_classes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.tb_framework.TransitionModel", "name": "TransitionModel", "type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.tb_framework.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.tb_framework.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.tb_framework.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.tb_framework.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.tb_framework.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.ml.tb_framework.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "X", "is_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.tb_framework.forward", "name": "forward", "type": null}}, "init": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["model", "X", "Y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.ml.tb_framework.init", "name": "init", "type": null}}, "noop": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.noop.noop", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\ml\\tb_framework.py"}