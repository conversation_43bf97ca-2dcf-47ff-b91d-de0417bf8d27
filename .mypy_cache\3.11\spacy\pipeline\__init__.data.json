{".class": "MypyFile", "_fullname": "spacy.pipeline", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AttributeRuler": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.attributeruler.AttributeRuler", "kind": "Gdef"}, "DependencyParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.DependencyParser", "name": "Dependency<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.DependencyParser", "source_any": null, "type_of_any": 3}}}, "EditTreeLemmatizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", "kind": "Gdef"}, "EntityLinker": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.entity_linker.EntityLinker", "kind": "Gdef"}, "EntityRecognizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.EntityRecognizer", "name": "EntityRecognizer", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.EntityRecognizer", "source_any": null, "type_of_any": 3}}}, "EntityRuler": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.entityruler.EntityRuler", "kind": "Gdef"}, "Lemmatizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.lemmatizer.Lemmatizer", "kind": "Gdef"}, "Morphologizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.Morphologizer", "name": "Morphologizer", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.Morphologizer", "source_any": null, "type_of_any": 3}}}, "MultiLabel_TextCategorizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.textcat_multilabel.MultiLabel_TextCategorizer", "kind": "Gdef"}, "Pipe": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.pipe.Pipe", "kind": "Gdef"}, "SentenceRecognizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.SentenceRecognizer", "name": "SentenceRecognizer", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.SentenceRecognizer", "source_any": null, "type_of_any": 3}}}, "Sentencizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.Sentencizer", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.Sentencizer", "source_any": null, "type_of_any": 3}}}, "SpanCategorizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.spancat.SpanCategorizer", "kind": "Gdef"}, "SpanFinder": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.span_finder.SpanFinder", "kind": "Gdef"}, "SpanRuler": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.span_ruler.SpanRuler", "kind": "Gdef"}, "Tagger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.Tagger", "name": "Tagger", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.Tagger", "source_any": null, "type_of_any": 3}}}, "TextCategorizer": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.textcat.TextCategorizer", "kind": "Gdef"}, "Tok2Vec": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.tok2vec.Tok2Vec", "kind": "Gdef"}, "TrainablePipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.TrainablePipe", "name": "TrainablePipe", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.TrainablePipe", "source_any": null, "type_of_any": 3}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "merge_entities": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.functions.merge_entities", "kind": "Gdef"}, "merge_noun_chunks": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.functions.merge_noun_chunks", "kind": "Gdef"}, "merge_subtokens": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.functions.merge_subtokens", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\__init__.py"}