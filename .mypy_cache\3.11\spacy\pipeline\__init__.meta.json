{"data_mtime": 1753783926, "dep_lines": [1, 3, 4, 5, 6, 7, 10, 13, 14, 15, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 8, 9, 11, 12, 16, 20], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["spacy.pipeline.attributeruler", "spacy.pipeline.edit_tree_lemmatizer", "spacy.pipeline.entity_linker", "spacy.pipeline.entityruler", "spacy.pipeline.functions", "spacy.pipeline.lemmatizer", "spacy.pipeline.pipe", "spacy.pipeline.span_finder", "spacy.pipeline.span_ruler", "spacy.pipeline.spancat", "spacy.pipeline.textcat", "spacy.pipeline.textcat_multilabel", "spacy.pipeline.tok2vec", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "typing"], "hash": "cd78c5686edcefa7714e6040bdd4bd2a55f4c91f", "id": "spacy.pipeline", "ignore_all": true, "interface_hash": "35f46331d71729c2a84edc09b810c4e4c775bdb1", "mtime": 1749318794, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\__init__.py", "plugin_data": null, "size": 1324, "suppressed": ["spacy.pipeline.dep_parser", "spacy.pipeline.morphologizer", "spacy.pipeline.ner", "spacy.pipeline.sentencizer", "spacy.pipeline.senter", "spacy.pipeline.tagger", "spacy.pipeline.trainable_pipe"], "version_id": "1.15.0"}