{".class": "MypyFile", "_fullname": "spacy.pipeline.edit_tree_lemmatizer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "DEFAULT_EDIT_TREE_LEMMATIZER_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.DEFAULT_EDIT_TREE_LEMMATIZER_MODEL", "name": "DEFAULT_EDIT_TREE_LEMMATIZER_MODEL", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "EditTreeLemmatizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", "name": "EditTreeLemmatizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.pipeline.edit_tree_lemmatizer", "mro": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "vocab", "model", "name", "backoff", "min_tree_freq", "overwrite", "top_k", "scorer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 5, 5, 5, 5, 5], "arg_names": ["self", "vocab", "model", "name", "backoff", "min_tree_freq", "overwrite", "top_k", "scorer"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", "spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EditTreeLemmatizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer._add_labels", "name": "_add_labels", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "labels"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_labels of EditTreeLemmatizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_labels_from_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "get_examples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer._labels_from_data", "name": "_labels_from_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "get_examples"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_labels_from_data of EditTreeLemmatizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pair2label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "form", "lemma", "add_label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer._pair2label", "name": "_pair2label", "type": null}}, "_scores2guesses_top_k_equals_1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "scores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer._scores2guesses_top_k_equals_1", "name": "_scores2guesses_top_k_equals_1", "type": null}}, "_scores2guesses_top_k_greater_1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "scores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer._scores2guesses_top_k_greater_1", "name": "_scores2guesses_top_k_greater_1", "type": null}}, "_scores2guesses_top_k_guardrail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "scores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer._scores2guesses_top_k_guardrail", "name": "_scores2guesses_top_k_guardrail", "type": null}}, "backoff": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.backoff", "name": "backoff", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cfg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.cfg", "name": "cfg", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "from_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "bytes_data", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.from_bytes", "name": "from_bytes", "type": null}}, "from_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.from_disk", "name": "from_disk", "type": null}}, "get_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "examples", "scores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.get_loss", "name": "get_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "examples", "scores"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_loss of EditTreeLemmatizer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "Instance", "args": ["thinc.types.Floats2d"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hide_labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.hide_labels", "name": "hide_labels", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hide_labels of EditTreeLemmatizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.hide_labels", "name": "hide_labels", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hide_labels of EditTreeLemmatizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "get_examples", "nlp", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "get_examples", "nlp", "labels"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["spacy.language.Language", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of EditTreeLemmatizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "label_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.label_data", "name": "label_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "label_data of EditTreeLemmatizer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.label_data", "name": "label_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "label_data of EditTreeLemmatizer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "labels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.labels", "name": "labels", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "labels of EditTreeLemmatizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.labels", "name": "labels", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "labels of EditTreeLemmatizer", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "min_tree_freq": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.min_tree_freq", "name": "min_tree_freq", "type": "builtins.int"}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.model", "name": "model", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.name", "name": "name", "type": "builtins.str"}}, "numpy_ops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.numpy_ops", "name": "numpy_ops", "type": {".class": "AnyType", "missing_import_name": "thinc.backends.NumpyOps", "source_any": {".class": "AnyType", "missing_import_name": "thinc.backends.NumpyOps", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "overwrite": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.overwrite", "name": "overwrite", "type": "builtins.bool"}}, "predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "docs"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of EditTreeLemmatizer", "ret_type": {".class": "Instance", "args": ["thinc.types.Ints2d"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scorer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.scorer", "name": "scorer", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "batch_tree_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.set_annotations", "name": "set_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "batch_tree_ids"], "arg_types": ["spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_annotations of EditTreeLemmatizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.to_bytes", "name": "to_bytes", "type": null}}, "to_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.to_disk", "name": "to_disk", "type": null}}, "top_k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.top_k", "name": "top_k", "type": "builtins.int"}}, "tree2label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.tree2label", "name": "tree2label", "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "trees": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.trees", "name": "trees", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.edit_tree_lemmatizer.EditTrees", "source_any": {".class": "AnyType", "missing_import_name": "spacy.pipeline.edit_tree_lemmatizer.EditTrees", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "vocab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.vocab", "name": "vocab", "type": "spacy.vocab.Vocab"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.pipeline.edit_tree_lemmatizer.EditTreeLemmatizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EditTrees": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.EditTrees", "name": "EditTrees", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.edit_tree_lemmatizer.EditTrees", "source_any": null, "type_of_any": 3}}}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Example": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.Example", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "Ints2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ints2d", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "NumpyOps": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.NumpyOps", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SequenceCategoricalCrossentropy": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.SequenceCategoricalCrossentropy", "kind": "Gdef"}, "TOP_K_GUARDRAIL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.TOP_K_GUARDRAIL", "name": "TOP_K_GUARDRAIL", "type": "builtins.int"}}, "TrainablePipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.TrainablePipe", "name": "TrainablePipe", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.edit_tree_lemmatizer.TrainablePipe", "source_any": null, "type_of_any": 3}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.edit_tree_lemmatizer.__getattr__", "name": "__getattr__", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "default_model_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.default_model_config", "name": "default_model_config", "type": "builtins.str"}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "lemmatizer_score": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.lemmatizer.lemmatizer_score", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.edit_tree_lemmatizer.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.edit_tree_lemmatizer.srsly", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "spacy.util", "kind": "Gdef"}, "validate_edit_tree": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline._edit_tree_internals.schemas.validate_edit_tree", "kind": "Gdef"}, "validate_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_examples", "kind": "Gdef"}, "validate_get_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_get_examples", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\edit_tree_lemmatizer.py"}