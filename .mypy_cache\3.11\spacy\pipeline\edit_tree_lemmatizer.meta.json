{"data_mtime": 1753783925, "dep_lines": [19, 20, 9, 10, 12, 13, 14, 15, 16, 17, 1, 2, 3, 4, 5, 7, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18, 21, 8], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["spacy.pipeline._edit_tree_internals.schemas", "spacy.pipeline.lemmatizer", "thinc.api", "thinc.types", "spacy.util", "spacy.errors", "spacy.language", "spacy.tokens", "spacy.training", "spacy.vocab", "importlib", "sys", "collections", "itertools", "typing", "numpy", "spacy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "_frozen_importlib", "abc", "confection", "configparser", "spacy.strings", "spacy.tokens.doc", "spacy.tokens.token", "spacy.training.example", "thinc", "thinc.model"], "hash": "65461268d9d7089511f20904fa97940e4ee66e4e", "id": "spacy.pipeline.edit_tree_lemmatizer", "ignore_all": true, "interface_hash": "35f042536ca3704c4394519006765fb77d004dc2", "mtime": 1749318794, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\edit_tree_lemmatizer.py", "plugin_data": null, "size": 15192, "suppressed": ["spacy.pipeline._edit_tree_internals.edit_trees", "spacy.pipeline.trainable_pipe", "srsly"], "version_id": "1.15.0"}