{".class": "MypyFile", "_fullname": "spacy.pipeline.entity_linker", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BACKWARD_OVERWRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.entity_linker.BACKWARD_OVERWRITE", "name": "BACKWARD_OVERWRITE", "type": "builtins.bool"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Candidate": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.Candidate", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "CosineDistance": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.CosineDistance", "kind": "Gdef"}, "DEFAULT_NEL_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.entity_linker.DEFAULT_NEL_MODEL", "name": "DEFAULT_NEL_MODEL", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "EntityLinker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.pipeline.entity_linker.EntityLinker", "name": "<PERSON><PERSON>ty<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "spacy.pipeline.entity_linker.EntityLinker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.pipeline.entity_linker", "mro": ["spacy.pipeline.entity_linker.EntityLinker", "builtins.object"], "names": {".class": "SymbolTable", "NIL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.NIL", "name": "NIL", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 3, 3, 5], "arg_names": ["self", "vocab", "model", "name", "labels_discard", "n_sents", "incl_prior", "incl_context", "entity_vector_length", "get_candidates", "get_candidates_batch", "generate_empty_kb", "overwrite", "scorer", "use_gold_ents", "candidates_batch_size", "threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 3, 3, 3, 3, 3, 3, 3, 3, 5, 5, 3, 3, 5], "arg_names": ["self", "vocab", "model", "name", "labels_discard", "n_sents", "incl_prior", "incl_context", "entity_vector_length", "get_candidates", "get_candidates_batch", "generate_empty_kb", "overwrite", "scorer", "use_gold_ents", "candidates_batch_size", "threshold"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", "spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.int", "builtins.bool", "builtins.bool", "builtins.int", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "spacy.tokens.span.Span"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.kb.Candidate", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.kb.Candidate", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["spacy.vocab.Vocab", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EntityLinker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ensure_ents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "examples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker._ensure_ents", "name": "_ensure_ents", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "examples"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_ents of EntityLinker", "ret_type": {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.add_label", "name": "add_label", "type": null}}, "batch_has_learnable_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "examples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.batch_has_learnable_example", "name": "batch_has_learnable_example", "type": null}}, "candidates_batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.candidates_batch_size", "name": "candidates_batch_size", "type": "builtins.int"}}, "cfg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.cfg", "name": "cfg", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "distance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.distance", "name": "distance", "type": "thinc.loss.CosineDistance"}}, "from_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "bytes_data", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.from_bytes", "name": "from_bytes", "type": null}}, "from_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "path", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.from_disk", "name": "from_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "path", "exclude"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_disk of EntityLinker", "ret_type": "spacy.pipeline.entity_linker.EntityLinker", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_candidates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.get_candidates", "name": "get_candidates", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "spacy.tokens.span.Span"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.kb.Candidate", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_candidates_batch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.get_candidates_batch", "name": "get_candidates_batch", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "spacy.kb.Candidate", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "examples", "sentence_encodings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.get_loss", "name": "get_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "examples", "sentence_encodings"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "thinc.types.Floats2d"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_loss of EntityLinker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "incl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.incl_context", "name": "incl_context", "type": "builtins.bool"}}, "incl_prior": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.incl_prior", "name": "incl_prior", "type": "builtins.bool"}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "get_examples", "nlp", "kb_loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "get_examples", "nlp", "kb_loader"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["spacy.language.Language", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.vocab.Vocab"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of EntityLinker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.kb", "name": "kb", "type": {".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}}}, "labels_discard": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.labels_discard", "name": "labels_discard", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.model", "name": "model", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}}}, "n_sents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.n_sents", "name": "n_sents", "type": "builtins.int"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.name", "name": "name", "type": "builtins.str"}}, "predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "docs"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rehearse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 4], "arg_names": ["self", "examples", "sgd", "losses", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.rehearse", "name": "rehearse", "type": null}}, "scorer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.scorer", "name": "scorer", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["examples", "kwargs"], "arg_types": [{".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "kb_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.set_annotations", "name": "set_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "kb_ids"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_annotations of EntityLinker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_kb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kb_loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.set_kb", "name": "set_kb", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kb_loader"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.vocab.Vocab"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": "spacy.kb.KnowledgeBase", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_kb of EntityLinker", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.threshold", "name": "threshold", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.to_bytes", "name": "to_bytes", "type": null}}, "to_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "path", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.to_disk", "name": "to_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "path", "exclude"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_disk of EntityLinker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "examples", "drop", "sgd", "losses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "examples", "drop", "sgd", "losses"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker", {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.float", {".class": "UnionType", "items": ["thinc.optimizers.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of EntityLinker", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_gold_ents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.use_gold_ents", "name": "use_gold_ents", "type": "builtins.bool"}}, "validate_kb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.EntityLinker.validate_kb", "name": "validate_kb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.entity_linker.EntityLinker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_kb of EntityLinker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vocab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.entity_linker.EntityLinker.vocab", "name": "vocab", "type": "spacy.vocab.Vocab"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.pipeline.entity_linker.EntityLinker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.pipeline.entity_linker.EntityLinker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EntityLinker_v1": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.legacy.entity_linker.EntityLinker_v1", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Example": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.Example", "kind": "Gdef"}, "Floats2d": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Floats2d", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "KnowledgeBase": {".class": "SymbolTableNode", "cross_ref": "spacy.kb.KnowledgeBase", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.Optimizer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Scorer": {".class": "SymbolTableNode", "cross_ref": "spacy.scorer.Scorer", "kind": "Gdef"}, "SimpleFrozenList": {".class": "SymbolTableNode", "cross_ref": "spacy.util.SimpleFrozenList", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span.Span", "kind": "Gdef"}, "TrainablePipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.entity_linker.TrainablePipe", "name": "TrainablePipe", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.entity_linker.TrainablePipe", "source_any": null, "type_of_any": 3}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.entity_linker.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.entity_linker.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.entity_linker.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.__getattr__", "name": "__getattr__", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.entity_linker.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.entity_linker.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.entity_linker.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "default_model_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.entity_linker.default_model_config", "name": "default_model_config", "type": "builtins.str"}}, "deserialize_config": {".class": "SymbolTableNode", "cross_ref": "spacy.pipeline.pipe.deserialize_config", "kind": "Gdef"}, "entity_linker_score": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["examples", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.entity_linker_score", "name": "entity_linker_score", "type": null}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "make_entity_linker_scorer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.entity_linker.make_entity_linker_scorer", "name": "make_entity_linker_scorer", "type": null}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "set_dropout_rate": {".class": "SymbolTableNode", "cross_ref": "thinc.model.set_dropout_rate", "kind": "Gdef"}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.entity_linker.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.entity_linker.srsly", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "spacy.util", "kind": "Gdef"}, "validate_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_examples", "kind": "Gdef"}, "validate_get_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_get_examples", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\entity_linker.py"}