{"data_mtime": 1753783926, "dep_lines": [9, 11, 17, 18, 19, 20, 26, 27, 33, 41, 42, 46, 47, 48, 3, 4, 6, 7, 49, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 10, 21, 22, 23, 24, 25, 40], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["spacy.pipeline.attributeruler", "spacy.pipeline.edit_tree_lemmatizer", "spacy.pipeline.entity_linker", "spacy.pipeline.entityruler", "spacy.pipeline.functions", "spacy.pipeline.lemmatizer", "spacy.pipeline.span_finder", "spacy.pipeline.span_ruler", "spacy.pipeline.spancat", "spacy.pipeline.textcat", "spacy.pipeline.textcat_multilabel", "spacy.pipeline.tok2vec", "spacy.tokens.doc", "spacy.tokens.span", "thinc.api", "thinc.types", "spacy.kb", "spacy.language", "spacy.vocab", "typing", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "spacy.tokens", "thinc", "thinc.model"], "hash": "bd6e7ee77e670a559399b54cd7a4df59f4a6cb32", "id": "spacy.pipeline.factories", "ignore_all": true, "interface_hash": "158448c1b1c51aa56461e2c47a3a154c6a79478d", "mtime": 1749318794, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\factories.py", "plugin_data": null, "size": 27749, "suppressed": ["spacy.pipeline._parser_internals.transition_system", "spacy.pipeline.dep_parser", "spacy.pipeline.morphologizer", "spacy.pipeline.multitask", "spacy.pipeline.ner", "spacy.pipeline.sentencizer", "spacy.pipeline.senter", "spacy.pipeline.tagger"], "version_id": "1.15.0"}