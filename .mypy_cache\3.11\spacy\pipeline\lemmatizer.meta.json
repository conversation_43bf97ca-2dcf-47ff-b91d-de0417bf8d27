{"data_mtime": 1753783925, "dep_lines": [18, 7, 9, 10, 11, 12, 13, 14, 15, 17, 1, 2, 3, 4, 5, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["spacy.pipeline.pipe", "thinc.api", "spacy.util", "spacy.errors", "spacy.language", "spacy.lookups", "spacy.scorer", "spacy.tokens", "spacy.training", "spacy.vocab", "importlib", "sys", "warnings", "pathlib", "typing", "spacy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "spacy.tokens.doc", "spacy.tokens.token", "spacy.training.example", "thinc", "thinc.model", "types"], "hash": "21fb16028c7f26c58e6e02d6a04c9c1cd959fcdd", "id": "spacy.pipeline.lemmatizer", "ignore_all": true, "interface_hash": "ea335914cb0c789897e5b9e2e48e726d924e5053", "mtime": 1749318794, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\lemmatizer.py", "plugin_data": null, "size": 12223, "suppressed": [], "version_id": "1.15.0"}