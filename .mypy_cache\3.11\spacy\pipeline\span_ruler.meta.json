{"data_mtime": 1753783925, "dep_lines": [31, 22, 23, 24, 25, 27, 28, 29, 1, 2, 3, 4, 5, 6, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 20], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["spacy.pipeline.pipe", "spacy.util", "spacy.errors", "spacy.language", "spacy.matcher", "spacy.scorer", "spacy.tokens", "spacy.training", "importlib", "sys", "warnings", "functools", "pathlib", "typing", "spacy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "_typeshed", "abc", "spacy.matcher.matcher", "spacy.matcher.phrasematcher", "spacy.strings", "spacy.tokens.doc", "spacy.tokens.span", "spacy.training.example", "spacy.vocab"], "hash": "1fe62a50b959f0ea5f382edb2e053e8e1c1fe0a2", "id": "spacy.pipeline.span_ruler", "ignore_all": true, "interface_hash": "c284e2972f855f0b3914f97910a78b63e7186889", "mtime": 1749318794, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\span_ruler.py", "plugin_data": null, "size": 19501, "suppressed": ["spacy.matcher.le<PERSON><PERSON>tein", "srsly"], "version_id": "1.15.0"}