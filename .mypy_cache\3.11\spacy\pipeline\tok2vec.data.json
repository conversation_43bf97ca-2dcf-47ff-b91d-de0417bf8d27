{".class": "MypyFile", "_fullname": "spacy.pipeline.tok2vec", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "DEFAULT_TOK2VEC_MODEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.tok2vec.DEFAULT_TOK2VEC_MODEL", "name": "DEFAULT_TOK2VEC_MODEL", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Example": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.Example", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.Optimizer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tok2Vec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.pipeline.tok2vec.Tok2Vec", "name": "Tok2Vec", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.pipeline.tok2vec", "mro": ["spacy.pipeline.tok2vec.Tok2Vec", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "vocab", "model", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "vocab", "model", "name"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", "spacy.vocab.Vocab", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Tok2Vec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "label"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.add_label", "name": "add_label", "type": null}}, "add_listener": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "listener", "component_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.add_listener", "name": "add_listener", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "listener", "component_name"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", "spacy.pipeline.tok2vec.Tok2VecListener", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_listener of Tok2Vec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cfg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.cfg", "name": "cfg", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "find_listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "component"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.find_listeners", "name": "find_listeners", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "component"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_listeners of Tok2Vec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "examples", "scores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.get_loss", "name": "get_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "examples", "scores"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_loss of Tok2Vec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "get_examples", "nlp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.initialize", "name": "initialize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "get_examples", "nlp"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["spacy.language.Language", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize of Tok2Vec", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "listener_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.listener_map", "name": "listener_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["spacy.pipeline.tok2vec.Tok2VecListener"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "listeners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.listeners", "name": "listeners", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listeners of Tok2Vec", "ret_type": {".class": "Instance", "args": ["spacy.pipeline.tok2vec.Tok2VecListener"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.listeners", "name": "listeners", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listeners of Tok2Vec", "ret_type": {".class": "Instance", "args": ["spacy.pipeline.tok2vec.Tok2VecListener"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "listening_components": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.listening_components", "name": "listening_components", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listening_components of Tok2Vec", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.listening_components", "name": "listening_components", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listening_components of Tok2Vec", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.model", "name": "model", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.name", "name": "name", "type": "builtins.str"}}, "predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.predict", "name": "predict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "docs"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "predict of Tok2Vec", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_listener": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "listener", "component_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.remove_listener", "name": "remove_listener", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "listener", "component_name"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", "spacy.pipeline.tok2vec.Tok2VecListener", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_listener of Tok2Vec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "tokvecses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.set_annotations", "name": "set_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "docs", "tokvecses"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_annotations of Tok2Vec", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "examples", "drop", "sgd", "losses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "examples", "drop", "sgd", "losses"], "arg_types": ["spacy.pipeline.tok2vec.Tok2Vec", {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.float", {".class": "UnionType", "items": ["thinc.optimizers.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of Tok2Vec", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vocab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2Vec.vocab", "name": "vocab", "type": "spacy.vocab.Vocab"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.pipeline.tok2vec.Tok2Vec.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.pipeline.tok2vec.Tok2Vec", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tok2VecListener": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.pipeline.tok2vec.Tok2VecListener", "name": "Tok2VecListener", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.pipeline.tok2vec", "mro": ["spacy.pipeline.tok2vec.Tok2VecListener", "thinc.model.Model", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "upstream_name", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "upstream_name", "width"], "arg_types": ["spacy.pipeline.tok2vec.Tok2VecListener", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Tok2VecListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_backprop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener._backprop", "name": "_backprop", "type": {".class": "NoneType"}}}, "_batch_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener._batch_id", "name": "_batch_id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_outputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener._outputs", "name": "_outputs", "type": {".class": "NoneType"}}}, "get_batch_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.get_batch_id", "name": "get_batch_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "inputs"], "arg_types": [{".class": "TypeType", "item": "spacy.pipeline.tok2vec.Tok2VecListener"}, {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_batch_id of Tok2VecListener", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.get_batch_id", "name": "get_batch_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "inputs"], "arg_types": [{".class": "TypeType", "item": "spacy.pipeline.tok2vec.Tok2VecListener"}, {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_batch_id of Tok2VecListener", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.name", "name": "name", "type": "builtins.str"}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "batch_id", "outputs", "backprop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "batch_id", "outputs", "backprop"], "arg_types": ["spacy.pipeline.tok2vec.Tok2VecListener", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "receive of Tok2VecListener", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upstream_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.upstream_name", "name": "upstream_name", "type": "builtins.str"}}, "verify_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.verify_inputs", "name": "verify_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["spacy.pipeline.tok2vec.Tok2VecListener", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_inputs of Tok2VecListener", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.pipeline.tok2vec.Tok2VecListener.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.pipeline.tok2vec.Tok2VecListener", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrainablePipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.pipeline.tok2vec.TrainablePipe", "name": "TrainablePipe", "type": {".class": "AnyType", "missing_import_name": "spacy.pipeline.tok2vec.TrainablePipe", "source_any": null, "type_of_any": 3}}}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.tok2vec.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.tok2vec.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.tok2vec.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.__getattr__", "name": "__getattr__", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.tok2vec.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.tok2vec.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.pipeline.tok2vec.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_empty_backprop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["dX"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec._empty_backprop", "name": "_empty_backprop", "type": null}}, "default_model_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.pipeline.tok2vec.default_model_config", "name": "default_model_config", "type": "builtins.str"}}, "forward": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "inputs", "is_train"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.pipeline.tok2vec.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "inputs", "is_train"], "arg_types": ["spacy.pipeline.tok2vec.Tok2VecListener", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "set_dropout_rate": {".class": "SymbolTableNode", "cross_ref": "thinc.model.set_dropout_rate", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "validate_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_examples", "kind": "Gdef"}, "validate_get_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_get_examples", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\pipeline\\tok2vec.py"}