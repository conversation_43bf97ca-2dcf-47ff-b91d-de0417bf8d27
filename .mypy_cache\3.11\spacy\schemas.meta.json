{"data_mtime": 1753783925, "dep_lines": [32, 46, 20, 47, 48, 51, 52, 53, 57, 58, 59, 1, 2, 3, 4, 5, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 50], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["pydantic.v1.main", "pydantic.main", "pydantic.v1", "thinc.api", "thinc.config", "spacy.compat", "spacy.lookups", "spacy.util", "spacy.language", "spacy.training", "spacy.vocab", "inspect", "re", "collections", "enum", "typing", "pydantic", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "numpy", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "confection", "functools", "pydantic._internal", "pydantic._internal._decorators_v1", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.deprecated", "pydantic.deprecated.class_validators", "pydantic.fields", "pydantic.types", "pydantic.v1.class_validators", "pydantic.v1.dataclasses", "pydantic.v1.error_wrappers", "pydantic.v1.fields", "pydantic.v1.types", "pydantic.v1.utils", "pydantic_core", "pydantic_core._pydantic_core", "spacy.training.example", "thinc", "thinc.model", "thinc.optimizers", "types", "typing_extensions"], "hash": "c4fc11c0999ceee569858fd906500d8155e52461", "id": "spacy.schemas", "ignore_all": true, "interface_hash": "94ef1d8a623b58e410fee0b1353b7a9b1b26dacc", "mtime": 1749318793, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\schemas.py", "plugin_data": null, "size": 22237, "suppressed": ["spacy.attrs"], "version_id": "1.15.0"}