{".class": "MypyFile", "_fullname": "spacy.tokens._dict_proxies", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span.Span", "kind": "Gdef"}, "SpanGroup": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span_group.SpanGroup", "kind": "Gdef"}, "SpanGroups": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "collections.UserDict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.tokens._dict_proxies.SpanGroups", "name": "SpanGroups", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "spacy.tokens._dict_proxies", "mro": ["spacy.tokens._dict_proxies.SpanGroups", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_EMPTY_BYTES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.tokens._dict_proxies.SpanGroups._EMPTY_BYTES", "name": "_EMPTY_BYTES", "type": {".class": "AnyType", "missing_import_name": "spacy.tokens._dict_proxies.srsly", "source_any": {".class": "AnyType", "missing_import_name": "spacy.tokens._dict_proxies.srsly", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "doc", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "doc", "items"], "arg_types": ["spacy.tokens._dict_proxies.SpanGroups", "spacy.tokens.doc.Doc", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "spacy.tokens.span_group.SpanGroup"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SpanGroups", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["spacy.tokens._dict_proxies.SpanGroups", "builtins.str", {".class": "UnionType", "items": ["spacy.tokens.span_group.SpanGroup", {".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SpanGroups", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ensure_doc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups._ensure_doc", "name": "_ensure_doc", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.tokens._dict_proxies.SpanGroups"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_doc of SpanGroups", "ret_type": "spacy.tokens.doc.Doc", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_span_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "spans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups._make_span_group", "name": "_make_span_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "spans"], "arg_types": ["spacy.tokens._dict_proxies.SpanGroups", "builtins.str", {".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_span_group of SpanGroups", "ret_type": "spacy.tokens.span_group.SpanGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "doc"], "arg_types": ["spacy.tokens._dict_proxies.SpanGroups", {".class": "UnionType", "items": ["spacy.tokens.doc.Doc", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of SpanGroups", "ret_type": "spacy.tokens._dict_proxies.SpanGroups", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "doc_ref": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.tokens._dict_proxies.SpanGroups.doc_ref", "name": "doc_ref", "type": {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "weakref.ReferenceType"}}}, "from_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bytes_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups.from_bytes", "name": "from_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bytes_data"], "arg_types": ["spacy.tokens._dict_proxies.SpanGroups", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_bytes of SpanGroups", "ret_type": "spacy.tokens._dict_proxies.SpanGroups", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setdefault": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups.setdefault", "name": "set<PERSON><PERSON>ult", "type": null}}, "to_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._dict_proxies.SpanGroups.to_bytes", "name": "to_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.tokens._dict_proxies.SpanGroups"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_bytes of SpanGroups", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.tokens._dict_proxies.SpanGroups.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.tokens._dict_proxies.SpanGroups", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UserDict": {".class": "SymbolTableNode", "cross_ref": "collections.UserDict", "kind": "Gdef"}, "Warnings": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Warnings", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._dict_proxies.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._dict_proxies.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._dict_proxies.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._dict_proxies.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._dict_proxies.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._dict_proxies.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.tokens._dict_proxies.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.tokens._dict_proxies.srsly", "source_any": null, "type_of_any": 3}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\tokens\\_dict_proxies.py"}