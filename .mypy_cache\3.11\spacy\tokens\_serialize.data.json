{".class": "MypyFile", "_fullname": "spacy.tokens._serialize", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALL_ATTRS": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.DOCBIN_ALL_ATTRS", "kind": "Gdef", "module_public": false}, "Binder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "spacy.tokens._serialize.Binder", "line": 305, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "spacy.tokens._serialize.DocBin"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef", "module_public": false}, "DocBin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.tokens._serialize.DocBin", "name": "Doc<PERSON>in", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.tokens._serialize", "mro": ["spacy.tokens._serialize.DocBin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "attrs", "store_user_data", "docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "attrs", "store_user_data", "docs"], "arg_types": ["spacy.tokens._serialize.DocBin", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool", {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DocBin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.tokens._serialize.DocBin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of DocBin", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "doc"], "arg_types": ["spacy.tokens._serialize.DocBin", "spacy.tokens.doc.Doc"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of DocBin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.attrs", "name": "attrs", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "cats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.cats", "name": "cats", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.flags", "name": "flags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "from_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bytes_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.from_bytes", "name": "from_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bytes_data"], "arg_types": ["spacy.tokens._serialize.DocBin", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_bytes of DocBin", "ret_type": "spacy.tokens._serialize.DocBin", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.from_disk", "name": "from_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["spacy.tokens._serialize.DocBin", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_disk of DocBin", "ret_type": "spacy.tokens._serialize.DocBin", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vocab"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "spacy.tokens._serialize.DocBin.get_docs", "name": "get_docs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "vocab"], "arg_types": ["spacy.tokens._serialize.DocBin", "spacy.vocab.Vocab"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_docs of DocBin", "ret_type": {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.merge", "name": "merge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["spacy.tokens._serialize.DocBin", "spacy.tokens._serialize.DocBin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge of DocBin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "spaces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.spaces", "name": "spaces", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "span_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.span_groups", "name": "span_groups", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "store_user_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.store_user_data", "name": "store_user_data", "type": "builtins.bool"}}, "strings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.strings", "name": "strings", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "to_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.to_bytes", "name": "to_bytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["spacy.tokens._serialize.DocBin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_bytes of DocBin", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.DocBin.to_disk", "name": "to_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["spacy.tokens._serialize.DocBin", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_disk of DocBin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.tokens", "name": "tokens", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "user_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.user_data", "name": "user_data", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.tokens._serialize.DocBin.version", "name": "version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.tokens._serialize.DocBin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.tokens._serialize.DocBin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef", "module_public": false}, "IDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.IDS", "name": "IDS", "type": {".class": "AnyType", "missing_import_name": "spacy.tokens._serialize.IDS", "source_any": null, "type_of_any": 3}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "NumpyOps": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.NumpyOps", "kind": "Gdef", "module_public": false}, "ORTH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.ORTH", "name": "ORTH", "type": {".class": "AnyType", "missing_import_name": "spacy.tokens._serialize.ORTH", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "SPACY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.SPACY", "name": "SPACY", "type": {".class": "AnyType", "missing_import_name": "spacy.tokens._serialize.SPACY", "source_any": null, "type_of_any": 3}}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "SimpleFrozenList": {".class": "SymbolTableNode", "cross_ref": "spacy.util.SimpleFrozenList", "kind": "Gdef", "module_public": false}, "SpanGroups": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens._dict_proxies.SpanGroups", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.tokens._serialize.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._serialize.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._serialize.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._serialize.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._serialize.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._serialize.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.tokens._serialize.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy_reg": {".class": "SymbolTableNode", "cross_ref": "spacy.compat.copy_reg", "kind": "Gdef", "module_public": false}, "ensure_path": {".class": "SymbolTableNode", "cross_ref": "spacy.util.ensure_path", "kind": "Gdef", "module_public": false}, "intify_attr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.intify_attr", "name": "intify_attr", "type": {".class": "AnyType", "missing_import_name": "spacy.tokens._serialize.intify_attr", "source_any": null, "type_of_any": 3}}}, "merge_bins": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["bins"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.merge_bins", "name": "merge_bins", "type": null}}, "ndarray": {".class": "SymbolTableNode", "cross_ref": "numpy.n<PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "pickle_bin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["doc_bin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.pickle_bin", "name": "pickle_bin", "type": null}}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.tokens._serialize.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.tokens._serialize.srsly", "source_any": null, "type_of_any": 3}}}, "unpickle_bin": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["byte_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.tokens._serialize.unpickle_bin", "name": "unpickle_bin", "type": null}}, "zlib": {".class": "SymbolTableNode", "cross_ref": "zlib", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\tokens\\_serialize.py"}