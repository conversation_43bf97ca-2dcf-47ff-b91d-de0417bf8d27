{".class": "MypyFile", "_fullname": "spacy.training", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alignment": {".class": "SymbolTableNode", "cross_ref": "spacy.training.alignment.Alignment", "kind": "Gdef"}, "Corpus": {".class": "SymbolTableNode", "cross_ref": "spacy.training.corpus.Corpus", "kind": "Gdef"}, "Example": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.Example", "kind": "Gdef"}, "JsonlCorpus": {".class": "SymbolTableNode", "cross_ref": "spacy.training.corpus.JsonlCorpus", "kind": "Gdef"}, "PlainTextCorpus": {".class": "SymbolTableNode", "cross_ref": "spacy.training.corpus.PlainTextCorpus", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.training.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "biluo_tags_to_offsets": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.biluo_tags_to_offsets", "kind": "Gdef"}, "biluo_tags_to_spans": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.biluo_tags_to_spans", "kind": "Gdef"}, "biluo_to_iob": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.biluo_to_iob", "kind": "Gdef"}, "console_logger": {".class": "SymbolTableNode", "cross_ref": "spacy.training.loggers.console_logger", "kind": "Gdef", "module_public": false}, "create_copy_from_base_model": {".class": "SymbolTableNode", "cross_ref": "spacy.training.callbacks.create_copy_from_base_model", "kind": "Gdef"}, "docs_to_json": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.docs_to_json", "name": "docs_to_json", "type": {".class": "AnyType", "missing_import_name": "spacy.training.docs_to_json", "source_any": null, "type_of_any": 3}}}, "dont_augment": {".class": "SymbolTableNode", "cross_ref": "spacy.training.augment.dont_augment", "kind": "Gdef"}, "iob_to_biluo": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.iob_to_biluo", "kind": "Gdef"}, "minibatch_by_padded_size": {".class": "SymbolTableNode", "cross_ref": "spacy.training.batchers.minibatch_by_padded_size", "kind": "Gdef"}, "minibatch_by_words": {".class": "SymbolTableNode", "cross_ref": "spacy.training.batchers.minibatch_by_words", "kind": "Gdef"}, "offsets_to_biluo_tags": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.offsets_to_biluo_tags", "kind": "Gdef"}, "orth_variants_augmenter": {".class": "SymbolTableNode", "cross_ref": "spacy.training.augment.orth_variants_augmenter", "kind": "Gdef"}, "read_json_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.read_json_file", "name": "read_json_file", "type": {".class": "AnyType", "missing_import_name": "spacy.training.read_json_file", "source_any": null, "type_of_any": 3}}}, "remove_bilu_prefix": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.remove_bilu_prefix", "kind": "Gdef"}, "split_bilu_label": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.split_bilu_label", "kind": "Gdef"}, "tags_to_entities": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.tags_to_entities", "kind": "Gdef"}, "validate_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_examples", "kind": "Gdef"}, "validate_get_examples": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.validate_get_examples", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\training\\__init__.py"}