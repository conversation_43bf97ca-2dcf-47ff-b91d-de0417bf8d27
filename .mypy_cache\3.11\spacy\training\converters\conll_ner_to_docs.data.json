{".class": "MypyFile", "_fullname": "spacy.training.converters.conll_ner_to_docs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Printer": {".class": "SymbolTableNode", "cross_ref": "wasabi.printer.Printer", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span.Span", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conll_ner_to_docs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conll_ner_to_docs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conll_ner_to_docs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conll_ner_to_docs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conll_ner_to_docs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conll_ner_to_docs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "conll_ner_to_docs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["input_data", "n_sents", "seg_sents", "model", "no_print", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conll_ner_to_docs.conll_ner_to_docs", "name": "conll_ner_to_docs", "type": null}}, "get_lang_class": {".class": "SymbolTableNode", "cross_ref": "spacy.util.get_lang_class", "kind": "Gdef"}, "iob_to_biluo": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.iob_to_biluo", "kind": "Gdef"}, "load_model": {".class": "SymbolTableNode", "cross_ref": "spacy.util.load_model", "kind": "Gdef"}, "n_sents_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["msg", "n_sents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conll_ner_to_docs.n_sents_info", "name": "n_sents_info", "type": null}}, "segment_docs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["input_data", "n_sents", "doc_delimiter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conll_ner_to_docs.segment_docs", "name": "segment_docs", "type": null}}, "segment_sents_and_docs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["doc", "n_sents", "doc_delimiter", "model", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conll_ner_to_docs.segment_sents_and_docs", "name": "segment_sents_and_docs", "type": null}}, "tags_to_entities": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.tags_to_entities", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\training\\converters\\conll_ner_to_docs.py"}