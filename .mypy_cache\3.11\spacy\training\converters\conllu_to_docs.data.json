{".class": "MypyFile", "_fullname": "spacy.training.converters.conllu_to_docs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "Printer": {".class": "SymbolTableNode", "cross_ref": "wasabi.printer.Printer", "kind": "Gdef"}, "Span": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span.Span", "kind": "Gdef"}, "Token": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.token.Token", "kind": "Gdef"}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conllu_to_docs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conllu_to_docs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conllu_to_docs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conllu_to_docs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conllu_to_docs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.conllu_to_docs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "biluo_tags_to_spans": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.biluo_tags_to_spans", "kind": "Gdef"}, "conllu_sentence_to_doc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["vocab", "lines", "ner_tag_pattern", "merge_subtokens", "append_morphology", "ner_map", "set_ents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conllu_to_docs.conllu_sentence_to_doc", "name": "conllu_sentence_to_doc", "type": null}}, "conllu_to_docs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["input_data", "n_sents", "append_morphology", "ner_map", "merge_subtokens", "no_print", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conllu_to_docs.conllu_to_docs", "name": "conllu_to_docs", "type": null}}, "get_entities": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["lines", "tag_pattern", "ner_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conllu_to_docs.get_entities", "name": "get_entities", "type": null}}, "has_ner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["input_data", "ner_tag_pattern"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conllu_to_docs.has_ner", "name": "has_ner", "type": null}}, "iob_to_biluo": {".class": "SymbolTableNode", "cross_ref": "spacy.training.iob_utils.iob_to_biluo", "kind": "Gdef"}, "merge_conllu_subtokens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["lines", "doc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conllu_to_docs.merge_conllu_subtokens", "name": "merge_conllu_subtokens", "type": null}}, "n_sents_info": {".class": "SymbolTableNode", "cross_ref": "spacy.training.converters.conll_ner_to_docs.n_sents_info", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "read_conllx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["input_data", "append_morphology", "merge_subtokens", "ner_tag_pattern", "ner_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.conllu_to_docs.read_conllx", "name": "read_conllx", "type": null}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\training\\converters\\conllu_to_docs.py"}