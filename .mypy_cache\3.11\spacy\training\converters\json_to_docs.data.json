{".class": "MypyFile", "_fullname": "spacy.training.converters.json_to_docs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MultiLanguage": {".class": "SymbolTableNode", "cross_ref": "spacy.lang.xx.MultiLanguage", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.json_to_docs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.json_to_docs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.json_to_docs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.json_to_docs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.json_to_docs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.converters.json_to_docs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_fix_legacy_dict_data": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example._fix_legacy_dict_data", "kind": "Gdef"}, "_parse_example_dict_data": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example._parse_example_dict_data", "kind": "Gdef"}, "annotations_to_doc": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.annotations_to_doc", "kind": "Gdef"}, "json_iterate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.converters.json_to_docs.json_iterate", "name": "json_iterate", "type": {".class": "AnyType", "missing_import_name": "spacy.training.converters.json_to_docs.json_iterate", "source_any": null, "type_of_any": 3}}}, "json_to_annotations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.converters.json_to_docs.json_to_annotations", "name": "json_to_annotations", "type": {".class": "AnyType", "missing_import_name": "spacy.training.converters.json_to_docs.json_to_annotations", "source_any": null, "type_of_any": 3}}}, "json_to_docs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["input_data", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.converters.json_to_docs.json_to_docs", "name": "json_to_docs", "type": null}}, "load_model": {".class": "SymbolTableNode", "cross_ref": "spacy.util.load_model", "kind": "Gdef"}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.converters.json_to_docs.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.training.converters.json_to_docs.srsly", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\training\\converters\\json_to_docs.py"}