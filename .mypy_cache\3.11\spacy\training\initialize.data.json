{".class": "MypyFile", "_fullname": "spacy.training.initialize", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "ConfigSchemaTraining": {".class": "SymbolTableNode", "cross_ref": "spacy.schemas.ConfigSchemaTraining", "kind": "Gdef"}, "ConfigValidationError": {".class": "SymbolTableNode", "cross_ref": "confection.ConfigValidationError", "kind": "Gdef"}, "DEFAULT_OOV_PROB": {".class": "SymbolTableNode", "cross_ref": "spacy.util.DEFAULT_OOV_PROB", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "Lookups": {".class": "SymbolTableNode", "cross_ref": "spacy.lookups.Lookups", "kind": "Gdef"}, "OOV_RANK": {".class": "SymbolTableNode", "cross_ref": "spacy.util.OOV_RANK", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Vectors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.initialize.Vectors", "name": "Vectors", "type": {".class": "AnyType", "missing_import_name": "spacy.training.initialize.Vectors", "source_any": null, "type_of_any": 3}}}, "VectorsMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.initialize.VectorsMode", "name": "VectorsMode", "type": {".class": "AnyType", "missing_import_name": "spacy.training.initialize.VectorsMode", "source_any": null, "type_of_any": 3}}}, "Warnings": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Warnings", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.initialize.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.initialize.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.initialize.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.initialize.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.initialize.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.initialize.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "convert_vectors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 5, 5, 5], "arg_names": ["nlp", "vectors_loc", "truncate", "prune", "name", "mode", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.convert_vectors", "name": "convert_vectors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 5, 5, 5], "arg_names": ["nlp", "vectors_loc", "truncate", "prune", "name", "mode", "attr"], "arg_types": ["spacy.language.Language", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert_vectors", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ensure_path": {".class": "SymbolTableNode", "cross_ref": "spacy.util.ensure_path", "kind": "Gdef"}, "ensure_shape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["vectors_loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.ensure_shape", "name": "ensure_shape", "type": null}}, "fix_random_seed": {".class": "SymbolTableNode", "cross_ref": "thinc.util.fix_random_seed", "kind": "Gdef"}, "get_sourced_components": {".class": "SymbolTableNode", "cross_ref": "spacy.util.get_sourced_components", "kind": "Gdef"}, "get_tok2vec_ref": {".class": "SymbolTableNode", "cross_ref": "spacy.training.pretrain.get_tok2vec_ref", "kind": "Gdef"}, "gzip": {".class": "SymbolTableNode", "cross_ref": "gzip", "kind": "Gdef"}, "init_nlp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["config", "use_gpu"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.init_nlp", "name": "init_nlp", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["config", "use_gpu"], "arg_types": ["confection.Config", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_nlp", "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_tok2vec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["nlp", "pretrain_config", "init_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.init_tok2vec", "name": "init_tok2vec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["nlp", "pretrain_config", "init_config"], "arg_types": ["spacy.language.Language", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_tok2vec", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_vocab": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["nlp", "data", "lookups", "vectors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.init_vocab", "name": "init_vocab", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["nlp", "data", "lookups", "vectors"], "arg_types": ["spacy.language.Language", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["spacy.lookups.Lookups", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_vocab", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "islice": {".class": "SymbolTableNode", "cross_ref": "itertools.islice", "kind": "Gdef"}, "load_model": {".class": "SymbolTableNode", "cross_ref": "spacy.util.load_model", "kind": "Gdef"}, "load_model_from_config": {".class": "SymbolTableNode", "cross_ref": "spacy.util.load_model_from_config", "kind": "Gdef"}, "load_vectors_into_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["nlp", "name", "add_strings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.load_vectors_into_model", "name": "load_vectors_into_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["nlp", "name", "add_strings"], "arg_types": ["spacy.language.Language", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_vectors_into_model", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "cross_ref": "spacy.util.logger", "kind": "Gdef"}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "open_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.open_file", "name": "open_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["loc"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open_file", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_vectors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["vectors_loc", "truncate_vectors", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.initialize.read_vectors", "name": "read_vectors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["vectors_loc", "truncate_vectors", "mode"], "arg_types": ["pathlib.Path", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_vectors", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "resolve_dot_names": {".class": "SymbolTableNode", "cross_ref": "spacy.util.resolve_dot_names", "kind": "Gdef"}, "set_gpu_allocator": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.set_gpu_allocator", "kind": "Gdef"}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.initialize.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.training.initialize.srsly", "source_any": null, "type_of_any": 3}}}, "tarfile": {".class": "SymbolTableNode", "cross_ref": "tarfile", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.training.initialize.tqdm", "name": "tqdm", "type": {".class": "AnyType", "missing_import_name": "spacy.training.initialize.tqdm", "source_any": null, "type_of_any": 3}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "zipfile": {".class": "SymbolTableNode", "cross_ref": "zipfile", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\training\\initialize.py"}