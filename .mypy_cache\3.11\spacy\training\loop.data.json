{".class": "MypyFile", "_fullname": "spacy.training.loop", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "ConfigSchemaTraining": {".class": "SymbolTableNode", "cross_ref": "spacy.schemas.ConfigSchemaTraining", "kind": "Gdef"}, "DIR_MODEL_BEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.training.loop.DIR_MODEL_BEST", "name": "DIR_MODEL_BEST", "type": "builtins.str"}}, "DIR_MODEL_LAST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.training.loop.DIR_MODEL_LAST", "name": "DIR_MODEL_LAST", "type": "builtins.str"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Example": {".class": "SymbolTableNode", "cross_ref": "spacy.training.example.Example", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.Optimizer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Printer": {".class": "SymbolTableNode", "cross_ref": "wasabi.printer.Printer", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.loop.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.loop.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.loop.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.loop.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.loop.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.training.loop.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "clean_output_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.clean_output_dir", "name": "clean_output_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clean_output_dir", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constant": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.constant", "kind": "Gdef"}, "create_before_to_disk_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.create_before_to_disk_callback", "name": "create_before_to_disk_callback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["callback"], "arg_types": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.language.Language"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_before_to_disk_callback", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.language.Language"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_evaluation_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["nlp", "dev_corpus", "weights"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.create_evaluation_callback", "name": "create_evaluation_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["nlp", "dev_corpus", "weights"], "arg_types": ["spacy.language.Language", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_evaluation_callback", "ret_type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_train_batches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["nlp", "corpus", "batcher", "max_epochs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.create_train_batches", "name": "create_train_batches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["nlp", "corpus", "batcher", "max_epochs"], "arg_types": ["spacy.language.Language", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["spacy.language.Language"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["spacy.training.example.Example"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_train_batches", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fix_random_seed": {".class": "SymbolTableNode", "cross_ref": "thinc.util.fix_random_seed", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "spacy.util.logger", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "spacy.util.registry", "kind": "Gdef"}, "resolve_dot_names": {".class": "SymbolTableNode", "cross_ref": "spacy.util.resolve_dot_names", "kind": "Gdef"}, "set_gpu_allocator": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.set_gpu_allocator", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "subdivide_batch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["batch", "accumulate_gradient"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.subdivide_batch", "name": "subdivide_batch", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "timer": {".class": "SymbolTableNode", "cross_ref": "timeit.default_timer", "kind": "Gdef"}, "train": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["nlp", "output_path", "use_gpu", "stdout", "stderr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5, 5], "arg_names": ["nlp", "output_path", "use_gpu", "stdout", "stderr"], "arg_types": ["spacy.language.Language", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train", "ret_type": {".class": "TupleType", "implicit": false, "items": ["spacy.language.Language", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "train_while_improving": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["nlp", "optimizer", "train_data", "evaluate", "dropout", "eval_frequency", "accumulate_gradient", "patience", "max_steps", "exclude", "annotating_components", "before_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.train_while_improving", "name": "train_while_improving", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["nlp", "optimizer", "train_data", "evaluate", "dropout", "eval_frequency", "accumulate_gradient", "patience", "max_steps", "exclude", "annotating_components", "before_update"], "arg_types": ["spacy.language.Language", "thinc.optimizers.Optimizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["spacy.language.Language", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "train_while_improving", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_meta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["training", "nlp", "info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.training.loop.update_meta", "name": "update_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["training", "nlp", "info"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}, "spacy.language.Language", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_meta", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\training\\loop.py"}