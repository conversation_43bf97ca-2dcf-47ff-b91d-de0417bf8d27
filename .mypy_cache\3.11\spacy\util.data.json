{".class": "MypyFile", "_fullname": "spacy.util", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Adam": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.Adam", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CONFIG_SECTION_ORDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.util.CONFIG_SECTION_ORDER", "name": "CONFIG_SECTION_ORDER", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "ConfigValidationError": {".class": "SymbolTableNode", "cross_ref": "confection.ConfigValidationError", "kind": "Gdef"}, "CudaStream": {".class": "SymbolTableNode", "cross_ref": "spacy.compat.CudaStream", "kind": "Gdef"}, "DEFAULT_OOV_PROB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.DEFAULT_OOV_PROB", "name": "DEFAULT_OOV_PROB", "type": "builtins.int"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Doc": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.doc.Doc", "kind": "Gdef"}, "DummyTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.util.DummyTokenizer", "name": "Dummy<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.util.DummyTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.util", "mro": ["spacy.util.DummyTokenizer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.DummyTokenizer.__call__", "name": "__call__", "type": null}}, "from_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "data", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.DummyTokenizer.from_bytes", "name": "from_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "data", "kwargs"], "arg_types": ["spacy.util.DummyTokenizer", "builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_bytes of DummyTokenizer", "ret_type": "spacy.util.DummyTokenizer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.DummyTokenizer.from_disk", "name": "from_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["spacy.util.DummyTokenizer", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_disk of DummyTokenizer", "ret_type": "spacy.util.DummyTokenizer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pipe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "texts", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.DummyTokenizer.pipe", "name": "pipe", "type": null}}, "to_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.DummyTokenizer.to_bytes", "name": "to_bytes", "type": null}}, "to_disk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.DummyTokenizer.to_disk", "name": "to_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "path", "kwargs"], "arg_types": ["spacy.util.DummyTokenizer", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_disk of DummyTokenizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.util.DummyTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.util.DummyTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ENV_VARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.util.ENV_VARS", "name": "ENV_VARS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.util.ENV_VARS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.util", "mro": ["spacy.util.ENV_VARS", "builtins.object"], "names": {".class": "SymbolTable", "CONFIG_OVERRIDES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.ENV_VARS.CONFIG_OVERRIDES", "name": "CONFIG_OVERRIDES", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.util.ENV_VARS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.util.ENV_VARS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Errors": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Errors", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "InvalidSpecifier": {".class": "SymbolTableNode", "cross_ref": "packaging.specifiers.InvalidSpecifier", "kind": "Gdef"}, "InvalidVersion": {".class": "SymbolTableNode", "cross_ref": "packaging.version.InvalidVersion", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LEXEME_NORM_LANGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.util.LEXEME_NORM_LANGS", "name": "LEXEME_NORM_LANGS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Language": {".class": "SymbolTableNode", "cross_ref": "spacy.language.Language", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "NumpyOps": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.NumpyOps", "kind": "Gdef"}, "OLD_MODEL_SHORTCUTS": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.OLD_MODEL_SHORTCUTS", "kind": "Gdef"}, "OOV_RANK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.util.OOV_RANK", "name": "OOV_RANK", "type": "builtins.int"}}, "ORTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.util.ORTH", "name": "ORTH", "type": {".class": "AnyType", "missing_import_name": "spacy.util.ORTH", "source_any": null, "type_of_any": 3}}}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.Optimizer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "PipeCallable": {".class": "SymbolTableNode", "cross_ref": "spacy.language.PipeCallable", "kind": "Gdef"}, "Registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.util.Registry", "name": "Registry", "type": {".class": "AnyType", "missing_import_name": "spacy.util.Registry", "source_any": null, "type_of_any": 3}}}, "RegistryError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.util.RegistryError", "name": "RegistryError", "type": {".class": "AnyType", "missing_import_name": "spacy.util.RegistryError", "source_any": null, "type_of_any": 3}}}, "Requirement": {".class": "SymbolTableNode", "cross_ref": "packaging.requirements.Requirement", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "SimpleFrozenDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.util.SimpleFrozenDict", "name": "SimpleFrozenDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenDict", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "spacy.util", "mro": ["spacy.util.SimpleFrozenDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "error", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenDict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 4], "arg_names": ["self", "args", "error", "kwargs"], "arg_types": ["spacy.util.SimpleFrozenDict", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleFrozenDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenDict.__setitem__", "name": "__setitem__", "type": null}}, "error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.util.SimpleFrozenDict.error", "name": "error", "type": "builtins.str"}}, "pop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenDict.pop", "name": "pop", "type": null}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenDict.update", "name": "update", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.util.SimpleFrozenDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.util.SimpleFrozenDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleFrozenList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.util.SimpleFrozenList", "name": "SimpleFrozenList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "spacy.util", "mro": ["spacy.util.SimpleFrozenList", "builtins.list", "typing.MutableSequence", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "args", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5], "arg_names": ["self", "args", "error"], "arg_types": ["spacy.util.SimpleFrozenList", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SimpleFrozenList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.append", "name": "append", "type": null}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.clear", "name": "clear", "type": null}}, "error": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "spacy.util.SimpleFrozenList.error", "name": "error", "type": "builtins.str"}}, "extend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.extend", "name": "extend", "type": null}}, "insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.insert", "name": "insert", "type": null}}, "pop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.pop", "name": "pop", "type": null}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.remove", "name": "remove", "type": null}}, "reverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.reverse", "name": "reverse", "type": null}}, "sort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.SimpleFrozenList.sort", "name": "sort", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.util.SimpleFrozenList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.util.SimpleFrozenList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Span": {".class": "SymbolTableNode", "cross_ref": "spacy.tokens.span.Span", "kind": "Gdef"}, "SpecifierSet": {".class": "SymbolTableNode", "cross_ref": "packaging.specifiers.SpecifierSet", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Version": {".class": "SymbolTableNode", "cross_ref": "packaging.version.Version", "kind": "Gdef"}, "Vocab": {".class": "SymbolTableNode", "cross_ref": "spacy.vocab.Vocab", "kind": "Gdef"}, "Warnings": {".class": "SymbolTableNode", "cross_ref": "spacy.errors.Warnings", "kind": "Gdef"}, "_DEFAULT_EMPTY_PIPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.util._DEFAULT_EMPTY_PIPES", "name": "_DEFAULT_EMPTY_PIPES", "type": "spacy.util.SimpleFrozenList"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "spacy.util.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_attr_unless_lookup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["default_func", "lookups", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util._get_attr_unless_lookup", "name": "_get_attr_unless_lookup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["default_func", "lookups", "string"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_attr_unless_lookup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_port_in_use": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["port", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util._is_port_in_use", "name": "_is_port_in_use", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["port", "host"], "arg_types": ["builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_port_in_use", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["docs", "proc", "name", "default_error_handler", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util._pipe", "name": "_pipe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["docs", "proc", "name", "default_error_handler", "kwargs"], "arg_types": [{".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "spacy.language.PipeCallable"}, "builtins.str", {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "spacy.language.PipeCallable"}, {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pipe", "ret_type": {".class": "Instance", "args": ["spacy.tokens.doc.Doc"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "about": {".class": "SymbolTableNode", "cross_ref": "spacy.about", "kind": "Gdef"}, "add_lookups": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["default_func", "lookups"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.add_lookups", "name": "add_lookups", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["default_func", "lookups"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_lookups", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_equal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.all_equal", "name": "all_equal", "type": null}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "catalogue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.util.catalogue", "name": "catalogue", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}}}, "check_bool_env_var": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["env_var"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.check_bool_env_var", "name": "check_bool_env_var", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["env_var"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_bool_env_var", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_lexeme_norms": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["vocab", "component_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.check_lexeme_norms", "name": "check_lexeme_norms", "type": null}}, "combine_score_weights": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["weights", "overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.combine_score_weights", "name": "combine_score_weights", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["weights", "overrides"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "combine_score_weights", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_infix_regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.compile_infix_regex", "name": "compile_infix_regex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["entries"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_infix_regex", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_prefix_regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.compile_prefix_regex", "name": "compile_prefix_regex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["entries"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_prefix_regex", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_suffix_regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.compile_suffix_regex", "name": "compile_suffix_regex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["entries"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compile_suffix_regex", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compounding": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.compounding", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "copy_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.copy_config", "name": "copy_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_config", "ret_type": "confection.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_default_optimizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.create_default_optimizer", "name": "create_default_optimizer", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_default_optimizer", "ret_type": "thinc.optimizers.Optimizer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cupy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.util.cupy", "name": "cupy", "type": {".class": "AnyType", "missing_import_name": "spacy.util.cupy", "source_any": null, "type_of_any": 3}}}, "decaying": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.decaying", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "dict_to_dot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["obj", "for_overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.dict_to_dot", "name": "dict_to_dot", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["obj", "for_overrides"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dict_to_dot", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dot_to_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.dot_to_dict", "name": "dot_to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["values"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dot_to_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dot_to_object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.dot_to_object", "name": "dot_to_object", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "section"], "arg_types": ["confection.Config", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dot_to_object", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ensure_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.ensure_path", "name": "ensure_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_path", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "escape_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.escape_html", "name": "escape_html", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "escape_html", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expand_exc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["excs", "search", "replace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.expand_exc", "name": "expand_exc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["excs", "search", "replace"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_exc", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_chain_spans": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["spans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.filter_chain_spans", "name": "filter_chain_spans", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["spans"], "arg_types": [{".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_chain_spans", "ret_type": {".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_spans": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["spans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.filter_spans", "name": "filter_spans", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["spans"], "arg_types": [{".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_spans", "ret_type": {".class": "Instance", "args": ["spacy.tokens.span.Span"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_available_port": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["start", "host", "auto_select"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.find_available_port", "name": "find_available_port", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["start", "host", "auto_select"], "arg_types": ["builtins.int", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_available_port", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_matching_language": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lang"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.find_matching_language", "name": "find_matching_language", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lang"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_matching_language", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fix_random_seed": {".class": "SymbolTableNode", "cross_ref": "thinc.util.fix_random_seed", "kind": "Gdef"}, "from_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["bytes_data", "setters", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.from_bytes", "name": "from_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["bytes_data", "setters", "exclude"], "arg_types": ["builtins.bytes", {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_bytes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["msg", "setters", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["msg", "setters", "exclude"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_disk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["path", "readers", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.from_disk", "name": "from_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["path", "readers", "exclude"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_disk", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_arg_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_arg_names", "name": "get_arg_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_arg_names", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_async": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["stream", "numpy_array"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_async", "name": "get_async", "type": null}}, "get_base_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_base_version", "name": "get_base_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["version"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_base_version", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cuda_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["require", "non_blocking"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_cuda_stream", "name": "get_cuda_stream", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["require", "non_blocking"], "arg_types": ["builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cuda_stream", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "spacy.compat.CudaStream", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_current_ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.get_current_ops", "kind": "Gdef"}, "get_installed_models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_installed_models", "name": "get_installed_models", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_installed_models", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_lang_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lang"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_lang_class", "name": "get_lang_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lang"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_lang_class", "ret_type": {".class": "TypeType", "item": "spacy.language.Language"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_minor_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_minor_version", "name": "get_minor_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["version"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_minor_version", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_minor_version_range": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_minor_version_range", "name": "get_minor_version_range", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["version"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_minor_version_range", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_lower_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_model_lower_version", "name": "get_model_lower_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["constraint"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_model_lower_version", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_meta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_model_meta", "name": "get_model_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_model_meta", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_module_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_module_path", "name": "get_module_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["module"], "arg_types": ["types.ModuleType"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_module_path", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_object_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_object_name", "name": "get_object_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_object_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_package_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_package_path", "name": "get_package_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_package_path", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_package_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_package_version", "name": "get_package_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_package_version", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sourced_components": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_sourced_components", "name": "get_sourced_components", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sourced_components", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_words_and_spaces": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["words", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.get_words_and_spaces", "name": "get_words_and_spaces", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["words", "text"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_words_and_spaces", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["proc_name", "proc", "docs", "e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.ignore_error", "name": "ignore_error", "type": null}}, "import_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "loc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.import_file", "name": "import_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "loc"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_file", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "importlib_metadata": {".class": "SymbolTableNode", "cross_ref": "importlib.metadata", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_compatible_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["version", "constraint", "prereleases"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_compatible_version", "name": "is_compatible_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["version", "constraint", "prereleases"], "arg_types": ["builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_compatible_version", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_cython_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_cython_func", "name": "is_cython_func", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_cython_func", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_in_interactive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_in_interactive", "name": "is_in_interactive", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_in_interactive", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_in_jupyter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_in_jupyter", "name": "is_in_jupyter", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_in_jupyter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_minor_version_match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["version_a", "version_b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_minor_version_match", "name": "is_minor_version_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["version_a", "version_b"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_minor_version_match", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_package", "name": "is_package", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_package", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_prerelease_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_prerelease_version", "name": "is_prerelease_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["version"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_prerelease_version", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_same_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["func1", "func2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_same_func", "name": "is_same_func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["func1", "func2"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_same_func", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_unconstrained_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["constraint", "prereleases"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.is_unconstrained_version", "name": "is_unconstrained_version", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["constraint", "prereleases"], "arg_types": ["builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unconstrained_version", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_windows": {".class": "SymbolTableNode", "cross_ref": "spacy.compat.is_windows", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "lang_class_is_loaded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lang"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.lang_class_is_loaded", "name": "lang_class_is_loaded", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lang"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lang_class_is_loaded", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "langcodes": {".class": "SymbolTableNode", "cross_ref": "langcodes", "kind": "Gdef"}, "load_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["path", "overrides", "interpolate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_config", "name": "load_config", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["path", "overrides", "interpolate"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_config", "ret_type": "confection.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_config_from_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["text", "overrides", "interpolate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_config_from_str", "name": "load_config_from_str", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["text", "overrides", "interpolate"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_config_from_str", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_language_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_language_data", "name": "load_language_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_language_data", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_meta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_meta", "name": "load_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_meta", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["name", "vocab", "disable", "enable", "exclude", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_model", "name": "load_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["name", "vocab", "disable", "enable", "exclude", "config"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["spacy.vocab.Vocab", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_model", "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_model_from_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["config", "meta", "vocab", "disable", "enable", "exclude", "auto_fill", "validate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_model_from_config", "name": "load_model_from_config", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["config", "meta", "vocab", "disable", "enable", "exclude", "auto_fill", "validate"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["spacy.vocab.Vocab", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_model_from_config", "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_model_from_init_py": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["init_file", "vocab", "disable", "enable", "exclude", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_model_from_init_py", "name": "load_model_from_init_py", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["init_file", "vocab", "disable", "enable", "exclude", "config"], "arg_types": [{".class": "UnionType", "items": ["pathlib.Path", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["spacy.vocab.Vocab", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_model_from_init_py", "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_model_from_package": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["name", "vocab", "disable", "enable", "exclude", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_model_from_package", "name": "load_model_from_package", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["name", "vocab", "disable", "enable", "exclude", "config"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["spacy.vocab.Vocab", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_model_from_package", "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_model_from_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["model_path", "meta", "vocab", "disable", "enable", "exclude", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.load_model_from_path", "name": "load_model_from_path", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["model_path", "meta", "vocab", "disable", "enable", "exclude", "config"], "arg_types": ["pathlib.Path", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["spacy.vocab.Vocab", "builtins.bool"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "confection.Config"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_model_from_path", "ret_type": "spacy.language.Language", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.util.logger", "name": "logger", "type": "logging.Logger"}}, "logger_stream_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "spacy.util.logger_stream_handler", "name": "logger_stream_handler", "type": {".class": "Instance", "args": ["typing.TextIO"], "extra_attrs": null, "type_ref": "logging.StreamHandler"}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_first_longest_spans_filter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.make_first_longest_spans_filter", "name": "make_first_longest_spans_filter", "type": null}}, "make_tempdir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.util.make_tempdir", "name": "make_tempdir", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_tempdir", "ret_type": {".class": "Instance", "args": ["pathlib.Path", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.util.make_tempdir", "name": "make_tempdir", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_tempdir", "ret_type": {".class": "Instance", "args": ["pathlib.Path", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "minibatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["items", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.minibatch", "name": "minibatch", "type": null}}, "minify_html": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["html"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.minify_html", "name": "minify_html", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["html"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "minify_html", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "normalize_slice": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["length", "start", "stop", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.normalize_slice", "name": "normalize_slice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["length", "start", "stop", "step"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "normalize_slice", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "numpy": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "packages_distributions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.packages_distributions", "name": "packages_distributions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "packages_distributions", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pkgutil": {".class": "SymbolTableNode", "cross_ref": "pkgu<PERSON>", "kind": "Gdef"}, "raise_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["proc_name", "proc", "docs", "e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.raise_error", "name": "raise_error", "type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "read_regex": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.read_regex", "name": "read_regex", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_regex", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["thinc.config.registry"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "spacy.util.registry", "name": "registry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "spacy.util.registry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "spacy.util", "mro": ["spacy.util.registry", "thinc.config.registry", "confection.registry", "builtins.object"], "names": {".class": "SymbolTable", "_entry_point_factories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry._entry_point_factories", "name": "_entry_point_factories", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "architectures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.architectures", "name": "architectures", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "augmenters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.augmenters", "name": "augmenters", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "batchers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.batchers", "name": "batchers", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "callbacks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.callbacks", "name": "callbacks", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "cli": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.cli", "name": "cli", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "displacy_colors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.displacy_colors", "name": "displacy_colors", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "ensure_populated": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "spacy.util.registry.ensure_populated", "name": "ensure_populated", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_populated of registry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "spacy.util.registry.ensure_populated", "name": "ensure_populated", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_populated of registry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "factories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.factories", "name": "factories", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "spacy.util.registry.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of registry", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "spacy.util.registry.find", "name": "find", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find of registry", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "spacy.util.registry.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of registry", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "spacy.util.registry.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of registry", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_registry_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "spacy.util.registry.get_registry_names", "name": "get_registry_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_registry_names of registry", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "spacy.util.registry.get_registry_names", "name": "get_registry_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_registry_names of registry", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "spacy.util.registry.has", "name": "has", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has of registry", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "spacy.util.registry.has", "name": "has", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "registry_name", "func_name"], "arg_types": [{".class": "TypeType", "item": "spacy.util.registry"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has of registry", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.languages", "name": "languages", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "lemmatizers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.lemmatizers", "name": "lemmatizers", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "loggers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.loggers", "name": "loggers", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "lookups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.lookups", "name": "lookups", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "misc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.misc", "name": "misc", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.models", "name": "models", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "readers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.readers", "name": "readers", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "scorers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.scorers", "name": "scorers", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "tokenizers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.tokenizers", "name": "tokenizers", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "vectors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "spacy.util.registry.vectors", "name": "vectors", "type": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": {".class": "AnyType", "missing_import_name": "spacy.util.catalogue", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "spacy.util.registry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "spacy.util.registry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "replace_model_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "target", "replacement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.replace_model_node", "name": "replace_model_node", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "target", "replacement"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "thinc.model.Model"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_model_node", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_dot_names": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "dot_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.resolve_dot_names", "name": "resolve_dot_names", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "dot_names"], "arg_types": ["confection.Config", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_dot_names", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["command", "stdin", "capture"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.run_command", "name": "run_command", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["command", "stdin", "capture"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_command", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "subprocess.CompletedProcess"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_dot_to_object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["config", "section", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.set_dot_to_object", "name": "set_dot_to_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "section", "value"], "arg_types": ["confection.Config", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_dot_to_object", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_lang_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["name", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.set_lang_class", "name": "set_lang_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["name", "cls"], "arg_types": ["builtins.str", {".class": "TypeType", "item": "spacy.language.Language"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_lang_class", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shlex": {".class": "SymbolTableNode", "cross_ref": "shlex", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "split_command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["command"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.split_command", "name": "split_command", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["command"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_command", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split_requirement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["requirement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.split_requirement", "name": "split_requirement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["requirement"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_requirement", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "srsly": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "spacy.util.srsly", "name": "srsly", "type": {".class": "AnyType", "missing_import_name": "spacy.util.srsly", "source_any": null, "type_of_any": 3}}}, "stat": {".class": "SymbolTableNode", "cross_ref": "stat", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "thinc": {".class": "SymbolTableNode", "cross_ref": "thinc", "kind": "Gdef"}, "to_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["getters", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.to_bytes", "name": "to_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["getters", "exclude"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_bytes", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["getters", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["getters", "exclude"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_disk": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["path", "writers", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.to_disk", "name": "to_disk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["path", "writers", "exclude"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_disk", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_ternary_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.to_ternary_int", "name": "to_ternary_int", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["val"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_ternary_int", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_exc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["base_exceptions", "addition_dicts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.update_exc", "name": "update_exc", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["base_exceptions", "addition_dicts"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_exc", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "walk_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["node", "parent", "for_overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.walk_dict", "name": "walk_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["node", "parent", "for_overrides"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "walk_dict", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warn_if_jupyter_cupy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "spacy.util.warn_if_jupyter_cupy", "name": "warn_if_jupyter_cupy", "type": null}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "working_dir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "spacy.util.working_dir", "name": "working_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "working_dir", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "spacy.util.working_dir", "name": "working_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "working_dir", "ret_type": {".class": "Instance", "args": ["pathlib.Path", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\util.py"}