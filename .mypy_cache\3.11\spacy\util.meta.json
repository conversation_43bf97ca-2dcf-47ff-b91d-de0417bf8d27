{"data_mtime": 1753783925, "dep_lines": [3, 48, 49, 50, 51, 72, 73, 74, 79, 80, 81, 138, 1, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 43, 44, 46, 72, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 62, 75, 42, 45, 62], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 5, 25, 25, 25, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 5, 5, 10, 20], "dependencies": ["importlib.util", "packaging.requirements", "packaging.specifiers", "packaging.version", "thinc.api", "spacy.about", "spacy.compat", "spacy.errors", "spacy.language", "spacy.tokens", "spacy.vocab", "spacy.registrations", "functools", "importlib", "inspect", "itertools", "logging", "os", "pkgu<PERSON>", "re", "shlex", "shutil", "socket", "stat", "subprocess", "sys", "tempfile", "warnings", "collections", "contextlib", "pathlib", "types", "typing", "langcodes", "numpy", "thinc", "spacy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "_frozen_importlib", "_typeshed", "abc", "confection", "numpy._typing", "numpy._typing._dtype_like", "spacy.tokens.doc", "spacy.tokens.span", "thinc.config", "thinc.model", "thinc.optimizers"], "hash": "e308ebeef273efb7791b9cdd4146d9a304604404", "id": "spacy.util", "ignore_all": true, "interface_hash": "19164f44239061e17d74a48623ff32c87f4ea1ec", "mtime": 1749318793, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\spacy\\util.py", "plugin_data": null, "size": 69707, "suppressed": ["cupy.random", "spacy.symbols", "catalogue", "srsly", "cupy"], "version_id": "1.15.0"}