{"data_mtime": 1753783521, "dep_lines": [8, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 10, 20, 5, 20, 30, 30, 30, 30, 30], "dependencies": ["anyio.to_thread", "__future__", "functools", "sys", "typing", "warnings", "anyio", "builtins", "dataclasses", "_frozen_importlib", "_typeshed", "abc", "anyio._core", "anyio._core._synchronization"], "hash": "68b4189caca85767274df122ae762d0ec319e0f9", "id": "starlette.concurrency", "ignore_all": true, "interface_hash": "97c7d986c0ae62dc0c6e24b9f8a02a1cbe42ed16", "mtime": 1743168367, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\starlette\\concurrency.py", "plugin_data": null, "size": 1850, "suppressed": [], "version_id": "1.15.0"}