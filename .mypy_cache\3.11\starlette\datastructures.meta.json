{"data_mtime": 1753783525, "dep_lines": [5, 7, 8, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["urllib.parse", "starlette.concurrency", "starlette.types", "__future__", "typing", "shlex", "builtins", "dataclasses", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "types", "typing_extensions", "urllib"], "hash": "27530302c66db7f8c73df2f1e78b269efa18a1d7", "id": "starlette.datastructures", "ignore_all": true, "interface_hash": "a36145efd67aee6770dd3da3fd8ca9097cdb11cb", "mtime": 1743168367, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\starlette\\datastructures.py", "plugin_data": null, "size": 22334, "suppressed": [], "version_id": "1.15.0"}