{"data_mtime": 1753783525, "dep_lines": [7, 9, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 13, 12], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 25, 25], "dependencies": ["urllib.parse", "starlette.datastructures", "__future__", "typing", "dataclasses", "enum", "tempfile", "builtins", "_frozen_importlib", "_io", "abc", "io", "types", "typing_extensions"], "hash": "f741831d112bf179834fbe30a396abded51a43a7", "id": "starlette.formparsers", "ignore_all": true, "interface_hash": "0f08e935042afba665b641902dcad0f344e32747", "mtime": 1743168367, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\starlette\\formparsers.py", "plugin_data": null, "size": 10779, "suppressed": ["multipart.multipart", "multipart"], "version_id": "1.15.0"}