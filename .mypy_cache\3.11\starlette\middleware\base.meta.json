{"data_mtime": 1753783525, "dep_lines": [6, 8, 9, 10, 11, 1, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 5, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["anyio.abc", "starlette._utils", "starlette.requests", "starlette.responses", "starlette.types", "__future__", "typing", "anyio", "builtins", "dataclasses", "_frozen_importlib", "abc", "anyio._core", "anyio._core._exceptions", "anyio._core._streams", "anyio._core._synchronization", "anyio._core._tasks", "anyio._core._typedattr", "anyio.abc._resources", "anyio.abc._streams", "anyio.abc._tasks", "anyio.streams", "anyio.streams.memory", "contextlib", "starlette.background"], "hash": "4a45dd11d43f0673b22216467969b1d5bc2e8a37", "id": "starlette.middleware.base", "ignore_all": true, "interface_hash": "6b20229ef59f6ffaf67030f70be6a92648f9ba6c", "mtime": 1743168367, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\starlette\\middleware\\base.py", "plugin_data": null, "size": 9204, "suppressed": [], "version_id": "1.15.0"}