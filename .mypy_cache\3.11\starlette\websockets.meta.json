{"data_mtime": 1753783525, "dep_lines": [7, 8, 9, 1, 3, 4, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 20, 30, 30], "dependencies": ["starlette.requests", "starlette.responses", "starlette.types", "__future__", "enum", "json", "typing", "builtins", "dataclasses", "_frozen_importlib", "abc"], "hash": "0ab71d4bacef09a176e478ddad02958474d40ba4", "id": "starlette.websockets", "ignore_all": true, "interface_hash": "b65aca23373384b74b3d3ffa5f4a12f2b9ee74ed", "mtime": 1743168367, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\starlette\\websockets.py", "plugin_data": null, "size": 8311, "suppressed": [], "version_id": "1.15.0"}