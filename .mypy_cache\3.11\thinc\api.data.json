{".class": "MypyFile", "_fullname": "thinc.api", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Adam": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.Adam", "kind": "Gdef"}, "ArgsKwargs": {".class": "SymbolTableNode", "cross_ref": "thinc.types.ArgsKwargs", "kind": "Gdef"}, "CategoricalCrossentropy": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.CategoricalCrossentropy", "kind": "Gdef"}, "CauchySimilarity": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.cauchysimilarity.CauchySimilarity", "kind": "Gdef"}, "ClippedLinear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.clipped_linear.ClippedLinear", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "confection.Config", "kind": "Gdef"}, "ConfigValidationError": {".class": "SymbolTableNode", "cross_ref": "confection.ConfigValidationError", "kind": "Gdef"}, "CosineDistance": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.CosineDistance", "kind": "Gdef"}, "CupyOps": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.cupy_ops.CupyOps", "kind": "Gdef"}, "DataValidationError": {".class": "SymbolTableNode", "cross_ref": "thinc.util.DataValidationError", "kind": "Gdef"}, "Dish": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.dish.Dish", "kind": "Gdef"}, "Dropout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.dropout.Dropout", "kind": "Gdef"}, "Embed": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.embed.Embed", "kind": "Gdef"}, "Gelu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.gelu.Gelu", "kind": "Gdef"}, "HardSigmoid": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.clipped_linear.HardSigmoid", "kind": "Gdef"}, "HardSwish": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.hard_swish.HardSwish", "kind": "Gdef"}, "HardSwishMobilenet": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.hard_swish_mobilenet.HardSwishMobilenet", "kind": "Gdef"}, "HardTanh": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.clipped_linear.HardTanh", "kind": "Gdef"}, "HashEmbed": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.hashembed.HashEmbed", "kind": "Gdef"}, "L2Distance": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.L2Distance", "kind": "Gdef"}, "LSTM": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.lstm.LSTM", "kind": "Gdef"}, "LayerNorm": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.layernorm.LayerNorm", "kind": "Gdef"}, "Linear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.linear.Linear", "kind": "Gdef"}, "Logistic": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.logistic.Logistic", "kind": "Gdef"}, "MPSOps": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.mps_ops.MPSOps", "kind": "Gdef"}, "MXNetShim": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.mxnet.MXNetShim", "kind": "Gdef"}, "MXNetWrapper": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.mxnetwrapper.MXNetWrapper", "kind": "Gdef"}, "Maxout": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.maxout.Maxout", "kind": "Gdef"}, "Mish": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.mish.Mish", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "thinc.model.Model", "kind": "Gdef"}, "MultiSoftmax": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.multisoftmax.MultiSoftmax", "kind": "Gdef"}, "NumpyOps": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.NumpyOps", "kind": "Gdef"}, "Ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.ops.Ops", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.Optimizer", "kind": "Gdef"}, "Padded": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Padded", "kind": "Gdef"}, "ParametricAttention": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.parametricattention.ParametricAttention", "kind": "Gdef"}, "ParametricAttention_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.parametricattention_v2.ParametricAttention_v2", "kind": "Gdef"}, "PyTorchGradScaler": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.pytorch_grad_scaler.PyTorchGradScaler", "kind": "Gdef"}, "PyTorchLSTM": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.lstm.PyTorchLSTM", "kind": "Gdef"}, "PyTorchRNNWrapper": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.pytorchwrapper.PyTorchRNNWrapper", "kind": "Gdef"}, "PyTorchShim": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.pytorch.PyTorchShim", "kind": "Gdef"}, "PyTorchWrapper": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.pytorchwrapper.PyTorchWrapper", "kind": "Gdef"}, "PyTorchWrapper_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.pytorchwrapper.PyTorchWrapper_v2", "kind": "Gdef"}, "PyTorchWrapper_v3": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.pytorchwrapper.PyTorchWrapper_v3", "kind": "Gdef"}, "RAdam": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.RAdam", "kind": "Gdef"}, "Ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Ragged", "kind": "Gdef"}, "Relu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.relu.Relu", "kind": "Gdef"}, "ReluK": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.clipped_linear.ReluK", "kind": "Gdef"}, "SGD": {".class": "SymbolTableNode", "cross_ref": "thinc.optimizers.SGD", "kind": "Gdef"}, "SequenceCategoricalCrossentropy": {".class": "SymbolTableNode", "cross_ref": "thinc.loss.SequenceCategoricalCrossentropy", "kind": "Gdef"}, "Shim": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.shim.Shim", "kind": "Gdef"}, "Sigmoid": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.sigmoid.Sigmoid", "kind": "Gdef"}, "Softmax": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax.Softmax", "kind": "Gdef"}, "Softmax_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax.Softmax_v2", "kind": "Gdef"}, "SparseLinear": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.SparseLinear", "kind": "Gdef"}, "SparseLinear_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.SparseLinear_v2", "kind": "Gdef"}, "Swish": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.swish.Swish", "kind": "Gdef"}, "TensorFlowShim": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.tensorflow.TensorFlowShim", "kind": "Gdef"}, "TensorFlowWrapper": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.tensorflowwrapper.TensorFlowWrapper", "kind": "Gdef"}, "TorchScriptShim": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.torchscript.TorchScriptShim", "kind": "Gdef"}, "TorchScriptWrapper_v1": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.torchscriptwrapper.TorchScriptWrapper_v1", "kind": "Gdef"}, "Unserializable": {".class": "SymbolTableNode", "cross_ref": "thinc.types.Unserializable", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "thinc.api.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "thinc.api.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "thinc.api.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "thinc.api.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "thinc.api.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "thinc.api.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "thinc.api.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.add.add", "kind": "Gdef"}, "array_getitem": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.array_getitem.array_getitem", "kind": "Gdef"}, "bidirectional": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.bidirectional.bidirectional", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.chain.chain", "kind": "Gdef"}, "change_attr_values": {".class": "SymbolTableNode", "cross_ref": "thinc.model.change_attr_values", "kind": "Gdef"}, "clone": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.clone.clone", "kind": "Gdef"}, "compounding": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.compounding", "kind": "Gdef"}, "concatenate": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.concatenate.concatenate", "kind": "Gdef"}, "configure_normal_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.configure_normal_init", "kind": "Gdef"}, "constant": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.constant", "kind": "Gdef"}, "constant_then": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.constant_then", "kind": "Gdef"}, "cyclic_triangular": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.cyclic_triangular", "kind": "Gdef"}, "data_validation": {".class": "SymbolTableNode", "cross_ref": "thinc.util.data_validation", "kind": "Gdef"}, "decaying": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.decaying", "kind": "Gdef"}, "deserialize_attr": {".class": "SymbolTableNode", "cross_ref": "thinc.model.deserialize_attr", "kind": "Gdef"}, "enable_mxnet": {".class": "SymbolTableNode", "cross_ref": "thinc.compat.enable_mxnet", "kind": "Gdef"}, "enable_tensorflow": {".class": "SymbolTableNode", "cross_ref": "thinc.compat.enable_tensorflow", "kind": "Gdef"}, "expand_window": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.expand_window.expand_window", "kind": "Gdef"}, "fix_random_seed": {".class": "SymbolTableNode", "cross_ref": "thinc.util.fix_random_seed", "kind": "Gdef"}, "get_array_module": {".class": "SymbolTableNode", "cross_ref": "thinc.util.get_array_module", "kind": "Gdef"}, "get_current_ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.get_current_ops", "kind": "Gdef"}, "get_ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.get_ops", "kind": "Gdef"}, "get_torch_default_device": {".class": "SymbolTableNode", "cross_ref": "thinc.util.get_torch_default_device", "kind": "Gdef"}, "get_width": {".class": "SymbolTableNode", "cross_ref": "thinc.util.get_width", "kind": "Gdef"}, "glorot_uniform_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.glorot_uniform_init", "kind": "Gdef"}, "has_cupy": {".class": "SymbolTableNode", "cross_ref": "thinc.compat.has_cupy", "kind": "Gdef"}, "is_cupy_array": {".class": "SymbolTableNode", "cross_ref": "thinc.util.is_cupy_array", "kind": "Gdef"}, "keras_model_fns": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.tensorflow.keras_model_fns", "kind": "Gdef"}, "keras_subclass": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.tensorflowwrapper.keras_subclass", "kind": "Gdef"}, "list2array": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2array.list2array", "kind": "Gdef"}, "list2padded": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2padded.list2padded", "kind": "Gdef"}, "list2ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.list2ragged.list2ragged", "kind": "Gdef"}, "map_list": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.map_list.map_list", "kind": "Gdef"}, "maybe_handshake_model": {".class": "SymbolTableNode", "cross_ref": "thinc.shims.tensorflow.maybe_handshake_model", "kind": "Gdef"}, "mxnet2xp": {".class": "SymbolTableNode", "cross_ref": "thinc.util.mxnet2xp", "kind": "Gdef"}, "noop": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.noop.noop", "kind": "Gdef"}, "normal_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.normal_init", "kind": "Gdef"}, "padded2list": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.padded2list.padded2list", "kind": "Gdef"}, "prefer_gpu": {".class": "SymbolTableNode", "cross_ref": "thinc.util.prefer_gpu", "kind": "Gdef"}, "premap_ids": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.premap_ids", "kind": "Gdef"}, "pytorch_to_torchscript_wrapper": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.torchscriptwrapper.pytorch_to_torchscript_wrapper", "kind": "Gdef"}, "ragged2list": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.ragged2list.ragged2list", "kind": "Gdef"}, "reduce_first": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_first.reduce_first", "kind": "Gdef"}, "reduce_last": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_last.reduce_last", "kind": "Gdef"}, "reduce_max": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_max.reduce_max", "kind": "Gdef"}, "reduce_mean": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_mean.reduce_mean", "kind": "Gdef"}, "reduce_sum": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.reduce_sum.reduce_sum", "kind": "Gdef"}, "registry": {".class": "SymbolTableNode", "cross_ref": "thinc.config.registry", "kind": "Gdef"}, "remap_ids": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.remap_ids.remap_ids", "kind": "Gdef"}, "remap_ids_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.remap_ids.remap_ids_v2", "kind": "Gdef"}, "require_cpu": {".class": "SymbolTableNode", "cross_ref": "thinc.util.require_cpu", "kind": "Gdef"}, "require_gpu": {".class": "SymbolTableNode", "cross_ref": "thinc.util.require_gpu", "kind": "Gdef"}, "residual": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.residual.residual", "kind": "Gdef"}, "resizable": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.resizable.resizable", "kind": "Gdef"}, "serialize_attr": {".class": "SymbolTableNode", "cross_ref": "thinc.model.serialize_attr", "kind": "Gdef"}, "set_active_gpu": {".class": "SymbolTableNode", "cross_ref": "thinc.util.set_active_gpu", "kind": "Gdef"}, "set_current_ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.set_current_ops", "kind": "Gdef"}, "set_dropout_rate": {".class": "SymbolTableNode", "cross_ref": "thinc.model.set_dropout_rate", "kind": "Gdef"}, "set_gpu_allocator": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.set_gpu_allocator", "kind": "Gdef"}, "siamese": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.siamese.siamese", "kind": "Gdef"}, "sigmoid_activation": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.sigmoid_activation.sigmoid_activation", "kind": "Gdef"}, "slanted_triangular": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.slanted_triangular", "kind": "Gdef"}, "softmax_activation": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.softmax_activation.softmax_activation", "kind": "Gdef"}, "strings2arrays": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.strings2arrays.strings2arrays", "kind": "Gdef"}, "tensorflow2xp": {".class": "SymbolTableNode", "cross_ref": "thinc.util.tensorflow2xp", "kind": "Gdef"}, "to_categorical": {".class": "SymbolTableNode", "cross_ref": "thinc.util.to_categorical", "kind": "Gdef"}, "to_numpy": {".class": "SymbolTableNode", "cross_ref": "thinc.util.to_numpy", "kind": "Gdef"}, "torch2xp": {".class": "SymbolTableNode", "cross_ref": "thinc.util.torch2xp", "kind": "Gdef"}, "tuplify": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.tuplify.tuplify", "kind": "Gdef"}, "uniform_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.uniform_init", "kind": "Gdef"}, "uniqued": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.uniqued.uniqued", "kind": "Gdef"}, "use_ops": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.use_ops", "kind": "Gdef"}, "use_pytorch_for_gpu_memory": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.use_pytorch_for_gpu_memory", "kind": "Gdef"}, "use_tensorflow_for_gpu_memory": {".class": "SymbolTableNode", "cross_ref": "thinc.backends.use_tensorflow_for_gpu_memory", "kind": "Gdef"}, "warmup_linear": {".class": "SymbolTableNode", "cross_ref": "thinc.schedules.warmup_linear", "kind": "Gdef"}, "with_array": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_array.with_array", "kind": "Gdef"}, "with_array2d": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_array2d.with_array2d", "kind": "Gdef"}, "with_cpu": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_cpu.with_cpu", "kind": "Gdef"}, "with_debug": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_debug.with_debug", "kind": "Gdef"}, "with_flatten": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_flatten.with_flatten", "kind": "Gdef"}, "with_flatten_v2": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_flatten_v2.with_flatten_v2", "kind": "Gdef"}, "with_getitem": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_getitem.with_getitem", "kind": "Gdef"}, "with_list": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_list.with_list", "kind": "Gdef"}, "with_nvtx_range": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_nvtx_range.with_nvtx_range", "kind": "Gdef"}, "with_padded": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_padded.with_padded", "kind": "Gdef"}, "with_ragged": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_ragged.with_ragged", "kind": "Gdef"}, "with_reshape": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_reshape.with_reshape", "kind": "Gdef"}, "with_signpost_interval": {".class": "SymbolTableNode", "cross_ref": "thinc.layers.with_signpost_interval.with_signpost_interval", "kind": "Gdef"}, "wrap_model_recursive": {".class": "SymbolTableNode", "cross_ref": "thinc.model.wrap_model_recursive", "kind": "Gdef"}, "xp2mxnet": {".class": "SymbolTableNode", "cross_ref": "thinc.util.xp2mxnet", "kind": "Gdef"}, "xp2tensorflow": {".class": "SymbolTableNode", "cross_ref": "thinc.util.xp2tensorflow", "kind": "Gdef"}, "xp2torch": {".class": "SymbolTableNode", "cross_ref": "thinc.util.xp2torch", "kind": "Gdef"}, "zero_init": {".class": "SymbolTableNode", "cross_ref": "thinc.initializers.zero_init", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\thinc\\api.py"}