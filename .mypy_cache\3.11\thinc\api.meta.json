{"data_mtime": 1753783925, "dep_lines": [23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 1, 14, 15, 16, 23, 106, 112, 120, 121, 130, 140, 141, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30], "dependencies": ["thinc.layers.add", "thinc.layers.array_getitem", "thinc.layers.bidirectional", "thinc.layers.chain", "thinc.layers.clone", "thinc.layers.concatenate", "thinc.layers.expand_window", "thinc.layers.list2array", "thinc.layers.list2padded", "thinc.layers.list2ragged", "thinc.layers.map_list", "thinc.layers.noop", "thinc.layers.padded2list", "thinc.layers.ragged2list", "thinc.layers.reduce_first", "thinc.layers.reduce_last", "thinc.layers.reduce_max", "thinc.layers.reduce_mean", "thinc.layers.reduce_sum", "thinc.layers.remap_ids", "thinc.layers.residual", "thinc.layers.resizable", "thinc.layers.siamese", "thinc.layers.sigmoid_activation", "thinc.layers.softmax_activation", "thinc.layers.strings2arrays", "thinc.layers.tuplify", "thinc.layers.uniqued", "thinc.layers.with_array", "thinc.layers.with_array2d", "thinc.layers.with_cpu", "thinc.layers.with_debug", "thinc.layers.with_flatten", "thinc.layers.with_flatten_v2", "thinc.layers.with_getitem", "thinc.layers.with_list", "thinc.layers.with_nvtx_range", "thinc.layers.with_padded", "thinc.layers.with_ragged", "thinc.layers.with_reshape", "thinc.layers.with_signpost_interval", "thinc.backends", "thinc.compat", "thinc.config", "thinc.initializers", "thinc.layers", "thinc.loss", "thinc.model", "thinc.optimizers", "thinc.schedules", "thinc.shims", "thinc.types", "thinc.util", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "_frozen_importlib", "abc", "typing"], "hash": "b400dca97686a7c53c4a7751c5a173fb673a452c", "id": "thinc.api", "ignore_all": true, "interface_hash": "5eb143d80ef59eecaad31ee86b4cc34ca1e4e865", "mtime": 1749318790, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\thinc\\api.py", "plugin_data": null, "size": 6093, "suppressed": [], "version_id": "1.15.0"}