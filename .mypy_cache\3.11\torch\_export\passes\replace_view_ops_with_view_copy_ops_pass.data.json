{".class": "MypyFile", "_fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HigherOrderOperator": {".class": "SymbolTableNode", "cross_ref": "torch._ops.HigherOrderOperator", "kind": "Gdef", "module_public": false}, "InternalError": {".class": "SymbolTableNode", "cross_ref": "torch._export.error.InternalError", "kind": "Gdef", "module_public": false}, "OpOverload": {".class": "SymbolTableNode", "cross_ref": "torch._ops.OpOverload", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ReplaceViewOpsWithViewCopyOpsPass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.ReplaceViewOpsWithViewCopyOpsPass", "name": "ReplaceViewOpsWithViewCopyOpsPass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.ReplaceViewOpsWithViewCopyOpsPass", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass", "mro": ["torch._export.passes.replace_view_ops_with_view_copy_ops_pass.ReplaceViewOpsWithViewCopyOpsPass", "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "torch.fx.passes.infra.pass_base.PassBase", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "call_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "op", "args", "kwargs", "meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.ReplaceViewOpsWithViewCopyOpsPass.call_operator", "name": "call_operator", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.ReplaceViewOpsWithViewCopyOpsPass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.ReplaceViewOpsWithViewCopyOpsPass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ExportPassBaseDeprecatedDoNotUse": {".class": "SymbolTableNode", "cross_ref": "torch._export.pass_base._ExportPassBaseDeprecatedDoNotUse", "kind": "Gdef", "module_public": false}, "_NON_FUNCTIONAL_OPS_TO_FUNCTIONAL_OPS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass._NON_FUNCTIONAL_OPS_TO_FUNCTIONAL_OPS", "name": "_NON_FUNCTIONAL_OPS_TO_FUNCTIONAL_OPS", "type": {".class": "Instance", "args": ["torch._ops.OpOverload", "torch._ops.OpOverload"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "get_view_copy_of_view_op": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.get_view_copy_of_view_op", "name": "get_view_copy_of_view_op", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": ["torch._C.FunctionSchema"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_view_copy_of_view_op", "ret_type": {".class": "UnionType", "items": ["torch._ops.OpOverload", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_view_op": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch._export.passes.replace_view_ops_with_view_copy_ops_pass.is_view_op", "name": "is_view_op", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["schema"], "arg_types": ["torch._C.FunctionSchema"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_view_op", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\_export\\passes\\replace_view_ops_with_view_copy_ops_pass.py"}