{".class": "MypyFile", "_fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_dequantize_per_channel_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["y", "scale", "zero_point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks._dequantize_per_channel_backend", "name": "_dequantize_per_channel_backend", "type": null}}, "_dequantize_per_tensor_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["y", "scale", "zero_point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks._dequantize_per_tensor_backend", "name": "_dequantize_per_tensor_backend", "type": null}}, "_get_allgather_out_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["all_gather_in_list", "world_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks._get_allgather_out_list", "name": "_get_allgather_out_list", "type": null}}, "_quantize_per_channel_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "scale", "zero_point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks._quantize_per_channel_backend", "name": "_quantize_per_channel_backend", "type": null}}, "_quantize_per_tensor_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "scale", "zero_point"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks._quantize_per_tensor_backend", "name": "_quantize_per_tensor_backend", "type": null}}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "quantization_perchannel_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["process_group", "bucket", "bucket_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.quantization_perchannel_hook", "name": "quantization_perchannel_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["process_group", "bucket", "bucket_size"], "arg_types": ["torch._C._distributed_c10d.ProcessGroup", "torch._C._distributed_c10d.GradBucket", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quantization_perchannel_hook", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quantization_pertensor_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["process_group", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.algorithms.ddp_comm_hooks.quantization_hooks.quantization_pertensor_hook", "name": "quantization_pertensor_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["process_group", "bucket"], "arg_types": ["torch._C._distributed_c10d.ProcessGroup", "torch._C._distributed_c10d.GradBucket"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quantization_pertensor_hook", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "torch.futures.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\algorithms\\ddp_comm_hooks\\quantization_hooks.py"}