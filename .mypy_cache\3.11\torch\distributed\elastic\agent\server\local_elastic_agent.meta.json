{"data_mtime": 1753783932, "dep_lines": [22, 29, 33, 40, 44, 20, 21, 34, 39, 20, 20, 11, 12, 13, 14, 15, 16, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 10, 10, 5, 5, 20, 20, 10, 10, 10, 10, 10, 10, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.elastic.agent.server.api", "torch.distributed.elastic.agent.server.health_check_server", "torch.distributed.elastic.metrics.api", "torch.distributed.elastic.utils.logging", "torch.distributed.elastic.events.api", "torch.distributed.elastic.timer", "torch.distributed.elastic.events", "torch.distributed.elastic.multiprocessing", "torch.distributed.elastic.utils", "torch.distributed.elastic", "torch.distributed", "json", "os", "signal", "socket", "time", "uuid", "string", "typing", "torch", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "torch.distributed.elastic.metrics", "torch.distributed.elastic.multiprocessing.api", "torch.distributed.elastic.rendezvous", "torch.distributed.elastic.rendezvous.api", "torch.distributed.elastic.timer.api", "torch.distributed.elastic.timer.file_based_local_timer", "typing_extensions"], "hash": "8d7ac94496a2396d200d10c0256767d4c7e9235b", "id": "torch.distributed.elastic.agent.server.local_elastic_agent", "ignore_all": true, "interface_hash": "22691d23255df62b6d7a69216e31aea59f576426", "mtime": 1746804087, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\elastic\\agent\\server\\local_elastic_agent.py", "plugin_data": null, "size": 17158, "suppressed": [], "version_id": "1.15.0"}