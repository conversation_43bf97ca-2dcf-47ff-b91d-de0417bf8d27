{".class": "MypyFile", "_fullname": "torch.distributed.elastic.timer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FileTimerClient": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.file_based_local_timer.FileTimerClient", "kind": "Gdef"}, "FileTimerRequest": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.file_based_local_timer.FileTimerRequest", "kind": "Gdef"}, "FileTimerServer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.file_based_local_timer.FileTimerServer", "kind": "Gdef"}, "LocalTimerClient": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.local_timer.LocalTimerClient", "kind": "Gdef"}, "LocalTimerServer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.local_timer.LocalTimerServer", "kind": "Gdef"}, "TimerClient": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.TimerClient", "kind": "Gdef"}, "TimerRequest": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.TimerRequest", "kind": "Gdef"}, "TimerServer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.TimerServer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "configure": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.configure", "kind": "Gdef"}, "expires": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.expires", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\elastic\\timer\\__init__.py"}