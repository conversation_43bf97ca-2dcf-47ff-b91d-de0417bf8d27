{"data_mtime": 1753783516, "dep_lines": [42, 49, 54, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 30, 30, 30], "dependencies": ["torch.distributed.elastic.timer.api", "torch.distributed.elastic.timer.file_based_local_timer", "torch.distributed.elastic.timer.local_timer", "builtins", "dataclasses", "_frozen_importlib", "abc", "typing"], "hash": "2691fe47df6aaccc9969f2d704b6a6d179044bcd", "id": "torch.distributed.elastic.timer", "ignore_all": true, "interface_hash": "8b00c020860e87c565b59e514f780bd644b1206f", "mtime": 1746804087, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\elastic\\timer\\__init__.py", "plugin_data": null, "size": 1804, "suppressed": [], "version_id": "1.15.0"}