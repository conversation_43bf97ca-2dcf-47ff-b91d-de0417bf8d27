{".class": "MypyFile", "_fullname": "torch.distributed.elastic.timer.local_timer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Empty": {".class": "SymbolTableNode", "cross_ref": "_queue.Empty", "kind": "Gdef", "module_public": false}, "LocalTimerClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.timer.api.TimerClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerClient", "name": "LocalTimerClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerClient", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.timer.local_timer", "mro": ["torch.distributed.elastic.timer.local_timer.LocalTimerClient", "torch.distributed.elastic.timer.api.TimerClient", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mp_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerClient.__init__", "name": "__init__", "type": null}}, "_mp_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerClient._mp_queue", "name": "_mp_queue", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "acquire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "scope_id", "expiration_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerClient.acquire", "name": "acquire", "type": null}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scope_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerClient.release", "name": "release", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.timer.local_timer.LocalTimerClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LocalTimerServer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.timer.api.TimerServer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer", "name": "LocalTimerServer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.timer.local_timer", "mro": ["torch.distributed.elastic.timer.local_timer.LocalTimerServer", "torch.distributed.elastic.timer.api.TimerServer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "mp_queue", "max_interval", "daemon"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "mp_queue", "max_interval", "daemon"], "arg_types": ["torch.distributed.elastic.timer.local_timer.LocalTimerServer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}, "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LocalTimerServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reap_worker": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "worker_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer._reap_worker", "name": "_reap_worker", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "worker_id"], "arg_types": ["torch.distributed.elastic.timer.local_timer.LocalTimerServer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_reap_worker of LocalTimerServer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_timers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer._timers", "name": "_timers", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "torch.distributed.elastic.timer.api.TimerRequest"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "clear_timers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "worker_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer.clear_timers", "name": "clear_timers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "worker_ids"], "arg_types": ["torch.distributed.elastic.timer.local_timer.LocalTimerServer", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_timers of LocalTimerServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_expired_timers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "deadline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer.get_expired_timers", "name": "get_expired_timers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "deadline"], "arg_types": ["torch.distributed.elastic.timer.local_timer.LocalTimerServer", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_expired_timers of LocalTimerServer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["torch.distributed.elastic.timer.api.TimerRequest"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_timers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timer_requests"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer.register_timers", "name": "register_timers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timer_requests"], "arg_types": ["torch.distributed.elastic.timer.local_timer.LocalTimerServer", {".class": "Instance", "args": ["torch.distributed.elastic.timer.api.TimerRequest"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_timers of LocalTimerServer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.timer.local_timer.LocalTimerServer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.timer.local_timer.LocalTimerServer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultiprocessingRequestQueue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.timer.api.RequestQueue"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue", "name": "MultiprocessingRequestQueue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.timer.local_timer", "mro": ["torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue", "torch.distributed.elastic.timer.api.RequestQueue", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mp_queue"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "mp_queue"], "arg_types": ["torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MultiprocessingRequestQueue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mp_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue._mp_queue", "name": "_mp_queue", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "multiprocessing.queues.Queue"}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "size", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "size", "timeout"], "arg_types": ["torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MultiprocessingRequestQueue", "ret_type": {".class": "Instance", "args": ["torch.distributed.elastic.timer.api.TimerRequest"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue.size", "name": "size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "size of MultiprocessingRequestQueue", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.timer.local_timer.MultiprocessingRequestQueue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestQueue": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.RequestQueue", "kind": "Gdef", "module_public": false}, "TimerClient": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.TimerClient", "kind": "Gdef", "module_public": false}, "TimerRequest": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.TimerRequest", "kind": "Gdef", "module_public": false}, "TimerServer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer.api.TimerServer", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.timer.local_timer.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.local_timer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.local_timer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.local_timer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.local_timer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.local_timer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.timer.local_timer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.timer.local_timer.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "mp": {".class": "SymbolTableNode", "cross_ref": "multiprocessing", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\elastic\\timer\\local_timer.py"}