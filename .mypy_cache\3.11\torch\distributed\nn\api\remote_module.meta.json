{"data_mtime": 1753783929, "dep_lines": [14, 14, 15, 11, 17, 18, 7, 11, 12, 3, 4, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.nn.jit.instantiator", "torch.distributed.nn.jit", "torch.distributed.rpc.internal", "torch.distributed.rpc", "torch.nn.parameter", "torch.utils.hooks", "collections.abc", "torch.distributed", "torch.nn", "collections", "io", "sys", "types", "typing", "torch", "builtins", "paddle", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "numpy", "_frozen_importlib", "abc", "torch._C", "torch._C._distributed_rpc", "torch._jit_internal", "torch._tensor", "torch.distributed.remote_device", "torch.distributed.rpc.api", "torch.jit", "torch.jit._script", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "typing_extensions"], "hash": "294ac7fad451dc15519dcc989c91f00e44a693a9", "id": "torch.distributed.nn.api.remote_module", "ignore_all": true, "interface_hash": "7684274902dd5b89c1c1503d18a06090ca2cc223", "mtime": 1746804087, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\nn\\api\\remote_module.py", "plugin_data": null, "size": 32004, "suppressed": [], "version_id": "1.15.0"}