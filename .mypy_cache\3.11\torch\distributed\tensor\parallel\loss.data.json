{".class": "MypyFile", "_fullname": "torch.distributed.tensor.parallel.loss", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef", "module_public": false}, "DTensorSpec": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._dtensor_spec.DTensorSpec", "kind": "Gdef", "module_public": false}, "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Placement": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Placement", "kind": "Gdef", "module_public": false}, "Reduction": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._ops._math_ops.Reduction", "kind": "Gdef", "module_public": false}, "Replicate": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Replicate", "kind": "Gdef", "module_public": false}, "Shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Shard", "kind": "Gdef", "module_public": false}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef", "module_public": false}, "TensorMeta": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._dtensor_spec.TensorMeta", "kind": "Gdef", "module_public": false}, "_MaskPartial": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._ops._embedding_ops._MaskPartial", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.tensor.parallel.loss.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.loss.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.loss.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.loss.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.loss.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.loss.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.loss.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cast_to_dtensor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["tensor", "placements", "mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._cast_to_dtensor", "name": "_cast_to_dtensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["tensor", "placements", "mesh"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cast_to_dtensor", "ret_type": "torch.distributed.tensor._api.DTensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_disable_custom_loss_ops": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._disable_custom_loss_ops", "name": "_disable_custom_loss_ops", "type": null}}, "_enable_custom_loss_ops": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._enable_custom_loss_ops", "name": "_enable_custom_loss_ops", "type": null}}, "_find_all_reduce_mesh_dim": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["placements", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._find_all_reduce_mesh_dim", "name": "_find_all_reduce_mesh_dim", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["placements", "dim"], "arg_types": [{".class": "Instance", "args": ["torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_all_reduce_mesh_dim", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log_softmax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["x", "dim", "half_to_float", "mesh", "mesh_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._log_softmax", "name": "_log_softmax", "type": null}}, "_log_softmax_backward_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._log_softmax_backward_handler", "name": "_log_softmax_backward_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "arg_types": ["torch._ops.OpOverload", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_softmax_backward_handler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log_softmax_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._log_softmax_handler", "name": "_log_softmax_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "arg_types": ["torch._ops.OpOverload", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_softmax_handler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_nll_loss_and_log_softmax_backward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["grad_output", "x", "target", "weight", "reduction", "ignore_index", "total_weight", "input_shape", "channel_dim", "mesh", "mesh_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._nll_loss_and_log_softmax_backward", "name": "_nll_loss_and_log_softmax_backward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["grad_output", "x", "target", "weight", "reduction", "ignore_index", "total_weight", "input_shape", "channel_dim", "mesh", "mesh_dim"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "torch._tensor.Tensor", "torch._<PERSON><PERSON>", "builtins.int", "torch.distributed.device_mesh.DeviceMesh", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_nll_loss_and_log_softmax_backward", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_nll_loss_backward_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._nll_loss_backward_handler", "name": "_nll_loss_backward_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "arg_types": ["torch._ops.OpOverload", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_nll_loss_backward_handler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_nll_loss_forward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["x", "target", "weight", "local_weight", "reduction", "ignore_index", "input_shape", "channel_dim", "mesh", "mesh_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._nll_loss_forward", "name": "_nll_loss_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["x", "target", "weight", "local_weight", "reduction", "ignore_index", "input_shape", "channel_dim", "mesh", "mesh_dim"], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", "torch._<PERSON><PERSON>", "builtins.int", "torch.distributed.device_mesh.DeviceMesh", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_nll_loss_forward", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_nll_loss_forward_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._nll_loss_forward_handler", "name": "_nll_loss_forward_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "arg_types": ["torch._ops.OpOverload", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_nll_loss_forward_handler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_propagate_tensor_meta": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.loss._propagate_tensor_meta", "name": "_propagate_tensor_meta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "arg_types": ["torch._ops.OpOverload", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_propagate_tensor_meta", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.tensor._dtensor_spec.TensorMeta"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_skip_dim": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._ops._math_ops._skip_dim", "kind": "Gdef", "module_public": false}, "aten": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.tensor.parallel.loss.aten", "name": "aten", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "c10d": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.distributed_c10d", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef", "module_public": false}, "customized_loss_ops": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.tensor.parallel.loss.customized_loss_ops", "name": "customized_loss_ops", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["op_call", "args", "kwargs"], "arg_types": ["torch._ops.OpOverload", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "funcol": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._functional_collectives", "kind": "Gdef", "module_public": false}, "loss_parallel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.tensor.parallel.loss.loss_parallel", "name": "loss_parallel", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.loss.loss_parallel", "name": "loss_parallel", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loss_parallel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "replicate_reduction_dims": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._ops._math_ops.replicate_reduction_dims", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "utils": {".class": "SymbolTableNode", "cross_ref": "torch._prims_common", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\loss.py"}