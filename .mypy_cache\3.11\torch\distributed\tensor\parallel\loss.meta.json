{"data_mtime": 1753783928, "dep_lines": [14, 15, 13, 20, 8, 9, 11, 12, 7, 8, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 10, 20, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.tensor._ops._embedding_ops", "torch.distributed.tensor._ops._math_ops", "torch.distributed.tensor._dtensor_spec", "torch.distributed.tensor.placement_types", "torch.distributed._functional_collectives", "torch.distributed.distributed_c10d", "torch.distributed.device_mesh", "torch.distributed.tensor", "torch._prims_common", "torch.distributed", "contextlib", "typing", "torch", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "torch._C", "torch._ops", "torch._tensor", "torch.distributed.tensor._api", "types"], "hash": "68580b82d4968bc2eac56e740f88ae611a5801a7", "id": "torch.distributed.tensor.parallel.loss", "ignore_all": true, "interface_hash": "623c1ab5293c8d9bbe3c03df5d138f941c84ac39", "mtime": 1746804087, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\loss.py", "plugin_data": null, "size": 18181, "suppressed": [], "version_id": "1.15.0"}