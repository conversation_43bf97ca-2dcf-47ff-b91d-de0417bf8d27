{".class": "MypyFile", "_fullname": "torch.distributed.tensor.parallel.style", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "ColwiseParallel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.tensor.parallel.style.ParallelStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel", "name": "ColwiseP<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.tensor.parallel.style", "mro": ["torch.distributed.tensor.parallel.style.ColwiseParallel", "torch.distributed.tensor.parallel.style.ParallelStyle", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "input_layouts", "output_layouts", "use_local_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "input_layouts", "output_layouts", "use_local_output"], "arg_types": ["torch.distributed.tensor.parallel.style.ColwiseParallel", {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ColwiseParallel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.ColwiseParallel", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of ColwiseParallel", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_partition_embedding_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel._partition_embedding_fn", "name": "_partition_embedding_fn", "type": null}}, "_partition_linear_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel._partition_linear_fn", "name": "_partition_linear_fn", "type": null}}, "_prepare_input_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["input_layouts", "desired_input_layouts", "mod", "inputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel._prepare_input_fn", "name": "_prepare_input_fn", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel._prepare_input_fn", "name": "_prepare_input_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["input_layouts", "desired_input_layouts", "mod", "inputs", "device_mesh"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_input_fn of ColwiseParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_prepare_output_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["output_layouts", "use_local_output", "mod", "outputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel._prepare_output_fn", "name": "_prepare_output_fn", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel._prepare_output_fn", "name": "_prepare_output_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["output_layouts", "use_local_output", "mod", "outputs", "device_mesh"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_output_fn of ColwiseParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "desired_input_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel.desired_input_layouts", "name": "desired_input_layouts", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Replicate"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "input_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel.input_layouts", "name": "input_layouts", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "output_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel.output_layouts", "name": "output_layouts", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "use_local_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel.use_local_output", "name": "use_local_output", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.tensor.parallel.style.ColwiseParallel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.tensor.parallel.style.ColwiseParallel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef", "module_public": false}, "DeviceMesh": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.device_mesh.DeviceMesh", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParallelStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_apply", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.tensor.parallel.style.ParallelStyle", "name": "ParallelStyle", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.distributed.tensor.parallel.style.ParallelStyle", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.tensor.parallel.style", "mro": ["torch.distributed.tensor.parallel.style.ParallelStyle", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "torch.distributed.tensor.parallel.style.ParallelStyle._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.ParallelStyle", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of ParallelStyle", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.ParallelStyle._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.ParallelStyle", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of ParallelStyle", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "src_data_rank": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "torch.distributed.tensor.parallel.style.ParallelStyle.src_data_rank", "name": "src_data_rank", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.tensor.parallel.style.ParallelStyle.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.tensor.parallel.style.ParallelStyle", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Placement": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Placement", "kind": "Gdef", "module_public": false}, "PrepareModuleInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.tensor.parallel.style.ParallelStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput", "name": "PrepareModuleInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.tensor.parallel.style", "mro": ["torch.distributed.tensor.parallel.style.PrepareModuleInput", "torch.distributed.tensor.parallel.style.ParallelStyle", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "input_layouts", "desired_input_layouts", "input_kwarg_layouts", "desired_input_kwarg_layouts", "use_local_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["self", "input_layouts", "desired_input_layouts", "input_kwarg_layouts", "desired_input_kwarg_layouts", "use_local_output"], "arg_types": ["torch.distributed.tensor.parallel.style.PrepareModuleInput", {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PrepareModuleInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.PrepareModuleInput", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of PrepareModuleInput", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_input_arg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "input", "mesh", "input_layout", "desired_layout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput._prepare_input_arg", "name": "_prepare_input_arg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "input", "mesh", "input_layout", "desired_layout"], "arg_types": ["torch.distributed.tensor.parallel.style.PrepareModuleInput", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "torch.distributed.device_mesh.DeviceMesh", {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_input_arg of PrepareModuleInput", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_input_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput._prepare_input_fn", "name": "_prepare_input_fn", "type": null}}, "_prepare_input_kwarg_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "inputs", "kwarg_inputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput._prepare_input_kwarg_fn", "name": "_prepare_input_kwarg_fn", "type": null}}, "desired_input_kwarg_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.desired_input_kwarg_layouts", "name": "desired_input_kwarg_layouts", "type": {".class": "Instance", "args": ["builtins.str", "torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "desired_input_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.desired_input_layouts", "name": "desired_input_layouts", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "input_kwarg_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.input_kwarg_layouts", "name": "input_kwarg_layouts", "type": {".class": "Instance", "args": ["builtins.str", "torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "input_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.input_layouts", "name": "input_layouts", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "use_local_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.use_local_output", "name": "use_local_output", "type": "builtins.bool"}}, "with_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.with_kwargs", "name": "with_kwargs", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.tensor.parallel.style.PrepareModuleInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PrepareModuleOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.tensor.parallel.style.ParallelStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput", "name": "PrepareModuleOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.tensor.parallel.style", "mro": ["torch.distributed.tensor.parallel.style.PrepareModuleOutput", "torch.distributed.tensor.parallel.style.ParallelStyle", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "output_layouts", "desired_output_layouts", "use_local_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "output_layouts", "desired_output_layouts", "use_local_output"], "arg_types": ["torch.distributed.tensor.parallel.style.PrepareModuleOutput", {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PrepareModuleOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.PrepareModuleOutput", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of PrepareModuleOutput", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_out_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "outputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput._prepare_out_fn", "name": "_prepare_out_fn", "type": null}}, "desired_output_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput.desired_output_layouts", "name": "desired_output_layouts", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "output_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput.output_layouts", "name": "output_layouts", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "use_local_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput.use_local_output", "name": "use_local_output", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.tensor.parallel.style.PrepareModuleOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.tensor.parallel.style.PrepareModuleOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Replicate": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Replicate", "kind": "Gdef", "module_public": false}, "RowwiseParallel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.tensor.parallel.style.ParallelStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel", "name": "RowwiseParallel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.tensor.parallel.style", "mro": ["torch.distributed.tensor.parallel.style.RowwiseParallel", "torch.distributed.tensor.parallel.style.ParallelStyle", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "input_layouts", "output_layouts", "use_local_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "input_layouts", "output_layouts", "use_local_output"], "arg_types": ["torch.distributed.tensor.parallel.style.RowwiseParallel", {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.tensor.placement_types.Placement", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RowwiseParallel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.RowwiseParallel", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of RowwiseParallel", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_partition_embedding_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel._partition_embedding_fn", "name": "_partition_embedding_fn", "type": null}}, "_partition_linear_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel._partition_linear_fn", "name": "_partition_linear_fn", "type": null}}, "_prepare_input_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["input_layouts", "desired_input_layouts", "mod", "inputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel._prepare_input_fn", "name": "_prepare_input_fn", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel._prepare_input_fn", "name": "_prepare_input_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["input_layouts", "desired_input_layouts", "mod", "inputs", "device_mesh"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_input_fn of RowwiseParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_prepare_output_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["output_layouts", "use_local_output", "mod", "outputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel._prepare_output_fn", "name": "_prepare_output_fn", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel._prepare_output_fn", "name": "_prepare_output_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["output_layouts", "use_local_output", "mod", "outputs", "device_mesh"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_output_fn of RowwiseParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "desired_input_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel.desired_input_layouts", "name": "desired_input_layouts", "type": {".class": "Instance", "args": ["torch.distributed.tensor.placement_types.Placement"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "input_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel.input_layouts", "name": "input_layouts", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "output_layouts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel.output_layouts", "name": "output_layouts", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Placement"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "use_local_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel.use_local_output", "name": "use_local_output", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.tensor.parallel.style.RowwiseParallel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.tensor.parallel.style.RowwiseParallel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SequenceParallel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.tensor.parallel.style.ParallelStyle"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel", "name": "SequenceParallel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.tensor.parallel.style", "mro": ["torch.distributed.tensor.parallel.style.SequenceParallel", "torch.distributed.tensor.parallel.style.ParallelStyle", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "sequence_dim", "use_local_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "sequence_dim", "use_local_output"], "arg_types": ["torch.distributed.tensor.parallel.style.SequenceParallel", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SequenceParallel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel._apply", "name": "_apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.SequenceParallel", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply of SequenceParallel", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_input_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["sequence_sharding", "mod", "inputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel._prepare_input_fn", "name": "_prepare_input_fn", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel._prepare_input_fn", "name": "_prepare_input_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["sequence_sharding", "mod", "inputs", "device_mesh"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_input_fn of SequenceParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_prepare_output_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["use_local_output", "mod", "outputs", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel._prepare_output_fn", "name": "_prepare_output_fn", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel._prepare_output_fn", "name": "_prepare_output_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["use_local_output", "mod", "outputs", "device_mesh"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_output_fn of SequenceParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_replicate_module_fn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "module", "device_mesh"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel._replicate_module_fn", "name": "_replicate_module_fn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "module", "device_mesh"], "arg_types": ["torch.distributed.tensor.parallel.style.SequenceParallel", "builtins.str", "torch.nn.modules.module.Module", "torch.distributed.device_mesh.DeviceMesh"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replicate_module_fn of SequenceParallel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sequence_sharding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel.sequence_sharding", "name": "sequence_sharding", "type": {".class": "TupleType", "implicit": false, "items": ["torch.distributed.tensor.placement_types.Shard"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "use_local_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel.use_local_output", "name": "use_local_output", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.tensor.parallel.style.SequenceParallel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.tensor.parallel.style.SequenceParallel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Shard": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor.placement_types.Shard", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.tensor.parallel.style.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.style.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.style.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.style.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.style.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.style.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.tensor.parallel.style.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "distribute_module": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.distribute_module", "kind": "Gdef", "module_public": false}, "distribute_tensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.distribute_tensor", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\distributed\\tensor\\parallel\\style.py"}