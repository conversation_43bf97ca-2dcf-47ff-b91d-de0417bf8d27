{".class": "MypyFile", "_fullname": "torch.utils.data.datapipes.iter.routeddecoder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BufferedIOBase": {".class": "SymbolTableNode", "cross_ref": "io.BufferedIOBase", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Decoder": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.utils.decoder.Decoder", "kind": "Gdef", "module_public": false}, "IterDataPipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "RoutedDecoderIterDataPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "torch.utils.data.datapipes.datapipe.IterDataPipe"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe", "name": "RoutedDecoderIterDataPipe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe", "has_param_spec_type": false, "metaclass_type": "torch.utils.data.datapipes._typing._IterDataPipeMeta", "metadata": {}, "module_name": "torch.utils.data.datapipes.iter.routeddecoder", "mro": ["torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe", "torch.utils.data.datapipes.datapipe.IterDataPipe", "torch.utils.data.dataset.IterableDataset", "torch.utils.data.dataset.Dataset", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "datapipe", "handlers", "key_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "datapipe", "handlers", "key_fn"], "arg_types": ["torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "io.BufferedIOBase"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RoutedDecoderIterDataPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of RoutedDecoderIterDataPipe", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of RoutedDecoderIterDataPipe", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe.add_handler", "name": "add_handler", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "handler"], "arg_types": ["torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_handler of RoutedDecoderIterDataPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datapipe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe.datapipe", "name": "datapipe", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "io.BufferedIOBase"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe.decoder", "name": "decoder", "type": "torch.utils.data.datapipes.utils.decoder.Decoder"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.iter.routeddecoder.RoutedDecoderIterDataPipe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sized": {".class": "SymbolTableNode", "cross_ref": "typing.Sized", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.iter.routeddecoder.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_deprecation_warning": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.utils.common._deprecation_warning", "kind": "Gdef", "module_public": false}, "decoder_basichandlers": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.utils.decoder.basichandlers", "kind": "Gdef", "module_public": false}, "decoder_imagehandler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.utils.decoder.imagehandler", "kind": "Gdef", "module_public": false}, "extension_extract_fn": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.utils.decoder.extension_extract_fn", "kind": "Gdef", "module_public": false}, "functional_datapipe": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes._decorator.functional_datapipe", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\iter\\routeddecoder.py"}