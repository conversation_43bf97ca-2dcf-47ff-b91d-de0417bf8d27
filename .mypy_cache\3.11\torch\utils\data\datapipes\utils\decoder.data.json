{".class": "MypyFile", "_fullname": "torch.utils.data.datapipes.utils.decoder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Decoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder", "name": "Decoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.utils.decoder", "mro": ["torch.utils.data.datapipes.utils.decoder.Decoder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5], "arg_names": ["self", "handler", "key_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.__init__", "name": "__init__", "type": null}}, "_is_stream_handle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder._is_stream_handle", "name": "_is_stream_handle", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder._is_stream_handle", "name": "_is_stream_handle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_stream_handle of Decoder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.add_handler", "name": "add_handler", "type": null}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.decode", "name": "decode", "type": null}}, "decode1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.decode1", "name": "decode1", "type": null}}, "handlers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.handlers", "name": "handlers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "key_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.key_fn", "name": "key_fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.utils.decoder.Decoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.utils.decoder.Decoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.utils.decoder.ImageHandler", "name": "ImageHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.ImageHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.utils.decoder", "mro": ["torch.utils.data.datapipes.utils.decoder.ImageHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extension", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.ImageHandler.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "imagespec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.ImageHandler.__init__", "name": "__init__", "type": null}}, "imagespec": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.utils.decoder.ImageHandler.imagespec", "name": "imagespec", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.utils.decoder.ImageHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.utils.decoder.ImageHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MatHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.data.datapipes.utils.decoder.MatHandler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.MatHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.data.datapipes.utils.decoder", "mro": ["torch.utils.data.datapipes.utils.decoder.MatHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "extension", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.MatHandler.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "loadmat_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.MatHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "loadmat_kwargs"], "arg_types": ["torch.utils.data.datapipes.utils.decoder.MatHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loadmat_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.utils.decoder.MatHandler.loadmat_kwargs", "name": "loadmat_kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "sio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.data.datapipes.utils.decoder.MatHandler.sio", "name": "sio", "type": {".class": "AnyType", "missing_import_name": "torch.utils.data.datapipes.utils.decoder.MatHandler.sio", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.data.datapipes.utils.decoder.MatHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.data.datapipes.utils.decoder.MatHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamWrapper": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.datapipes.utils.common.StreamWrapper", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.utils.decoder.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.utils.decoder.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.utils.decoder.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.utils.decoder.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.utils.decoder.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.utils.decoder.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.data.datapipes.utils.decoder.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "audiohandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["extension", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.audiohandler", "name": "audiohandler", "type": null}}, "basichandlers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["extension", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.basichandlers", "name": "basichandlers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["extension", "data"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "basichandlers", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extension_extract_fn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pathname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.extension_extract_fn", "name": "extension_extract_fn", "type": null}}, "handle_extension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["extensions", "f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.handle_extension", "name": "handle_extension", "type": null}}, "imagehandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["imagespec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.imagehandler", "name": "imagehandler", "type": null}}, "imagespecs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.data.datapipes.utils.decoder.imagespecs", "name": "imagespecs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "mathandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["loadmat_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.mathandler", "name": "<PERSON><PERSON><PERSON>", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef", "module_public": false}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "videohandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["extension", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.data.datapipes.utils.decoder.videohandler", "name": "videohandler", "type": null}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\utils\\data\\datapipes\\utils\\decoder.py"}