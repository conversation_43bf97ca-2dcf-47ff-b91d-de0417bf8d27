{".class": "MypyFile", "_fullname": "torch.utils.hipify.hipify_python", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CAFFE2_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.CAFFE2_MAP", "name": "CAFFE2_MAP", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "CAFFE2_TRIE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.CAFFE2_TRIE", "name": "CAFFE2_TRIE", "type": "torch.utils.hipify.hipify_python.Trie"}}, "CUDA_TO_HIP_MAPPINGS": {".class": "SymbolTableNode", "cross_ref": "torch.utils.hipify.cuda_to_hip_mappings.CUDA_TO_HIP_MAPPINGS", "kind": "Gdef", "module_public": false}, "CurrentState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.hipify.hipify_python.CurrentState", "name": "CurrentState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.utils.hipify.hipify_python.CurrentState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.utils.hipify.hipify_python", "mro": ["torch.utils.hipify.hipify_python.CurrentState", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.CurrentState.DONE", "name": "DONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "INITIALIZED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.CurrentState.INITIALIZED", "name": "INITIALIZED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.hipify.hipify_python.CurrentState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.hipify.hipify_python.CurrentState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "GeneratedFileCleaner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner", "name": "GeneratedFileCleaner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.hipify.hipify_python", "mro": ["torch.utils.hipify.hipify_python.GeneratedFileCleaner", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "keep_intermediates"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.__init__", "name": "__init__", "type": null}}, "dirs_to_clean": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.dirs_to_clean", "name": "dirs_to_clean", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "files_to_clean": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.files_to_clean", "name": "files_to_clean", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "keep_intermediates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.keep_intermediates", "name": "keep_intermediates", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "makedirs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dn", "exist_ok"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.makedirs", "name": "makedirs", "type": null}}, "open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "fn", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.open", "name": "open", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.hipify.hipify_python.GeneratedFileCleaner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.hipify.hipify_python.GeneratedFileCleaner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HIPIFY_C_BREADCRUMB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.HIPIFY_C_BREADCRUMB", "name": "HIPIFY_C_BREADCRUMB", "type": "builtins.str"}}, "HIPIFY_FINAL_RESULT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.HIPIFY_FINAL_RESULT", "name": "HIPIFY_FINAL_RESULT", "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.utils.hipify.hipify_python.HipifyFinalResult"}}}, "HipifyFinalResult": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.utils.hipify.hipify_python.HipifyFinalResult", "line": 58, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", "torch.utils.hipify.hipify_python.HipifyResult"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "HipifyResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.hipify.hipify_python.HipifyResult", "name": "HipifyResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.HipifyResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.hipify.hipify_python", "mro": ["torch.utils.hipify.hipify_python.HipifyResult", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "current_state", "hipified_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.HipifyResult.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.HipifyResult.__str__", "name": "__str__", "type": null}}, "current_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.HipifyResult.current_state", "name": "current_state", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hipified_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.HipifyResult.hipified_path", "name": "hipified_path", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.HipifyResult.status", "name": "status", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.hipify.hipify_python.HipifyResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.hipify.hipify_python.HipifyResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InputError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.hipify.hipify_python.InputError", "name": "InputError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.InputError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.hipify.hipify_python", "mro": ["torch.utils.hipify.hipify_python.InputError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.InputError.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.InputError.__str__", "name": "__str__", "type": null}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.InputError.message", "name": "message", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.hipify.hipify_python.InputError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.hipify.hipify_python.InputError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "MATH_TRANSPILATIONS": {".class": "SymbolTableNode", "cross_ref": "torch.utils.hipify.cuda_to_hip_mappings.MATH_TRANSPILATIONS", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PYTORCH_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.PYTORCH_MAP", "name": "PYTORCH_MAP", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PYTORCH_SPECIAL_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.PYTORCH_SPECIAL_MAP", "name": "PYTORCH_SPECIAL_MAP", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PYTORCH_TEMPLATE_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.PYTORCH_TEMPLATE_MAP", "name": "PYTORCH_TEMPLATE_MAP", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PYTORCH_TRIE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.PYTORCH_TRIE", "name": "PYTORCH_TRIE", "type": "torch.utils.hipify.hipify_python.Trie"}}, "RE_ANGLE_HEADER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_ANGLE_HEADER", "name": "RE_ANGLE_HEADER", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_ASSERT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_ASSERT", "name": "RE_ASSERT", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_CAFFE2_PREPROCESSOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_CAFFE2_PREPROCESSOR", "name": "RE_CAFFE2_PREPROCESSOR", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_CU_SUFFIX": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_CU_SUFFIX", "name": "RE_CU_SUFFIX", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_EXTERN_SHARED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_EXTERN_SHARED", "name": "RE_EXTERN_SHARED", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_INCLUDE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_INCLUDE", "name": "RE_INCLUDE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_KERNEL_LAUNCH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_KERNEL_LAUNCH", "name": "RE_KERNEL_LAUNCH", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_PYTORCH_PREPROCESSOR": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_PYTORCH_PREPROCESSOR", "name": "RE_PYTORCH_PREPROCESSOR", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_QUOTE_HEADER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_QUOTE_HEADER", "name": "RE_QUOTE_HEADER", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_SYNCTHREADS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_SYNCTHREADS", "name": "RE_SYNCTHREADS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "RE_THC_GENERIC_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.RE_THC_GENERIC_FILE", "name": "RE_THC_GENERIC_FILE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Trie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.hipify.hipify_python.Trie", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.hipify.hipify_python", "mro": ["torch.utils.hipify.hipify_python.Trie", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie.__init__", "name": "__init__", "type": null}}, "_digest": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.Trie._digest", "name": "_digest", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_hash": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.Trie._hash", "name": "_hash", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "root", "digest"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.utils.hipify.hipify_python.Trie._pattern", "name": "_pattern", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.utils.hipify.hipify_python.Trie._pattern", "name": "_pattern", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "word"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie.add", "name": "add", "type": null}}, "dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie.dump", "name": "dump", "type": null}}, "export_to_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie.export_to_regex", "name": "export_to_regex", "type": null}}, "pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie.pattern", "name": "pattern", "type": null}}, "quote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "char"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie.quote", "name": "quote", "type": null}}, "root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.Trie.root", "name": "root", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "word"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.Trie.search", "name": "search", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.hipify.hipify_python.Trie.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.hipify.hipify_python.Trie", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrieNode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.hipify.hipify_python.TrieNode", "name": "TrieNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.TrieNode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.hipify.hipify_python", "mro": ["torch.utils.hipify.hipify_python.TrieNode", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.TrieNode.__init__", "name": "__init__", "type": null}}, "children": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.utils.hipify.hipify_python.TrieNode.children", "name": "children", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.hipify.hipify_python.TrieNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.hipify.hipify_python.TrieNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.hipify.hipify_python.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.hipify.hipify_python.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.hipify.hipify_python.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.hipify.hipify_python.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.hipify.hipify_python.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.utils.hipify.hipify_python.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_fnmatch": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["filepath", "patterns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python._fnmatch", "name": "_fnmatch", "type": null}}, "_to_unix_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python._to_unix_path", "name": "_to_unix_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_unix_path", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_dim3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["kernel_string", "cuda_kernel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.add_dim3", "name": "add_dim3", "type": null}}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "bcolors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.utils.hipify.hipify_python.bcolors", "name": "bcolors", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.bcolors", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.utils.hipify.hipify_python", "mro": ["torch.utils.hipify.hipify_python.bcolors", "builtins.object"], "names": {".class": "SymbolTable", "BOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.BOLD", "name": "BOLD", "type": "builtins.str"}}, "ENDC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.ENDC", "name": "ENDC", "type": "builtins.str"}}, "FAIL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.FAIL", "name": "FAIL", "type": "builtins.str"}}, "HEADER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.HEADER", "name": "HEADER", "type": "builtins.str"}}, "OKBLUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.OKBLUE", "name": "OKBLUE", "type": "builtins.str"}}, "OKGREEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.OKGREEN", "name": "OKGREEN", "type": "builtins.str"}}, "UNDERLINE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.UNDERLINE", "name": "UNDERLINE", "type": "builtins.str"}}, "WARNING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.bcolors.WARNING", "name": "WARNING", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.utils.hipify.hipify_python.bcolors.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.utils.hipify.hipify_python.bcolors", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "compute_stats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.compute_stats", "name": "compute_stats", "type": null}}, "constants": {".class": "SymbolTableNode", "cross_ref": "torch.utils.hipify.constants", "kind": "Gdef", "module_public": false}, "dst": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.dst", "name": "dst", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "extract_arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["start", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.extract_arguments", "name": "extract_arguments", "type": null}}, "file_add_header": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["filepath", "header"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.file_add_header", "name": "file_add_header", "type": null}}, "file_specific_replacement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["filepath", "search_string", "replace_string", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.file_specific_replacement", "name": "file_specific_replacement", "type": null}}, "find_bracket_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["input_string", "start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.find_bracket_group", "name": "find_bracket_group", "type": null}}, "find_closure_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["input_string", "start", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.find_closure_group", "name": "find_closure_group", "type": null}}, "find_parentheses_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["input_string", "start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.find_parentheses_group", "name": "find_parentheses_group", "type": null}}, "fix_static_global_kernels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["in_txt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.fix_static_global_kernels", "name": "fix_static_global_kernels", "type": null}}, "fnmatch": {".class": "SymbolTableNode", "cross_ref": "fnmatch", "kind": "Gdef", "module_public": false}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef", "module_public": false}, "get_hip_file_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["rel_filepath", "is_pytorch_extension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.get_hip_file_path", "name": "get_hip_file_path", "type": null}}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "hip_header_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.hip_header_magic", "name": "hip_header_magic", "type": null}}, "hipify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["project_directory", "show_detailed", "extensions", "header_extensions", "output_directory", "header_include_dirs", "includes", "extra_files", "out_of_place_only", "ignores", "show_progress", "hip_clang_launch", "is_pytorch_extension", "hipify_extra_files_only", "clean_ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.hipify", "name": "hipify", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["project_directory", "show_detailed", "extensions", "header_extensions", "output_directory", "header_include_dirs", "includes", "extra_files", "out_of_place_only", "ignores", "show_progress", "hip_clang_launch", "is_pytorch_extension", "hipify_extra_files_only", "clean_ctx"], "arg_types": ["builtins.str", "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["torch.utils.hipify.hipify_python.GeneratedFileCleaner", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hipify", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.utils.hipify.hipify_python.HipifyFinalResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_caffe2_gpu_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rel_filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.is_caffe2_gpu_file", "name": "is_caffe2_gpu_file", "type": null}}, "is_cusparse_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rel_filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.is_cusparse_file", "name": "is_cusparse_file", "type": null}}, "is_out_of_place": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rel_filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.is_out_of_place", "name": "is_out_of_place", "type": null}}, "is_pytorch_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rel_filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.is_pytorch_file", "name": "is_pytorch_file", "type": null}}, "is_special_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rel_filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.is_special_file", "name": "is_special_file", "type": null}}, "mapping": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "torch.utils.hipify.hipify_python.mapping", "name": "mapping", "type": "builtins.object"}}, "match_extensions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["filename", "extensions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.match_extensions", "name": "match_extensions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["filename", "extensions"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_extensions", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "matched_files_iter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["root_path", "includes", "ignores", "extensions", "out_of_place_only", "is_pytorch_extension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.matched_files_iter", "name": "matched_files_iter", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["root_path", "includes", "ignores", "extensions", "out_of_place_only", "is_pytorch_extension"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matched_files_iter", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "meta_data": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.meta_data", "name": "meta_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "openf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["filename", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.openf", "name": "openf", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "preprocess_file_and_save_result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["output_directory", "filepath", "all_files", "header_include_dirs", "stats", "hip_clang_launch", "is_pytorch_extension", "clean_ctx", "show_progress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.preprocess_file_and_save_result", "name": "preprocess_file_and_save_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["output_directory", "filepath", "all_files", "header_include_dirs", "stats", "hip_clang_launch", "is_pytorch_extension", "clean_ctx", "show_progress"], "arg_types": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", "torch.utils.hipify.hipify_python.GeneratedFileCleaner", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preprocess_file_and_save_result", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preprocessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["output_directory", "filepath", "all_files", "header_include_dirs", "stats", "hip_clang_launch", "is_pytorch_extension", "clean_ctx", "show_progress"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.preprocessor", "name": "preprocessor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["output_directory", "filepath", "all_files", "header_include_dirs", "stats", "hip_clang_launch", "is_pytorch_extension", "clean_ctx", "show_progress"], "arg_types": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", "torch.utils.hipify.hipify_python.GeneratedFileCleaner", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preprocessor", "ret_type": "torch.utils.hipify.hipify_python.HipifyResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "processKernelLaunches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["string", "stats"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.processKernelLaunches", "name": "processKernelLaunches", "type": null}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "replace_extern_shared": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.replace_extern_shared", "name": "replace_extern_shared", "type": null}}, "replace_math_functions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.replace_math_functions", "name": "replace_math_functions", "type": null}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef", "module_public": false}, "src": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.src", "name": "src", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "str2bool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.utils.hipify.hipify_python.str2bool", "name": "str2bool", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "value": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.utils.hipify.hipify_python.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\utils\\hipify\\hipify_python.py"}