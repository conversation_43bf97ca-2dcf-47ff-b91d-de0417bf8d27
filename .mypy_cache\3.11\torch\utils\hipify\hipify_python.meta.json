{"data_mtime": 1753783513, "dep_lines": [34, 35, 34, 39, 27, 28, 29, 30, 31, 32, 38, 41, 42, 43, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 10, 10, 10, 10, 10, 10, 5, 5, 10, 10, 5, 20, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils.hipify.constants", "torch.utils.hipify.cuda_to_hip_mappings", "torch.utils.hipify", "collections.abc", "<PERSON><PERSON><PERSON><PERSON>", "fnmatch", "re", "shutil", "sys", "os", "typing", "enum", "functools", "<PERSON><PERSON><PERSON>", "builtins", "dataclasses", "_frozen_importlib", "_hashlib", "_typeshed", "abc", "types", "typing_extensions"], "hash": "d9016c1ad19bea1f42d4cc348776cde33c176641", "id": "torch.utils.hipify.hipify_python", "ignore_all": true, "interface_hash": "ed8b19cfbbcc53a86dc83a672b578c86f14e53bb", "mtime": 1746804072, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\torch\\utils\\hipify\\hipify_python.py", "plugin_data": null, "size": 48187, "suppressed": [], "version_id": "1.15.0"}