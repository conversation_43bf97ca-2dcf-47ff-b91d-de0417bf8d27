{"data_mtime": 1753783925, "dep_lines": [23, 26, 27, 28, 29, 30, 31, 32, 44, 45, 26, 17, 18, 19, 20, 21, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 55, 56, 54], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["huggingface_hub.utils._deprecation", "transformers.utils.logging", "transformers.utils.import_utils", "transformers.agents.agent_types", "transformers.agents.default_tools", "transformers.agents.llm_engine", "transformers.agents.monitoring", "transformers.agents.prompts", "transformers.agents.python_interpreter", "transformers.agents.tools", "transformers.utils", "json", "logging", "re", "time", "typing", "transformers", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "PIL", "PIL.Image", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "functools", "huggingface_hub", "huggingface_hub.utils", "types", "typing_extensions"], "hash": "518804386b79453640ea09988af8d8b55d528bdf", "id": "transformers.agents.agents", "ignore_all": true, "interface_hash": "5dd7e669466812c873b7807fb57f073d8b6f936a", "mtime": 1746815061, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\agents\\agents.py", "plugin_data": null, "size": 52753, "suppressed": ["pygments.formatters", "pygments.lexers", "pygments"], "version_id": "1.15.0"}