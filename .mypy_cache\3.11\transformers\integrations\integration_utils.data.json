{".class": "MypyFile", "_fullname": "transformers.integrations.integration_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AzureMLCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.AzureMLCallback", "name": "AzureMLCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.AzureMLCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.AzureMLCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "azureml_run"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.AzureMLCallback.__init__", "name": "__init__", "type": null}}, "azureml_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.AzureMLCallback.azureml_run", "name": "azureml_run", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_init_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.AzureMLCallback.on_init_end", "name": "on_init_end", "type": null}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.AzureMLCallback.on_log", "name": "on_log", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.AzureMLCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.AzureMLCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BestRun": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.BestRun", "kind": "Gdef"}, "ClearMLCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.ClearMLCallback", "name": "ClearMLCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.ClearMLCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback.__init__", "name": "__init__", "type": null}}, "_checkpoints_saved": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._checkpoints_saved", "name": "_checkpoints_saved", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_clearml": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._clearml", "name": "_clearml", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_clearml_task": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._clearml_task", "name": "_clearml_task", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_copy_training_args_as_hparams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "training_args", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._copy_training_args_as_hparams", "name": "_copy_training_args_as_hparams", "type": null}}, "_hparams_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._hparams_section", "name": "_hparams_section", "type": "builtins.str"}}, "_ignoge_model_config_overrides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._ignoge_model_config_overrides", "name": "_ignoge_model_config_overrides", "type": "builtins.str"}}, "_ignore_hparams_overrides": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._ignore_hparams_overrides", "name": "_ignore_hparams_overrides", "type": "builtins.str"}}, "_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._initialized", "name": "_initialized", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_log_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._log_model", "name": "_log_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_model_config_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._model_config_description", "name": "_model_config_description", "type": "builtins.str"}}, "_model_config_description_note": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._model_config_description_note", "name": "_model_config_description_note", "type": "builtins.str"}}, "_model_config_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._model_config_section", "name": "_model_config_section", "type": "builtins.str"}}, "_model_connect_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._model_connect_counter", "name": "_model_connect_counter", "type": "builtins.int"}}, "_should_close_on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._should_close_on_train_end", "name": "_should_close_on_train_end", "type": {".class": "NoneType"}}}, "_task_created_in_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._task_created_in_callback", "name": "_task_created_in_callback", "type": "builtins.bool"}}, "_train_run_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback._train_run_counter", "name": "_train_run_counter", "type": "builtins.int"}}, "log_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.ClearMLCallback.log_suffix", "name": "log_suffix", "type": "builtins.str"}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "processing_class", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback.on_log", "name": "on_log", "type": null}}, "on_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback.on_save", "name": "on_save", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "processing_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback.on_train_end", "name": "on_train_end", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "model", "processing_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.ClearMLCallback.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.ClearMLCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.ClearMLCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CodeCarbonCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback", "name": "CodeCarbonCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.CodeCarbonCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback.__init__", "name": "__init__", "type": null}}, "_codecarbon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback._codecarbon", "name": "_codecarbon", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_init_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback.on_init_end", "name": "on_init_end", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback.on_train_end", "name": "on_train_end", "type": null}}, "tracker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback.tracker", "name": "tracker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.CodeCarbonCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.CodeCarbonCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CometCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.CometCallback", "name": "CometCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CometCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.CometCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CometCallback.__init__", "name": "__init__", "type": null}}, "_experiment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.CometCallback._experiment", "name": "_experiment", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.CometCallback._initialized", "name": "_initialized", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_log_assets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.CometCallback._log_assets", "name": "_log_assets", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CometCallback.on_log", "name": "on_log", "type": null}}, "on_predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CometCallback.on_predict", "name": "on_predict", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CometCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CometCallback.on_train_end", "name": "on_train_end", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "args", "state", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.CometCallback.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.CometCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.CometCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DVCLiveCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.DVCLiveCallback", "name": "DVCLiveCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.DVCLiveCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "live", "log_model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "live", "log_model", "kwargs"], "arg_types": ["transformers.integrations.integration_utils.DVCLiveCallback", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DVCLiveCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback._initialized", "name": "_initialized", "type": "builtins.bool"}}, "_log_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback._log_model", "name": "_log_model", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "live": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.live", "name": "live", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.on_log", "name": "on_log", "type": null}}, "on_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.on_save", "name": "on_save", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.on_train_end", "name": "on_train_end", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "args", "state", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.DVCLiveCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.DVCLiveCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DagsHubCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.integrations.integration_utils.MLflowCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.DagsHubCallback", "name": "DagsHubCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DagsHubCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.DagsHubCallback", "transformers.integrations.integration_utils.MLflowCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "Repo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.Repo", "name": "Repo", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.__init__", "name": "__init__", "type": null}}, "log_artifacts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.log_artifacts", "name": "log_artifacts", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.on_train_end", "name": "on_train_end", "type": null}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.path", "name": "path", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "remote": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.remote", "name": "remote", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "repo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.repo", "name": "repo", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.DagsHubCallback.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.DagsHubCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.DagsHubCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ENV_VARS_TRUE_VALUES": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.ENV_VARS_TRUE_VALUES", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "FlyteCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.FlyteCallback", "name": "FlyteCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.FlyteCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.FlyteCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "save_log_history", "sync_checkpoints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.FlyteCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "save_log_history", "sync_checkpoints"], "arg_types": ["transformers.integrations.integration_utils.FlyteCallback", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FlyteCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.FlyteCallback.cp", "name": "cp", "type": {".class": "AnyType", "missing_import_name": "transformers.integrations.integration_utils.FlyteCallback.current_context", "source_any": {".class": "AnyType", "missing_import_name": "transformers.integrations.integration_utils.FlyteCallback.current_context", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "on_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.FlyteCallback.on_save", "name": "on_save", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.FlyteCallback.on_train_end", "name": "on_train_end", "type": null}}, "save_log_history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.FlyteCallback.save_log_history", "name": "save_log_history", "type": "builtins.bool"}}, "sync_checkpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.FlyteCallback.sync_checkpoints", "name": "sync_checkpoints", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.FlyteCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.FlyteCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "INTEGRATION_TO_CALLBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.INTEGRATION_TO_CALLBACK", "name": "INTEGRATION_TO_CALLBACK", "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "IntervalStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.IntervalStrategy", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MLflowCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.MLflowCallback", "name": "MLflowCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.MLflowCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "_MAX_PARAMS_TAGS_PER_BATCH": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._MAX_PARAMS_TAGS_PER_BATCH", "name": "_MAX_PARAMS_TAGS_PER_BATCH", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_MAX_PARAM_VAL_LENGTH": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._MAX_PARAM_VAL_LENGTH", "name": "_MAX_PARAM_VAL_LENGTH", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback.__del__", "name": "__del__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback.__init__", "name": "__init__", "type": null}}, "_async_log": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._async_log", "name": "_async_log", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_auto_end_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._auto_end_run", "name": "_auto_end_run", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_experiment_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._experiment_name", "name": "_experiment_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_flatten_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._flatten_params", "name": "_flatten_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._initialized", "name": "_initialized", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_log_artifacts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._log_artifacts", "name": "_log_artifacts", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_max_log_params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._max_log_params", "name": "_max_log_params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_ml_flow": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._ml_flow", "name": "_ml_flow", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_nested_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._nested_run", "name": "_nested_run", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_run_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._run_id", "name": "_run_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_tracking_uri": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.MLflowCallback._tracking_uri", "name": "_tracking_uri", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "logs", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback.on_log", "name": "on_log", "type": null}}, "on_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback.on_save", "name": "on_save", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback.on_train_end", "name": "on_train_end", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "args", "state", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.MLflowCallback.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.MLflowCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.MLflowCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NeptuneCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.NeptuneCallback", "name": "NeptuneCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.NeptuneCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.__del__", "name": "__del__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "api_token", "project", "name", "base_namespace", "run", "log_parameters", "log_checkpoints", "neptune_run_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "api_token", "project", "name", "base_namespace", "run", "log_parameters", "log_checkpoints", "neptune_run_kwargs"], "arg_types": ["transformers.integrations.integration_utils.NeptuneCallback", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NeptuneCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_base_namespace_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._base_namespace_path", "name": "_base_namespace_path", "type": "builtins.str"}}, "_ensure_at_least_run_without_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._ensure_at_least_run_without_monitoring", "name": "_ensure_at_least_run_without_monitoring", "type": null}}, "_ensure_run_with_monitoring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._ensure_run_with_monitoring", "name": "_ensure_run_with_monitoring", "type": null}}, "_force_reset_monitoring_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._force_reset_monitoring_run", "name": "_force_reset_monitoring_run", "type": "builtins.bool"}}, "_init_run_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._init_run_kwargs", "name": "_init_run_kwargs", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_initial_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._initial_run", "name": "_initial_run", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.integrations.integration_utils.NeptuneCallback.Run", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_initialize_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "additional_neptune_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._initialize_run", "name": "_initialize_run", "type": null}}, "_is_monitoring_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._is_monitoring_run", "name": "_is_monitoring_run", "type": "builtins.bool"}}, "_log_checkpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._log_checkpoints", "name": "_log_checkpoints", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_log_hyper_param_search_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._log_hyper_param_search_parameters", "name": "_log_hyper_param_search_parameters", "type": null}}, "_log_integration_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._log_integration_version", "name": "_log_integration_version", "type": null}}, "_log_model_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_directory", "checkpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._log_model_checkpoint", "name": "_log_model_checkpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_directory", "checkpoint"], "arg_types": ["transformers.integrations.integration_utils.NeptuneCallback", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log_model_checkpoint of NeptuneCallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_log_model_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._log_model_parameters", "name": "_log_model_parameters", "type": null}}, "_log_parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._log_parameters", "name": "_log_parameters", "type": "builtins.bool"}}, "_log_trainer_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._log_trainer_parameters", "name": "_log_trainer_parameters", "type": null}}, "_metadata_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._metadata_namespace", "name": "_metadata_namespace", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._metadata_namespace", "name": "_metadata_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.integrations.integration_utils.NeptuneCallback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_metadata_namespace of NeptuneCallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_recent_checkpoint_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._recent_checkpoint_path", "name": "_recent_checkpoint_path", "type": {".class": "NoneType"}}}, "_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._run", "name": "_run", "type": {".class": "NoneType"}}}, "_run_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._run_id", "name": "_run_id", "type": {".class": "NoneType"}}}, "_should_clean_recently_uploaded_checkpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._should_clean_recently_uploaded_checkpoint", "name": "_should_clean_recently_uploaded_checkpoint", "type": "builtins.bool"}}, "_should_upload_checkpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._should_upload_checkpoint", "name": "_should_upload_checkpoint", "type": "builtins.bool"}}, "_stop_run_if_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._stop_run_if_exists", "name": "_stop_run_if_exists", "type": null}}, "_target_checkpoints_namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._target_checkpoints_namespace", "name": "_target_checkpoints_namespace", "type": "builtins.str"}}, "_use_initial_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._use_initial_run", "name": "_use_initial_run", "type": null}}, "_volatile_checkpoints_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback._volatile_checkpoints_dir", "name": "_volatile_checkpoints_dir", "type": {".class": "NoneType"}}}, "flat_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.flat_metrics", "name": "flat_metrics", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "get_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "trainer"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.get_run", "name": "get_run", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.get_run", "name": "get_run", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "trainer"], "arg_types": [{".class": "TypeType", "item": "transformers.integrations.integration_utils.NeptuneCallback"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_run of NeptuneCallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "integration_version_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.integration_version_key", "name": "integration_version_key", "type": "builtins.str"}}, "model_parameters_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.model_parameters_key", "name": "model_parameters_key", "type": "builtins.str"}}, "on_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.on_evaluate", "name": "on_evaluate", "type": null}}, "on_init_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.on_init_end", "name": "on_init_end", "type": null}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.on_log", "name": "on_log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "logs", "kwargs"], "arg_types": ["transformers.integrations.integration_utils.NeptuneCallback", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_log of NeptuneCallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.on_save", "name": "on_save", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.on_train_end", "name": "on_train_end", "type": null}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.run", "name": "run", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.integrations.integration_utils.NeptuneCallback"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trainer_parameters_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.trainer_parameters_key", "name": "trainer_parameters_key", "type": "builtins.str"}}, "trial_name_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.trial_name_key", "name": "trial_name_key", "type": "builtins.str"}}, "trial_params_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.NeptuneCallback.trial_params_key", "name": "trial_params_key", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.NeptuneCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.NeptuneCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NeptuneMissingConfiguration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.NeptuneMissingConfiguration", "name": "NeptuneMissingConfiguration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneMissingConfiguration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.NeptuneMissingConfiguration", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.NeptuneMissingConfiguration.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.NeptuneMissingConfiguration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.NeptuneMissingConfiguration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PREFIX_CHECKPOINT_DIR": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.PREFIX_CHECKPOINT_DIR", "kind": "Gdef"}, "ParallelMode": {".class": "SymbolTableNode", "cross_ref": "transformers.training_args.ParallelMode", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.dummy_pt_objects.PreTrainedModel", "kind": "Gdef"}, "ProgressCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.ProgressCallback", "kind": "Gdef"}, "PushToHubMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.PushToHubMixin", "kind": "Gdef"}, "SwanLabCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.SwanLabCallback", "name": "SwanLabCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.SwanLabCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback.__init__", "name": "__init__", "type": null}}, "_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.SwanLabCallback._initialized", "name": "_initialized", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_log_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.SwanLabCallback._log_model", "name": "_log_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_swanlab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.SwanLabCallback._swanlab", "name": "_swanlab", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback.on_log", "name": "on_log", "type": null}}, "on_predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback.on_predict", "name": "on_predict", "type": null}}, "on_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback.on_save", "name": "on_save", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "processing_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback.on_train_end", "name": "on_train_end", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.SwanLabCallback.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.SwanLabCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.SwanLabCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.dummy_tf_objects.TFPreTrainedModel", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TensorBoardCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.TensorBoardCallback", "name": "TensorBoardCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.TensorBoardCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "_SummaryWriter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback._SummaryWriter", "name": "_SummaryWriter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "tb_writer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback.__init__", "name": "__init__", "type": null}}, "_init_summary_writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "args", "log_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback._init_summary_writer", "name": "_init_summary_writer", "type": null}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback.on_log", "name": "on_log", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback.on_train_end", "name": "on_train_end", "type": null}}, "tb_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.TensorBoardCallback.tb_writer", "name": "tb_writer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.TensorBoardCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.TensorBoardCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WandbCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.WandbCallback", "name": "WandbCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.WandbCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback.__init__", "name": "__init__", "type": null}}, "_initialized": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.WandbCallback._initialized", "name": "_initialized", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_log_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.WandbCallback._log_model", "name": "_log_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_wandb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.integration_utils.WandbCallback._wandb", "name": "_wandb", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback.on_log", "name": "on_log", "type": null}}, "on_predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback.on_predict", "name": "on_predict", "type": null}}, "on_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback.on_save", "name": "on_save", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "processing_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback.on_train_end", "name": "on_train_end", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.WandbCallback.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.WandbCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.WandbCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WandbLogModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.integration_utils.WandbLogModel", "name": "WandbLogModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.integrations.integration_utils.WandbLogModel", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.integrations.integration_utils", "mro": ["transformers.integrations.integration_utils.WandbLogModel", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CHECKPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.WandbLogModel.CHECKPOINT", "name": "CHECKPOINT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "checkpoint"}, "type_ref": "builtins.str"}}}, "END": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.WandbLogModel.END", "name": "END", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "end"}, "type_ref": "builtins.str"}}}, "FALSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.WandbLogModel.FALSE", "name": "FALSE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "false"}, "type_ref": "builtins.str"}}}, "_missing_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.integrations.integration_utils.WandbLogModel._missing_", "name": "_missing_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": "transformers.integrations.integration_utils.WandbLogModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_missing_ of WandbLogModel", "ret_type": "transformers.integrations.integration_utils.WandbLogModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.integration_utils.WandbLogModel._missing_", "name": "_missing_", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": "transformers.integrations.integration_utils.WandbLogModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_missing_ of WandbLogModel", "ret_type": "transformers.integrations.integration_utils.WandbLogModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_enabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.integrations.integration_utils.WandbLogModel.is_enabled", "name": "is_enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.integrations.integration_utils.WandbLogModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_enabled of WandbLogModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.integrations.integration_utils.WandbLogModel.is_enabled", "name": "is_enabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.integrations.integration_utils.WandbLogModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_enabled of WandbLogModel", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.integration_utils.WandbLogModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.integration_utils.WandbLogModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MIN_COMET_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils._MIN_COMET_VERSION", "name": "_MIN_COMET_VERSION", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.integration_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.integration_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.integration_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.integration_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.integration_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.integration_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_comet_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils._comet_version", "name": "_comet_version", "type": "builtins.str"}}, "_has_neptune": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils._has_neptune", "name": "_has_neptune", "type": "builtins.bool"}}, "_is_comet_configured": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils._is_comet_configured", "name": "_is_comet_configured", "type": "builtins.bool"}}, "_is_comet_installed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils._is_comet_installed", "name": "_is_comet_installed", "type": "builtins.bool"}}, "_is_comet_recent_enough": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils._is_comet_recent_enough", "name": "_is_comet_recent_enough", "type": "builtins.bool"}}, "_neptune_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils._neptune_version", "name": "_neptune_version", "type": "builtins.str"}}, "asdict": {".class": "SymbolTableNode", "cross_ref": "dataclasses.asdict", "kind": "Gdef"}, "comet_ml": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.integrations.integration_utils.comet_ml", "name": "comet_ml", "type": {".class": "AnyType", "missing_import_name": "transformers.integrations.integration_utils.comet_ml", "source_any": null, "type_of_any": 3}}}, "fields": {".class": "SymbolTableNode", "cross_ref": "dataclasses.fields", "kind": "Gdef"}, "flatten_dict": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.flatten_dict", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "get_available_reporting_integrations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.get_available_reporting_integrations", "name": "get_available_reporting_integrations", "type": null}}, "get_reporting_integration_callbacks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["report_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.get_reporting_integration_callbacks", "name": "get_reporting_integration_callbacks", "type": null}}, "hp_params": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.hp_params", "name": "hp_params", "type": null}}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "is_azureml_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_azureml_available", "name": "is_azureml_available", "type": null}}, "is_clearml_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_clearml_available", "name": "is_clearml_available", "type": null}}, "is_codecarbon_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_codecarbon_available", "name": "is_codecarbon_available", "type": null}}, "is_comet_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_comet_available", "name": "is_comet_available", "type": null}}, "is_dagshub_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_dagshub_available", "name": "is_dagshub_available", "type": null}}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_datasets_available", "kind": "Gdef"}, "is_dvclive_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_dvclive_available", "name": "is_dvclive_available", "type": null}}, "is_flyte_deck_standard_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_flyte_deck_standard_available", "name": "is_flyte_deck_standard_available", "type": null}}, "is_flytekit_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_flytekit_available", "name": "is_flytekit_available", "type": null}}, "is_mlflow_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_mlflow_available", "name": "is_mlflow_available", "type": null}}, "is_neptune_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_neptune_available", "name": "is_neptune_available", "type": null}}, "is_optuna_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_optuna_available", "name": "is_optuna_available", "type": null}}, "is_pandas_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_pandas_available", "kind": "Gdef"}, "is_ray_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_ray_available", "name": "is_ray_available", "type": null}}, "is_ray_tune_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_ray_tune_available", "name": "is_ray_tune_available", "type": null}}, "is_sigopt_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_sigopt_available", "name": "is_sigopt_available", "type": null}}, "is_swanlab_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_swanlab_available", "name": "is_swanlab_available", "type": null}}, "is_tensorboard_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_tensorboard_available", "name": "is_tensorboard_available", "type": null}}, "is_tf_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_tf_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "is_torch_xla_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_xla_available", "kind": "Gdef"}, "is_wandb_available": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.is_wandb_available", "name": "is_wandb_available", "type": null}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.integrations.integration_utils.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "modelcard": {".class": "SymbolTableNode", "cross_ref": "transformers.modelcard", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "numbers": {".class": "SymbolTableNode", "cross_ref": "numbers", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "packaging": {".class": "SymbolTableNode", "cross_ref": "packaging", "kind": "Gdef"}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef"}, "rewrite_logs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.rewrite_logs", "name": "rewrite_logs", "type": null}}, "run_hp_search_optuna": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.run_hp_search_optuna", "name": "run_hp_search_optuna", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_hp_search_optuna", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.BestRun"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_hp_search_ray": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.run_hp_search_ray", "name": "run_hp_search_ray", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_hp_search_ray", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.BestRun"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_hp_search_sigopt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.run_hp_search_sigopt", "name": "run_hp_search_sigopt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_hp_search_sigopt", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.BestRun"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_hp_search_wandb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.run_hp_search_wandb", "name": "run_hp_search_wandb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["trainer", "n_trials", "direction", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_hp_search_wandb", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.BestRun"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_model_architecture_to_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "output_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.integration_utils.save_model_architecture_to_file", "name": "save_model_architecture_to_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["model", "output_dir"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_model_architecture_to_file", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "transformers.__version__", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\integrations\\integration_utils.py"}