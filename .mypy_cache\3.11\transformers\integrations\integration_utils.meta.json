{"data_mtime": 1753783925, "dep_lines": [38, 664, 19, 20, 34, 38, 89, 90, 91, 92, 936, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 36, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 828, 1488, 1489, 1180, 1486, 1533, 2047, 1380, 1532, 1530, 1596, 2121, 2122, 63, 669, 789, 1201, 1485, 1718, 1770, 2029, 2045, 2077, 2166], "dep_prios": [10, 20, 10, 10, 10, 5, 10, 5, 5, 5, 20, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 10, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.utils.logging", "torch.utils.tensorboard", "importlib.metadata", "importlib.util", "packaging.version", "transformers.utils", "transformers.modelcard", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "transformers.trainer", "functools", "importlib", "json", "numbers", "os", "pickle", "shutil", "sys", "tempfile", "dataclasses", "enum", "pathlib", "typing", "numpy", "packaging", "transformers", "torch", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "_frozen_importlib", "_typeshed", "abc", "json.decoder", "numpy._typing", "numpy._typing._ufunc", "torch.utils", "torch.utils.tensorboard.writer", "torch.version", "transformers.utils.generic", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "2e3b1db2ac4794577f54f29b63d7f2d6e4142a31", "id": "transformers.integrations.integration_utils", "ignore_all": true, "interface_hash": "7f63470fee22329cc8f99376ed294e914a95eff1", "mtime": 1746815061, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\integrations\\integration_utils.py", "plugin_data": null, "size": 105582, "suppressed": ["wandb.sdk.lib.config_util", "neptune.new.internal.utils", "neptune.new.metadata_containers.run", "azureml.core.run", "neptune.internal.utils", "neptune.new.exceptions", "flytekitplugins.deck.renderer", "dagshub.upload", "neptune.new", "neptune.exceptions", "neptune.utils", "dvclive.plots", "dvclive.utils", "comet_ml", "tensorboardX", "wandb", "mlflow", "neptune", "codecarbon", "clearml", "flytekit", "pandas", "dvclive", "swan<PERSON>b"], "version_id": "1.15.0"}