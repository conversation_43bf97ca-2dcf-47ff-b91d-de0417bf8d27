{"data_mtime": 1753783925, "dep_lines": [10, 13, 14, 1, 2, 3, 4, 5, 7, 9, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["packaging.version", "transformers.modelcard", "transformers.modeling_tf_utils", "logging", "os", "pathlib", "time", "typing", "numpy", "huggingface_hub", "transformers", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "_frozen_importlib", "_typeshed", "abc", "enum", "huggingface_hub._space_api", "huggingface_hub.hf_api", "huggingface_hub.repository", "packaging", "transformers.tokenization_utils_base", "transformers.trainer_utils", "transformers.utils", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "2d2cd8eccadf5b46c334f959cfc1f70aa3ca7b80", "id": "transformers.keras_callbacks", "ignore_all": true, "interface_hash": "a5943ca13bf5d64fd69c79ac56cc598e4c04f1ea", "mtime": 1746815061, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\keras_callbacks.py", "plugin_data": null, "size": 20669, "suppressed": ["tensorflow"], "version_id": "1.15.0"}