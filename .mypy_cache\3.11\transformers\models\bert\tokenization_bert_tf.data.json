{".class": "MypyFile", "_fullname": "transformers.models.bert.tokenization_bert_tf", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BertTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.models.bert.tokenization_bert.BertTokenizer", "kind": "Gdef", "module_public": false}, "BertTokenizerLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.BertTokenizerLayer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.BertTokenizerLayer", "source_any": null, "type_of_any": 3}}}, "FastBertTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.FastBertTokenizer", "name": "FastBertTokenizer", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.FastBertTokenizer", "source_any": null, "type_of_any": 3}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ShrinkLongestTrimmer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.ShrinkLongestTrimmer", "name": "ShrinkLongestTrimmer", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.ShrinkLongestTrimmer", "source_any": null, "type_of_any": 3}}}, "TFBertTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer", "name": "TFBertTokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.bert.tokenization_bert_tf", "mro": ["transformers.models.bert.tokenization_bert_tf.TFBertTokenizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_list", "do_lower_case", "cls_token_id", "sep_token_id", "pad_token_id", "padding", "truncation", "max_length", "pad_to_multiple_of", "return_token_type_ids", "return_attention_mask", "use_fast_bert_tokenizer", "tokenizer_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "vocab_list", "do_lower_case", "cls_token_id", "sep_token_id", "pad_token_id", "padding", "truncation", "max_length", "pad_to_multiple_of", "return_token_type_ids", "return_attention_mask", "use_fast_bert_tokenizer", "tokenizer_kwargs"], "arg_types": ["transformers.models.bert.tokenization_bert_tf.TFBertTokenizer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFBertTokenizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "text", "text_pair", "padding", "truncation", "max_length", "pad_to_multiple_of", "return_token_type_ids", "return_attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.call", "name": "call", "type": null}}, "cls_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.cls_token_id", "name": "cls_token_id", "type": "builtins.int"}}, "do_lower_case": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.do_lower_case", "name": "do_lower_case", "type": "builtins.bool"}}, "from_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "init_inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.from_pretrained", "name": "from_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "init_inputs", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_pretrained of TFBertTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.from_pretrained", "name": "from_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "init_inputs", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_pretrained of TFBertTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "tokenizer", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.from_tokenizer", "name": "from_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "tokenizer", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_tokenizer of TFBertTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.from_tokenizer", "name": "from_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "tokenizer", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_tokenizer of TFBertTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.get_config", "name": "get_config", "type": null}}, "max_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.max_length", "name": "max_length", "type": "builtins.int"}}, "pad_to_multiple_of": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.pad_to_multiple_of", "name": "pad_to_multiple_of", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pad_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.pad_token_id", "name": "pad_token_id", "type": "builtins.int"}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.padding", "name": "padding", "type": "builtins.str"}}, "paired_trimmer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.paired_trimmer", "name": "paired_trimmer", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.ShrinkLongestTrimmer", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.ShrinkLongestTrimmer", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "return_attention_mask": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.return_attention_mask", "name": "return_attention_mask", "type": "builtins.bool"}}, "return_token_type_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.return_token_type_ids", "name": "return_token_type_ids", "type": "builtins.bool"}}, "sep_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.sep_token_id", "name": "sep_token_id", "type": "builtins.int"}}, "tf_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.tf_tokenizer", "name": "tf_tokenizer", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.FastBertTokenizer", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.FastBertTokenizer", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "truncation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.truncation", "name": "truncation", "type": "builtins.bool"}}, "unpaired_tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.unpaired_tokenize", "name": "unpaired_tokenize", "type": null}}, "vocab_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.vocab_list", "name": "vocab_list", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.bert.tokenization_bert_tf.TFBertTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.bert.tokenization_bert_tf.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.bert.tokenization_bert_tf.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.bert.tokenization_bert_tf.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.bert.tokenization_bert_tf.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.bert.tokenization_bert_tf.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.bert.tokenization_bert_tf.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.bert.tokenization_bert_tf.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "case_fold_utf8": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.case_fold_utf8", "name": "case_fold_utf8", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.case_fold_utf8", "source_any": null, "type_of_any": 3}}}, "combine_segments": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.combine_segments", "name": "combine_segments", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.combine_segments", "source_any": null, "type_of_any": 3}}}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pad_model_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.pad_model_inputs", "name": "pad_model_inputs", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.pad_model_inputs", "source_any": null, "type_of_any": 3}}}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.bert.tokenization_bert_tf.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.bert.tokenization_bert_tf.tf", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\bert\\tokenization_bert_tf.py"}