{".class": "MypyFile", "_fullname": "transformers.models.cvt.modeling_tf_cvt", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"int\" and \"Iterable\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.<subclass of \"int\" and \"Iterable\">", "name": "<subclass of \"int\" and \"Iterable\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "transformers.models.cvt.modeling_tf_cvt.<subclass of \"int\" and \"Iterable\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.<subclass of \"int\" and \"Iterable\">", "builtins.int", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CvtConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.cvt.configuration_cvt.CvtConfig", "kind": "Gdef", "module_public": false}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "TFBaseModelOutputWithCLSToken": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", "name": "TFBaseModelOutputWithCLSToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 69, "name": "cls_token_value", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "cls_token_value", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "cls_token_value", "hidden_states"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFBaseModelOutputWithCLSToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "last_hidden_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cls_token_value"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["last_hidden_state", "cls_token_value", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["last_hidden_state", "cls_token_value", "hidden_states"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFBaseModelOutputWithCLSToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["last_hidden_state", "cls_token_value", "hidden_states"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFBaseModelOutputWithCLSToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "cls_token_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.cls_token_value", "name": "cls_token_value", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.hidden_states", "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "last_hidden_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.last_hidden_state", "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCVT_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCVT_INPUTS_DOCSTRING", "name": "TFCVT_INPUTS_DOCSTRING", "type": "builtins.str"}}, "TFCVT_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCVT_START_DOCSTRING", "name": "TFCVT_START_DOCSTRING", "type": "builtins.str"}}, "TFCvtAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention", "name": "TFCvtAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_heads", "embed_dim", "kernel_size", "stride_q", "stride_kv", "padding_q", "padding_kv", "qkv_projection_method", "qkv_bias", "attention_drop_rate", "drop_rate", "with_cls_token", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_heads", "embed_dim", "kernel_size", "stride_q", "stride_kv", "padding_q", "padding_kv", "qkv_projection_method", "qkv_bias", "attention_drop_rate", "drop_rate", "with_cls_token", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtAttention", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.bool", "builtins.float", "builtins.float", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.attention", "name": "attention", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_state", "height", "width", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_state", "height", "width", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtAttention", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtAttention", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.dense_output", "name": "dense_output", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput"}}, "prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.prune_heads", "name": "prune_heads", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtConvEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings", "name": "TFCvtConvEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "patch_size", "num_channels", "embed_dim", "stride", "padding", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "patch_size", "num_channels", "embed_dim", "stride", "padding", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtConvEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtConvEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.embed_dim", "name": "embed_dim", "type": "builtins.int"}}, "normalization": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.normalization", "name": "normalization", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.num_channels", "name": "num_channels", "type": "builtins.int"}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.padding", "name": "padding", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "patch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.patch_size", "name": "patch_size", "type": {".class": "UnionType", "items": ["transformers.models.cvt.modeling_tf_cvt.<subclass of \"int\" and \"Iterable\">", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.projection", "name": "projection", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtDropPath": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath", "name": "TFCvtDropPath", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop_prob", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop_prob", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtDropPath", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtDropPath", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath.drop_prob", "name": "drop_prob", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings", "name": "TFCvtEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "patch_size", "num_channels", "embed_dim", "stride", "padding", "dropout_rate", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "patch_size", "num_channels", "embed_dim", "stride", "padding", "dropout_rate", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convolution_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings.convolution_embeddings", "name": "convolution_embeddings", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtConvEmbeddings"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder", "name": "TFCvtEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder", "transformers.models.cvt.configuration_cvt.CvtConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtEncoder", "ret_type": {".class": "UnionType", "items": ["transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.config", "name": "config", "type": "transformers.models.cvt.configuration_cvt.CvtConfig"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.config_class", "name": "config_class", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["num_channels", "patch_sizes", "patch_stride", "patch_padding", "embed_dim", "num_heads", "depth", "mlp_ratio", "attention_drop_rate", "drop_rate", "drop_path_rate", "qkv_bias", "cls_token", "qkv_projection_method", "kernel_qkv", "padding_kv", "stride_kv", "padding_q", "stride_q", "initializer_range", "layer_norm_eps", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.cvt.configuration_cvt.CvtConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.cvt.configuration_cvt.CvtConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.stages", "name": "stages", "type": {".class": "Instance", "args": ["transformers.models.cvt.modeling_tf_cvt.TFCvtStage"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtForImageClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel", "transformers.modeling_tf_utils.TFSequenceClassificationLoss"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification", "name": "TFCvtForImageClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification", "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification", "transformers.models.cvt.configuration_cvt.CvtConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtForImageClassification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtForImageClassification", "ret_type": {".class": "UnionType", "items": ["transformers.modeling_tf_outputs.TFImageClassifierOutputWithNoAttention", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.classifier", "name": "classifier", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "cvt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.cvt", "name": "cvt", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer"}}, "layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.layernorm", "name": "layernorm", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.num_labels", "name": "num_labels", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtForImageClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtIntermediate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate", "name": "TFCvtIntermediate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "mlp_ratio", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "mlp_ratio", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtIntermediate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtIntermediate", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate.dense", "name": "dense", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate.embed_dim", "name": "embed_dim", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer", "name": "TFCvtLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_heads", "embed_dim", "kernel_size", "stride_q", "stride_kv", "padding_q", "padding_kv", "qkv_projection_method", "qkv_bias", "attention_drop_rate", "drop_rate", "mlp_ratio", "drop_path_rate", "with_cls_token", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_heads", "embed_dim", "kernel_size", "stride_q", "stride_kv", "padding_q", "padding_kv", "qkv_projection_method", "qkv_bias", "attention_drop_rate", "drop_rate", "mlp_ratio", "drop_path_rate", "with_cls_token", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtLayer", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.bool", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.attention", "name": "attention", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtAttention"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_state", "height", "width", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_state", "height", "width", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtLayer", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtLayer", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.dense_output", "name": "dense_output", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.drop_path", "name": "drop_path", "type": {".class": "UnionType", "items": ["transformers.models.cvt.modeling_tf_cvt.TFCvtDropPath", {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.embed_dim", "name": "embed_dim", "type": "builtins.int"}}, "intermediate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.intermediate", "name": "intermediate", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtIntermediate"}}, "layernorm_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.layernorm_after", "name": "layernorm_after", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layernorm_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.layernorm_before", "name": "layernorm_before", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtMainLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer", "name": "TFCvtMainLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer", "transformers.models.cvt.configuration_cvt.CvtConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtMainLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtMainLayer", "ret_type": {".class": "UnionType", "items": ["transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.config", "name": "config", "type": "transformers.models.cvt.configuration_cvt.CvtConfig"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.config_class", "name": "config_class", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["num_channels", "patch_sizes", "patch_stride", "patch_padding", "embed_dim", "num_heads", "depth", "mlp_ratio", "attention_drop_rate", "drop_rate", "drop_path_rate", "qkv_bias", "cls_token", "qkv_projection_method", "kernel_qkv", "padding_kv", "stride_kv", "padding_q", "stride_q", "initializer_range", "layer_norm_eps", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.cvt.configuration_cvt.CvtConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.cvt.configuration_cvt.CvtConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.encoder", "name": "encoder", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtEncoder"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel", "name": "TFCvtModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtModel", "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtModel", "transformers.models.cvt.configuration_cvt.CvtConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtModel", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtModel", "ret_type": {".class": "UnionType", "items": ["transformers.models.cvt.modeling_tf_cvt.TFBaseModelOutputWithCLSToken", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "cvt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel.cvt", "name": "cvt", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtMainLayer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput", "name": "TFCvtOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtOutput", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "mlp_ratio", "drop_rate", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "mlp_ratio", "drop_rate", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtOutput", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "hidden_state", "input_tensor", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "hidden_state", "input_tensor", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtOutput", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtOutput", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.dense", "name": "dense", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.embed_dim", "name": "embed_dim", "type": "builtins.int"}}, "mlp_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.mlp_ratio", "name": "mlp_ratio", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_utils.TFPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel", "name": "TFCvtPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel.config_class", "name": "config_class", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention", "name": "TFCvtSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_heads", "embed_dim", "kernel_size", "stride_q", "stride_kv", "padding_q", "padding_kv", "qkv_projection_method", "qkv_bias", "attention_drop_rate", "with_cls_token", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_heads", "embed_dim", "kernel_size", "stride_q", "stride_kv", "padding_q", "padding_kv", "qkv_projection_method", "qkv_bias", "attention_drop_rate", "with_cls_token", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.bool", "builtins.float", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtSelfAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_state", "height", "width", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "hidden_state", "height", "width", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtSelfAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convolution_projection_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.convolution_projection_key", "name": "convolution_projection_key", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection"}}, "convolution_projection_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.convolution_projection_query", "name": "convolution_projection_query", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection"}}, "convolution_projection_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.convolution_projection_value", "name": "convolution_projection_value", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.embed_dim", "name": "embed_dim", "type": "builtins.int"}}, "num_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.num_heads", "name": "num_heads", "type": "builtins.int"}}, "projection_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.projection_key", "name": "projection_key", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "projection_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.projection_query", "name": "projection_query", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "projection_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.projection_value", "name": "projection_value", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "rearrange_for_multi_head_attention": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.rearrange_for_multi_head_attention", "name": "rearrange_for_multi_head_attention", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rearrange_for_multi_head_attention of TFCvtSelfAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.scale", "name": "scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "with_cls_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.with_cls_token", "name": "with_cls_token", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtSelfAttentionConvProjection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection", "name": "TFCvtSelfAttentionConvProjection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "kernel_size", "stride", "padding", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "kernel_size", "stride", "padding", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtSelfAttentionConvProjection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtSelfAttentionConvProjection", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convolution": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.convolution", "name": "convolution", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.embed_dim", "name": "embed_dim", "type": "builtins.int"}}, "normalization": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.normalization", "name": "normalization", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.padding", "name": "padding", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtSelfAttentionLinearProjection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection", "name": "TFCvtSelfAttentionLinearProjection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection", "builtins.object"], "names": {".class": "SymbolTable", "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtSelfAttentionLinearProjection", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtSelfAttentionProjection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection", "name": "TFCvtSelfAttentionProjection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "embed_dim", "kernel_size", "stride", "padding", "projection_method", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "embed_dim", "kernel_size", "stride", "padding", "projection_method", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtSelfAttentionProjection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtSelfAttentionProjection", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convolution_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection.convolution_projection", "name": "convolution_projection", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionConvProjection"}}, "linear_projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection.linear_projection", "name": "linear_projection", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionLinearProjection"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfAttentionProjection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtSelfOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput", "name": "TFCvtSelfOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "drop_rate", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "config", "embed_dim", "drop_rate", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtSelfOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtSelfOutput", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.dense", "name": "dense", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.embed_dim", "name": "embed_dim", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtSelfOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFCvtStage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage", "name": "TFCvtStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.cvt.modeling_tf_cvt", "mro": ["transformers.models.cvt.modeling_tf_cvt.TFCvtStage", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "stage", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "stage", "kwargs"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtStage", "transformers.models.cvt.configuration_cvt.CvtConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFCvtStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "arg_types": ["transformers.models.cvt.modeling_tf_cvt.TFCvtStage", {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFCvtStage", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cls_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.cls_token", "name": "cls_token", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.config", "name": "config", "type": "transformers.models.cvt.configuration_cvt.CvtConfig"}}, "embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.embedding", "name": "embedding", "type": "transformers.models.cvt.modeling_tf_cvt.TFCvtEmbeddings"}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.layers", "name": "layers", "type": {".class": "Instance", "args": ["transformers.models.cvt.modeling_tf_cvt.TFCvtLayer"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "stage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.stage", "name": "stage", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.cvt.modeling_tf_cvt.TFCvtStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFImageClassifierOutputWithNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFImageClassifierOutputWithNoAttention", "kind": "Gdef", "module_public": false}, "TFModelInputType": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFModelInputType", "kind": "Gdef", "module_public": false}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef", "module_public": false}, "TFSequenceClassificationLoss": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.cvt.modeling_tf_cvt.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "get_initializer": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.get_initializer", "kind": "Gdef", "module_public": false}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef", "module_public": false}, "keras_serializable": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras_serializable", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.cvt.modeling_tf_cvt.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "shape_list": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.shape_list", "kind": "Gdef", "module_public": false}, "stable_softmax": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.stable_softmax", "kind": "Gdef", "module_public": false}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.cvt.modeling_tf_cvt.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.cvt.modeling_tf_cvt.tf", "source_any": null, "type_of_any": 3}}}, "unpack_inputs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.unpack_inputs", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\cvt\\modeling_tf_cvt.py"}