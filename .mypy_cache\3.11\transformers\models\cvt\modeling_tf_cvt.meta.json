{"data_mtime": 1753783924, "dep_lines": [43, 36, 19, 25, 26, 35, 36, 17, 19, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 20, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.cvt.configuration_cvt", "transformers.utils.logging", "collections.abc", "transformers.modeling_tf_outputs", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "collections", "dataclasses", "typing", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "numpy", "_frozen_importlib", "_typeshed", "abc", "transformers.configuration_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "8196e4f8ac3479b05cd10deff79534bfc9465815", "id": "transformers.models.cvt.modeling_tf_cvt", "ignore_all": true, "interface_hash": "23d883eeaa4fa713f5cfd0ad6f6e3bedbc70435f", "mtime": 1746815061, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\cvt\\modeling_tf_cvt.py", "plugin_data": null, "size": 43565, "suppressed": ["tensorflow"], "version_id": "1.15.0"}