{".class": "MypyFile", "_fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"int\" and \"Iterable\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.<subclass of \"int\" and \"Iterable\">", "name": "<subclass of \"int\" and \"Iterable\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.<subclass of \"int\" and \"Iterable\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.<subclass of \"int\" and \"Iterable\">", "builtins.int", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"int\" and \"Iterable\">1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.<subclass of \"int\" and \"Iterable\">1", "name": "<subclass of \"int\" and \"Iterable\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.<subclass of \"int\" and \"Iterable\">1", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.<subclass of \"int\" and \"Iterable\">1", "builtins.int", "typing.Iterable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DATA2VEC_VISION_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.DATA2VEC_VISION_INPUTS_DOCSTRING", "name": "DATA2VEC_VISION_INPUTS_DOCSTRING", "type": "builtins.str"}}, "DATA2VEC_VISION_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.DATA2VEC_VISION_START_DOCSTRING", "name": "DATA2VEC_VISION_START_DOCSTRING", "type": "builtins.str"}}, "Data2VecVisionConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "TFAdaptiveAvgPool2D": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D", "name": "TFAdaptiveAvgPool2D", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "output_dims", "input_ordering", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "output_dims", "input_ordering", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFAdaptiveAvgPool2D", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFAdaptiveAvgPool2D", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "h_axis": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.h_axis", "name": "h_axis", "type": "builtins.int"}}, "input_ordering": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.input_ordering", "name": "input_ordering", "type": "builtins.str"}}, "output_dims": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.output_dims", "name": "output_dims", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "pseudo_1d_pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "h_pooling"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.pseudo_1d_pool", "name": "pseudo_1d_pool", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "h_pooling"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pseudo_1d_pool of TFAdaptiveAvgPool2D", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "w_axis": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.w_axis", "name": "w_axis", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFAdaptiveAvgPool2D", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFBaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFBaseModelOutput", "kind": "Gdef", "module_public": false}, "TFBaseModelOutputWithPooling": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFBaseModelOutputWithPooling", "kind": "Gdef", "module_public": false}, "TFData2VecVisionAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention", "name": "TFData2VecVisionAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.attention", "name": "attention", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "input_tensor", "head_mask", "output_attentions", "relative_position_bias", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "input_tensor", "head_mask", "output_attentions", "relative_position_bias", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "UnionType", "items": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.dense_output", "name": "dense_output", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput"}}, "prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.prune_heads", "name": "prune_heads", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionConvModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule", "name": "TFData2VecVisionConvModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "in_channels", "out_channels", "kernel_size", "padding", "bias", "dilation", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "in_channels", "out_channels", "kernel_size", "padding", "bias", "dilation", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionConvModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "bn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.bn", "name": "bn", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionConvModule", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.conv", "name": "conv", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "in_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.in_channels", "name": "in_channels", "type": "builtins.int"}}, "out_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.out_channels", "name": "out_channels", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionDropPath": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath", "name": "TFData2VecVisionDropPath", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath.__init__", "name": "__init__", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath.call", "name": "call", "type": null}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath.drop_path", "name": "drop_path", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings", "name": "TFData2VecVisionEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cls_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.cls_token", "name": "cls_token", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "mask_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.mask_token", "name": "mask_token", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_patches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.num_patches", "name": "num_patches", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "patch_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.patch_embeddings", "name": "patch_embeddings", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings"}}, "position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.position_embeddings", "name": "position_embeddings", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder", "name": "TFData2VecVisionEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionEncoder", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_tf_outputs.TFBaseModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.layer", "name": "layer", "type": {".class": "Instance", "args": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "relative_position_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.relative_position_bias", "name": "relative_position_bias", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionFCNHead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead", "name": "TFData2VecVisionFCNHead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "config", "in_index", "kernel_size", "dilation", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "config", "in_index", "kernel_size", "dilation", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionFCNHead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoder_hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encoder_hidden_states"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionFCNHead", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.channels", "name": "channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.classifier", "name": "classifier", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "concat_input": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.concat_input", "name": "concat_input", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv_cat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.conv_cat", "name": "conv_cat", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule"}}, "convs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.convs", "name": "convs", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "in_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.in_channels", "name": "in_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "in_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.in_index", "name": "in_index", "type": "builtins.int"}}, "num_convs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.num_convs", "name": "num_convs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionForImageClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "transformers.modeling_tf_utils.TFSequenceClassificationLoss"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification", "name": "TFData2VecVisionForImageClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification", "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionForImageClassification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "head_mask", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "head_mask", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionForImageClassification", "ret_type": {".class": "UnionType", "items": ["transformers.modeling_tf_outputs.TFSequenceClassifierOutput", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.classifier", "name": "classifier", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "data2vec_vision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.data2vec_vision", "name": "data2vec_vision", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer"}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.num_labels", "name": "num_labels", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForImageClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionForSemanticSegmentation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation", "name": "TFData2VecVisionForSemanticSegmentation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation", "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionForSemanticSegmentation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auxiliary_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.auxiliary_head", "name": "auxiliary_head", "type": {".class": "UnionType", "items": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionFCNHead", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "head_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "head_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionForSemanticSegmentation", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_tf_outputs.TFSemanticSegmenterOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "compute_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "logits", "auxiliary_logits", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.compute_loss", "name": "compute_loss", "type": null}}, "data2vec_vision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.data2vec_vision", "name": "data2vec_vision", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer"}}, "decode_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.decode_head", "name": "decode_head", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead"}}, "fpn1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.fpn1", "name": "fpn1", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fpn2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.fpn2", "name": "fpn2", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fpn3": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.fpn3", "name": "fpn3", "type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fpn4": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.fpn4", "name": "fpn4", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.num_labels", "name": "num_labels", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionForSemanticSegmentation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionIntermediate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate", "name": "TFData2VecVisionIntermediate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionIntermediate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionIntermediate", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.dense", "name": "dense", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "intermediate_act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.intermediate_act_fn", "name": "intermediate_act_fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer", "name": "TFData2VecVisionLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "config", "window_size", "drop_path_rate", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "config", "window_size", "drop_path_rate", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.attention", "name": "attention", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionAttention"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of TFData2VecVisionLayer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.built", "name": "built", "type": "builtins.bool"}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "relative_position_bias", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "relative_position_bias", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "UnionType", "items": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionLayer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "data2vec_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.data2vec_output", "name": "data2vec_output", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.drop_path", "name": "drop_path", "type": {".class": "UnionType", "items": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionDropPath", {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "init_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.init_values", "name": "init_values", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.intermediate", "name": "intermediate", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionIntermediate"}}, "lambda_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.lambda_1", "name": "lambda_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "lambda_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.lambda_2", "name": "lambda_2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "layernorm_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.layernorm_after", "name": "layernorm_after", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layernorm_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.layernorm_before", "name": "layernorm_before", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionMainLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer", "name": "TFData2VecVisionMainLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "add_pooling_layer", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "add_pooling_layer", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionMainLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads_to_prune"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer._prune_heads", "name": "_prune_heads", "type": null}}, "add_pooling_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.add_pooling_layer", "name": "add_pooling_layer", "type": "builtins.bool"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos", "head_mask", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos", "head_mask", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionMainLayer", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.config_class", "name": "config_class", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "hidden_act", "hidden_dropout_prob", "attention_probs_dropout_prob", "initializer_range", "layer_norm_eps", "image_size", "patch_size", "num_channels", "use_mask_token", "use_absolute_position_embeddings", "use_relative_position_bias", "use_shared_relative_position_bias", "layer_scale_init_value", "drop_path_rate", "use_mean_pooling", "out_indices", "pool_scales", "use_auxiliary_head", "auxiliary_loss_weight", "auxiliary_channels", "auxiliary_num_convs", "auxiliary_concat_input", "semantic_loss_ignore_index", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.embeddings", "name": "embeddings", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEmbeddings"}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.encoder", "name": "encoder", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionEncoder"}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.get_input_embeddings", "name": "get_input_embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_input_embeddings of TFData2VecVisionMainLayer", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.layernorm", "name": "layernorm", "type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "pooler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.pooler", "name": "pooler", "type": {".class": "UnionType", "items": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel", "name": "TFData2VecVisionModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel", "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "config", "add_pooling_layer", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 2, 4], "arg_names": ["self", "config", "add_pooling_layer", "inputs", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos", "head_mask", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos", "head_mask", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "data2vec_vision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.data2vec_vision", "name": "data2vec_vision", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionMainLayer"}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionModelOutputWithPooling": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_outputs.TFBaseModelOutputWithPooling"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling", "name": "TFData2VecVisionModelOutputWithPooling", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 93, "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 94, "name": "pooler_output", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 95, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 96, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling", "transformers.modeling_tf_outputs.TFBaseModelOutputWithPooling", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "pooler_output", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "pooler_output", "hidden_states", "attentions"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionModelOutputWithPooling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "last_hidden_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pooler_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["last_hidden_state", "pooler_output", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["last_hidden_state", "pooler_output", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFData2VecVisionModelOutputWithPooling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["last_hidden_state", "pooler_output", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFData2VecVisionModelOutputWithPooling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.attentions", "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.hidden_states", "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "last_hidden_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.last_hidden_state", "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pooler_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.pooler_output", "name": "pooler_output", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionModelOutputWithPooling", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput", "name": "TFData2VecVisionOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionOutput", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.dense", "name": "dense", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionPatchEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings", "name": "TFData2VecVisionPatchEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionPatchEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionPatchEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "image_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.image_size", "name": "image_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.num_channels", "name": "num_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_patches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.num_patches", "name": "num_patches", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "patch_shape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.patch_shape", "name": "patch_shape", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "patch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.patch_size", "name": "patch_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.projection", "name": "projection", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPatchEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionPooler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler", "name": "TFData2VecVisionPooler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionPooler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionPooler", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler.layernorm", "name": "layernorm", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPooler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_utils.TFPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "name": "TFData2VecVisionPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "_keys_to_ignore_on_load_unexpected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel._keys_to_ignore_on_load_unexpected", "name": "_keys_to_ignore_on_load_unexpected", "type": null}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel.config_class", "name": "config_class", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionPyramidPoolingModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule", "name": "TFData2VecVisionPyramidPoolingModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "pool_scales", "in_channels", "out_channels", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "pool_scales", "in_channels", "out_channels", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionPyramidPoolingModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionPyramidPoolingModule", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.in_channels", "name": "in_channels", "type": "builtins.int"}}, "layer_list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.layer_list", "name": "layer_list", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "out_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.out_channels", "name": "out_channels", "type": "builtins.int"}}, "pool_scales": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.pool_scales", "name": "pool_scales", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionRelativePositionBias": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", "name": "TFData2VecVisionRelativePositionBias", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionRelativePositionBias", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "inputs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionRelativePositionBias", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "get_position_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.get_position_index", "name": "get_position_index", "type": null}}, "num_relative_distance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.num_relative_distance", "name": "num_relative_distance", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_of_any": 7}}}, "relative_position_bias_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.relative_position_bias_table", "name": "relative_position_bias_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "relative_position_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.relative_position_index", "name": "relative_position_index", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "window_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.window_size", "name": "window_size", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention", "name": "TFData2VecVisionSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionSelfAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.all_head_size", "name": "all_head_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "attention_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.attention_head_size", "name": "attention_head_size", "type": "builtins.int"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "relative_position_bias", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "relative_position_bias", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "UnionType", "items": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionSelfAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.key", "name": "key", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.num_attention_heads", "name": "num_attention_heads", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.query", "name": "query", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "relative_position_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.relative_position_bias", "name": "relative_position_bias", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionRelativePositionBias"}}, "sqrt_att_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.sqrt_att_head_size", "name": "sqrt_att_head_size", "type": "builtins.float"}}, "transpose_for_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensor", "batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.transpose_for_scores", "name": "transpose_for_scores", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tensor", "batch_size"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transpose_for_scores of TFData2VecVisionSelfAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.value", "name": "value", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionSelfOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput", "name": "TFData2VecVisionSelfOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionSelfOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "input_tensor", "gamma", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "input_tensor", "gamma", "training"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionSelfOutput", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.config", "name": "config", "type": "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig"}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.dense", "name": "dense", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionSelfOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFData2VecVisionUperHead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead", "name": "TFData2VecVisionUperHead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.data2vec.modeling_tf_data2vec_vision", "mro": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead", "transformers.models.data2vec.configuration_data2vec_vision.Data2VecVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFData2VecVisionUperHead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bottleneck": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.bottleneck", "name": "bottleneck", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoder_hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encoder_hidden_states"], "arg_types": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead", {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFData2VecVisionUperHead", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.channels", "name": "channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.classifier", "name": "classifier", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fpn_bottleneck": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.fpn_bottleneck", "name": "fpn_bottleneck", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule"}}, "fpn_convs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.fpn_convs", "name": "fpn_convs", "type": {".class": "Instance", "args": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "in_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.in_channels", "name": "in_channels", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "lateral_convs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.lateral_convs", "name": "lateral_convs", "type": {".class": "Instance", "args": ["transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionConvModule"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "pool_scales": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.pool_scales", "name": "pool_scales", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "psp_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.psp_forward", "name": "psp_forward", "type": null}}, "psp_modules": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.psp_modules", "name": "psp_modules", "type": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionPyramidPoolingModule"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.data2vec.modeling_tf_data2vec_vision.TFData2VecVisionUperHead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFModelInputType": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFModelInputType", "kind": "Gdef", "module_public": false}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef", "module_public": false}, "TFSemanticSegmenterOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFSemanticSegmenterOutput", "kind": "Gdef", "module_public": false}, "TFSequenceClassificationLoss": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "kind": "Gdef", "module_public": false}, "TFSequenceClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFSequenceClassifierOutput", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "_EXPECTED_OUTPUT_SHAPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision._EXPECTED_OUTPUT_SHAPE", "name": "_EXPECTED_OUTPUT_SHAPE", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_IMAGE_CLASS_CHECKPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision._IMAGE_CLASS_CHECKPOINT", "name": "_IMAGE_CLASS_CHECKPOINT", "type": "builtins.str"}}, "_IMAGE_CLASS_EXPECTED_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision._IMAGE_CLASS_EXPECTED_OUTPUT", "name": "_IMAGE_CLASS_EXPECTED_OUTPUT", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_code_sample_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_code_sample_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "get_initializer": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.get_initializer", "kind": "Gdef", "module_public": false}, "get_tf_activation": {".class": "SymbolTableNode", "cross_ref": "transformers.activations_tf.get_tf_activation", "kind": "Gdef", "module_public": false}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef", "module_public": false}, "keras_serializable": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras_serializable", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "shape_list": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.shape_list", "kind": "Gdef", "module_public": false}, "stable_softmax": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.stable_softmax", "kind": "Gdef", "module_public": false}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.data2vec.modeling_tf_data2vec_vision.tf", "source_any": null, "type_of_any": 3}}}, "unpack_inputs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.unpack_inputs", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\data2vec\\modeling_tf_data2vec_vision.py"}