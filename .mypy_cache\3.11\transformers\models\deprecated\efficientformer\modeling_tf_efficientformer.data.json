{".class": "MypyFile", "_fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations_tf.ACT2FN", "kind": "Gdef"}, "EFFICIENTFORMER_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.EFFICIENTFORMER_INPUTS_DOCSTRING", "name": "EFFICIENTFORMER_INPUTS_DOCSTRING", "type": "builtins.str"}}, "EFFICIENTFORMER_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.EFFICIENTFORMER_START_DOCSTRING", "name": "EFFICIENTFORMER_START_DOCSTRING", "type": "builtins.str"}}, "EfficientFormerConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "kind": "Gdef"}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TFBaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFBaseModelOutput", "kind": "Gdef"}, "TFBaseModelOutputWithPooling": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFBaseModelOutputWithPooling", "kind": "Gdef"}, "TFEfficientFormerConvMlp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp", "name": "TFEfficientFormerConvMlp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "config", "in_features", "hidden_features", "out_features", "drop", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "config", "in_features", "hidden_features", "out_features", "drop", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerConvMlp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": "transformers.activations_tf.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.activations_tf.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "batchnorm_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.batchnorm_after", "name": "batchnorm_after", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "batchnorm_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.batchnorm_before", "name": "batchnorm_before", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_state", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerConvMlp", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convolution1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.convolution1", "name": "convolution1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "convolution2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.convolution2", "name": "convolution2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "hidden_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.hidden_features", "name": "hidden_features", "type": "builtins.int"}}, "in_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.in_features", "name": "in_features", "type": "builtins.int"}}, "out_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.out_features", "name": "out_features", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerConvStem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem", "name": "TFEfficientFormerConvStem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "out_channels", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "out_channels", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerConvStem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "batchnorm_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.batchnorm_after", "name": "batchnorm_after", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "batchnorm_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.batchnorm_before", "name": "batchnorm_before", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerConvStem", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.config", "name": "config", "type": "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"}}, "convolution1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.convolution1", "name": "convolution1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "convolution2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.convolution2", "name": "convolution2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "out_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.out_channels", "name": "out_channels", "type": "builtins.int"}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.padding", "name": "padding", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerDenseMlp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp", "name": "TFEfficientFormerDenseMlp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "config", "in_features", "hidden_features", "out_features", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "config", "in_features", "hidden_features", "out_features", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerDenseMlp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": "transformers.activations_tf.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.activations_tf.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerDenseMlp", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "hidden_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.hidden_features", "name": "hidden_features", "type": "builtins.int"}}, "in_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.in_features", "name": "in_features", "type": "builtins.int"}}, "linear_in": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.linear_in", "name": "linear_in", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "linear_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.linear_out", "name": "linear_out", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerDropPath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", "name": "TFEfficientFormerDropPath", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop_path", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerDropPath", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerDropPath", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath.drop_path", "name": "drop_path", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder", "name": "TFEfficientFormerEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "output_attentions", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "output_attentions", "return_dict", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerEncoder", "ret_type": "transformers.modeling_tf_outputs.TFBaseModelOutput", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.config", "name": "config", "type": "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"}}, "intermediate_stages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.intermediate_stages", "name": "intermediate_stages", "type": {".class": "Instance", "args": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "last_stage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.last_stage", "name": "last_stage", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerFlat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat", "name": "TFEfficientFormerFlat", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat.__init__", "name": "__init__", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerFlat", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerForImageClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "transformers.modeling_tf_utils.TFSequenceClassificationLoss"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification", "name": "TFEfficientFormerForImageClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification", "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerForImageClassification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerForImageClassification", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "transformers.modeling_tf_outputs.TFImageClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.classifier", "name": "classifier", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "efficientformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.efficientformer", "name": "efficientformer", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer"}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.num_labels", "name": "num_labels", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerForImageClassificationWithTeacher": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher", "name": "TFEfficientFormerForImageClassificationWithTeacher", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher", "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerForImageClassificationWithTeacher", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerForImageClassificationWithTeacher", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.classifier", "name": "classifier", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "distillation_classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.distillation_classifier", "name": "distillation_classifier", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "efficientformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.efficientformer", "name": "efficientformer", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer"}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.num_labels", "name": "num_labels", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacher", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerForImageClassificationWithTeacherOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput", "name": "TFEfficientFormerForImageClassificationWithTeacherOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1090, "name": "logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1091, "name": "cls_logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1092, "name": "distillation_logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1093, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1094, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "logits", "cls_logits", "distillation_logits", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "logits", "cls_logits", "distillation_logits", "hidden_states", "attentions"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerForImageClassificationWithTeacherOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "logits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cls_logits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "distillation_logits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["logits", "cls_logits", "distillation_logits", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["logits", "cls_logits", "distillation_logits", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFEfficientFormerForImageClassificationWithTeacherOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["logits", "cls_logits", "distillation_logits", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFEfficientFormerForImageClassificationWithTeacherOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.attentions", "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cls_logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.cls_logits", "name": "cls_logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "distillation_logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.distillation_logits", "name": "distillation_logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.hidden_states", "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.logits", "name": "logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerForImageClassificationWithTeacherOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerIntermediateStage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage", "name": "TFEfficientFormerIntermediateStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "index", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "index", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerIntermediateStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerIntermediateStage", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "meta4D_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage.meta4D_layers", "name": "meta4D_layers", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerIntermediateStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerLastStage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage", "name": "TFEfficientFormerLastStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerLastStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerLastStage", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flat": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.flat", "name": "flat", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerFlat"}}, "meta3D_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.meta3D_layers", "name": "meta3D_layers", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers"}}, "meta4D_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.meta4D_layers", "name": "meta4D_layers", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerLastStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerMainLayer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer", "name": "TFEfficientFormerMainLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerMainLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerMainLayer", "ret_type": {".class": "UnionType", "items": ["transformers.modeling_tf_outputs.TFBaseModelOutput", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.config", "name": "config", "type": "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.config_class", "name": "config_class", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["depths", "hidden_sizes", "downsamples", "dim", "key_dim", "attention_ratio", "resolution", "num_hidden_layers", "num_attention_heads", "mlp_expansion_ratio", "hidden_dropout_prob", "patch_size", "num_channels", "pool_size", "downsample_patch_size", "downsample_stride", "downsample_pad", "drop_path_rate", "num_meta3d_blocks", "distillation", "use_layer_scale", "layer_scale_init_value", "hidden_act", "initializer_range", "layer_norm_eps", "image_size", "batch_norm_eps", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.int", "builtins.bool", "builtins.bool", "builtins.float", "builtins.str", "builtins.float", "builtins.float", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.encoder", "name": "encoder", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerEncoder"}}, "layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.layernorm", "name": "layernorm", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "patch_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.patch_embed", "name": "patch_embed", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvStem"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerMeta3D": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D", "name": "TFEfficientFormerMeta3D", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "config", "dim", "drop_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "config", "dim", "drop_path", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerMeta3D", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerMeta3D", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.config", "name": "config", "type": "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.dim", "name": "dim", "type": "builtins.int"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.drop_path", "name": "drop_path", "type": {".class": "UnionType", "items": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "layer_scale_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.layer_scale_1", "name": "layer_scale_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_scale_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.layer_scale_2", "name": "layer_scale_2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layernorm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.layernorm1", "name": "layernorm1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layernorm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.layernorm2", "name": "layernorm2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.mlp", "name": "mlp", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDenseMlp"}}, "token_mixer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.token_mixer", "name": "token_mixer", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerMeta3DLayers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers", "name": "TFEfficientFormerMeta3DLayers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerMeta3DLayers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers.blocks", "name": "blocks", "type": {".class": "Instance", "args": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3D"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerMeta3DLayers", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta3DLayers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerMeta4D": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D", "name": "TFEfficientFormerMeta4D", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "config", "dim", "drop_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "config", "dim", "drop_path", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerMeta4D", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerMeta4D", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.config", "name": "config", "type": "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig"}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.dim", "name": "dim", "type": "builtins.int"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.drop_path", "name": "drop_path", "type": {".class": "UnionType", "items": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerDropPath", {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "layer_scale_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.layer_scale_1", "name": "layer_scale_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_scale_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.layer_scale_2", "name": "layer_scale_2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.mlp", "name": "mlp", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerConvMlp"}}, "token_mixer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.token_mixer", "name": "token_mixer", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerMeta4DLayers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers", "name": "TFEfficientFormerMeta4DLayers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "stage_idx", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "stage_idx", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerMeta4DLayers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers.blocks", "name": "blocks", "type": {".class": "Instance", "args": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4D"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerMeta4DLayers", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMeta4DLayers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel", "name": "TFEfficientFormerModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel", "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_tf_outputs.TFBaseModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "efficientformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel.efficientformer", "name": "efficientformer", "type": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerMainLayer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerPatchEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings", "name": "TFEfficientFormerPatchEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_channels", "embed_dim", "apply_norm", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "config", "num_channels", "embed_dim", "apply_norm", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", "builtins.int", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerPatchEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerPatchEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.embed_dim", "name": "embed_dim", "type": "builtins.int"}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.norm", "name": "norm", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.num_channels", "name": "num_channels", "type": "builtins.int"}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.padding", "name": "padding", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.projection", "name": "projection", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPatchEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerPooling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling", "name": "TFEfficientFormerPooling", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "pool_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "pool_size", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerPooling", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerPooling", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling.pool", "name": "pool", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPooling", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_utils.TFPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "name": "TFEfficientFormerPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel.config_class", "name": "config_class", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFEfficientFormerSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention", "name": "TFEfficientFormerSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer", "mro": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "dim", "key_dim", "num_heads", "attention_ratio", "resolution", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "dim", "key_dim", "num_heads", "attention_ratio", "resolution", "config", "kwargs"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "transformers.models.deprecated.efficientformer.configuration_efficientformer.EfficientFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFEfficientFormerSelfAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention_bias_idxs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.attention_bias_idxs", "name": "attention_bias_idxs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "attention_biases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.attention_biases", "name": "attention_biases", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "attention_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.attention_ratio", "name": "attention_ratio", "type": "builtins.int"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.build", "name": "build", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "build of TFEfficientFormerSelfAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.built", "name": "built", "type": "builtins.bool"}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "arg_types": ["transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention", {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFEfficientFormerSelfAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.dim", "name": "dim", "type": "builtins.int"}}, "expanded_key_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.expanded_key_dim", "name": "expanded_key_dim", "type": "builtins.int"}}, "key_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.key_dim", "name": "key_dim", "type": "builtins.int"}}, "num_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.num_heads", "name": "num_heads", "type": "builtins.int"}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.projection", "name": "projection", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "qkv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.qkv", "name": "qkv", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "resolution": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.resolution", "name": "resolution", "type": "builtins.int"}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.scale", "name": "scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "total_expanded_key_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.total_expanded_key_dim", "name": "total_expanded_key_dim", "type": "builtins.int"}}, "total_key_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.total_key_dim", "name": "total_key_dim", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.TFEfficientFormerSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFImageClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFImageClassifierOutput", "kind": "Gdef"}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef"}, "TFSequenceClassificationLoss": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "_EXPECTED_OUTPUT_SHAPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer._EXPECTED_OUTPUT_SHAPE", "name": "_EXPECTED_OUTPUT_SHAPE", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_IMAGE_CLASS_CHECKPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer._IMAGE_CLASS_CHECKPOINT", "name": "_IMAGE_CLASS_CHECKPOINT", "type": "builtins.str"}}, "_IMAGE_CLASS_EXPECTED_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer._IMAGE_CLASS_EXPECTED_OUTPUT", "name": "_IMAGE_CLASS_EXPECTED_OUTPUT", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_code_sample_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_code_sample_docstrings", "kind": "Gdef"}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef"}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "get_initializer": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.get_initializer", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef"}, "keras_serializable": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras_serializable", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "shape_list": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.shape_list", "kind": "Gdef"}, "stable_softmax": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.stable_softmax", "kind": "Gdef"}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.efficientformer.modeling_tf_efficientformer.tf", "source_any": null, "type_of_any": 3}}}, "unpack_inputs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.unpack_inputs", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\deprecated\\efficientformer\\modeling_tf_efficientformer.py"}