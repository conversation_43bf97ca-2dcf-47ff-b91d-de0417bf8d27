{".class": "MypyFile", "_fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TFAdaptiveEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding", "name": "TFAdaptiveEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "n_token", "d_embed", "d_proj", "cutoffs", "div_val", "init_std", "sample_softmax", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.__init__", "name": "__init__", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.call", "name": "call", "type": null}}, "cutoff_ends": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.cutoff_ends", "name": "cutoff_ends", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cutoffs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.cutoffs", "name": "cutoffs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.d_embed", "name": "d_embed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.d_proj", "name": "d_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "div_val": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.div_val", "name": "div_val", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "emb_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.emb_layers", "name": "emb_layers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "emb_projs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.emb_projs", "name": "emb_projs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "emb_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.emb_scale", "name": "emb_scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_std": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.init_std", "name": "init_std", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.n_token", "name": "n_token", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFAdaptiveEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFAdaptiveSoftmaxMask": {".class": "SymbolTableNode", "cross_ref": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities.TFAdaptiveSoftmaxMask", "kind": "Gdef"}, "TFModelInputType": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFModelInputType", "kind": "Gdef"}, "TFPositionalEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding", "name": "TFPositionalEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "demb", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding.__init__", "name": "__init__", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pos_seq", "bsz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding.call", "name": "call", "type": null}}, "inv_freq": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding.inv_freq", "name": "inv_freq", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionalEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFPositionwiseFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF", "name": "TFPositionwiseFF", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "d_model", "d_inner", "dropout", "pre_lnorm", "layer_norm_epsilon", "init_std", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.__init__", "name": "__init__", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "inp", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.call", "name": "call", "type": null}}, "d_inner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.d_inner", "name": "d_inner", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.d_model", "name": "d_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "drop_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.drop_1", "name": "drop_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "drop_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.drop_2", "name": "drop_2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.layer_1", "name": "layer_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.layer_2", "name": "layer_2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.layer_norm", "name": "layer_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pre_lnorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.pre_lnorm", "name": "pre_lnorm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFPositionwiseFF", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef"}, "TFRelPartialLearnableDecoderLayer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer", "name": "TFRelPartialLearnableDecoderLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "n_head", "d_model", "d_head", "d_inner", "dropout", "dropatt", "pre_lnorm", "r_w_bias", "r_r_bias", "layer_norm_epsilon", "init_std", "output_attentions", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer.__init__", "name": "__init__", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "dec_inp", "r", "dec_attn_mask", "mems", "head_mask", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer.call", "name": "call", "type": null}}, "dec_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer.dec_attn", "name": "dec_attn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_ff": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer.pos_ff", "name": "pos_ff", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableDecoderLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFRelPartialLearnableMultiHeadAttn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn", "name": "TFRelPartialLearnableMultiHeadAttn", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "n_head", "d_model", "d_head", "dropout", "dropatt", "pre_lnorm", "r_r_bias", "r_w_bias", "layer_norm_epsilon", "init_std", "output_attentions", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.__init__", "name": "__init__", "type": null}}, "_rel_shift": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn._rel_shift", "name": "_rel_shift", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "w", "r", "attn_mask", "mems", "head_mask", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.call", "name": "call", "type": null}}, "d_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.d_head", "name": "d_head", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.d_model", "name": "d_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "drop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.drop", "name": "drop", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropatt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.dropatt", "name": "dropatt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.layer_norm", "name": "layer_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.n_head", "name": "n_head", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "o_net": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.o_net", "name": "o_net", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_attentions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.output_attentions", "name": "output_attentions", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pre_lnorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.pre_lnorm", "name": "pre_lnorm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "qkv_net": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.qkv_net", "name": "qkv_net", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "r_net": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.r_net", "name": "r_net", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "r_r_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.r_r_bias", "name": "r_r_bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "r_w_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.r_w_bias", "name": "r_w_bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.scale", "name": "scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFRelPartialLearnableMultiHeadAttn", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSequenceClassificationLoss": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "kind": "Gdef"}, "TFTransfoEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings", "name": "TFTransfoEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "vocab_size", "emb_size", "init_std", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.__init__", "name": "__init__", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.call", "name": "call", "type": null}}, "emb_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.emb_size", "name": "emb_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_std": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.init_std", "name": "init_std", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.vocab_size", "name": "vocab_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.weight", "name": "weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLForSequenceClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "transformers.modeling_tf_utils.TFSequenceClassificationLoss"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification", "name": "TFTransfoXLForSequenceClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification", "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.modeling_tf_utils.TFSequenceClassificationLoss", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.__init__", "name": "__init__", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "arg_types": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFTransfoXLForSequenceClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.get_output_embeddings", "name": "get_output_embeddings", "type": null}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.num_labels", "name": "num_labels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "score": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.score", "name": "score", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "transformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.transformer", "name": "transformer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLForSequenceClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLLMHeadModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel", "name": "TFTransfoXLLMHeadModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel", "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.__init__", "name": "__init__", "type": null}}, "_resize_token_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_num_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel._resize_token_embeddings", "name": "_resize_token_embeddings", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "arg_types": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFTransfoXLLMHeadModel", "ret_type": {".class": "UnionType", "items": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "crit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.crit", "name": "crit", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.get_output_embeddings", "name": "get_output_embeddings", "type": null}}, "init_mems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bsz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.init_mems", "name": "init_mems", "type": null}}, "prepare_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "input_ids", "past_key_values", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.prepare_inputs_for_generation", "name": "prepare_inputs_for_generation", "type": null}}, "reset_memory_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mem_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.reset_memory_length", "name": "reset_memory_length", "type": null}}, "sample_softmax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.sample_softmax", "name": "sample_softmax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tf_to_pt_weight_rename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tf_weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.tf_to_pt_weight_rename", "name": "tf_to_pt_weight_rename", "type": null}}, "transformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.transformer", "name": "transformer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLLMHeadModelOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput", "name": "TFTransfoXLLMHeadModelOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 726, "name": "prediction_scores", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 727, "name": "mems", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 728, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 729, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "prediction_scores", "mems", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "prediction_scores", "mems", "hidden_states", "attentions"], "arg_types": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFTransfoXLLMHeadModelOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "prediction_scores"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mems"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["prediction_scores", "mems", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["prediction_scores", "mems", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFTransfoXLLMHeadModelOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["prediction_scores", "mems", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFTransfoXLLMHeadModelOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.attentions", "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.hidden_states", "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "mems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.mems", "name": "mems", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "prediction_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.prediction_scores", "name": "prediction_scores", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLLMHeadModelOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLMainLayer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer", "name": "TFTransfoXLMainLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.__init__", "name": "__init__", "type": null}}, "_prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer._prune_heads", "name": "_prune_heads", "type": null}}, "_update_mems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "hids", "mems", "mlen", "qlen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer._update_mems", "name": "_update_mems", "type": null}}, "attn_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.attn_type", "name": "attn_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "backward_compatible": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.backward_compatible", "name": "backward_compatible", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "labels", "training"], "arg_types": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFTransfoXLMainLayer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "clamp_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.clamp_len", "name": "clamp_len", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.config_class", "name": "config_class", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "cutoffs", "d_model", "d_embed", "n_head", "d_head", "d_inner", "div_val", "pre_lnorm", "n_layer", "mem_len", "clamp_len", "same_length", "proj_share_all_but_first", "attn_type", "sample_softmax", "adaptive", "dropout", "dropatt", "untie_r", "init", "init_range", "proj_init_std", "init_std", "layer_norm_epsilon", "eos_token_id", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.deprecated.transfo_xl.configuration_transfo_xl.TransfoXLConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.deprecated.transfo_xl.configuration_transfo_xl.TransfoXLConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "d_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.d_embed", "name": "d_embed", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.d_head", "name": "d_head", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "d_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.d_model", "name": "d_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "drop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.drop", "name": "drop", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "init_mems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bsz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.init_mems", "name": "init_mems", "type": null}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.layers", "name": "layers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mem_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.mem_len", "name": "mem_len", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.n_head", "name": "n_head", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.n_layer", "name": "n_layer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "n_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.n_token", "name": "n_token", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_attentions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.output_attentions", "name": "output_attentions", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_hidden_states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.output_hidden_states", "name": "output_hidden_states", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_emb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.pos_emb", "name": "pos_emb", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "r_r_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.r_r_bias", "name": "r_r_bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "r_w_bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.r_w_bias", "name": "r_w_bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reset_memory_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mem_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.reset_memory_length", "name": "reset_memory_length", "type": null}}, "return_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.return_dict", "name": "return_dict", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "same_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.same_length", "name": "same_length", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sample_softmax": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.sample_softmax", "name": "sample_softmax", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.set_input_embeddings", "name": "set_input_embeddings", "type": null}}, "untie_r": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.untie_r", "name": "untie_r", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "word_emb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.word_emb", "name": "word_emb", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLMainLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel", "name": "TFTransfoXLModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel", "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel.__init__", "name": "__init__", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "mems", "head_mask", "inputs_embeds", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFTransfoXLModel", "ret_type": {".class": "UnionType", "items": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "transformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel.transformer", "name": "transformer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLModelOutput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput", "name": "TFTransfoXLModelOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 693, "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 694, "name": "mems", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 695, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 696, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "mems", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "last_hidden_state", "mems", "hidden_states", "attentions"], "arg_types": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFTransfoXLModelOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "last_hidden_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mems"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["last_hidden_state", "mems", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["last_hidden_state", "mems", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFTransfoXLModelOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["last_hidden_state", "mems", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFTransfoXLModelOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.attentions", "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.hidden_states", "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "last_hidden_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.last_hidden_state", "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "mems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.mems", "name": "mems", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLModelOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_utils.TFPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "name": "TFTransfoXLPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel.config_class", "name": "config_class", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFTransfoXLSequenceClassifierOutputWithPast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast", "name": "TFTransfoXLSequenceClassifierOutputWithPast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 759, "name": "loss", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 760, "name": "logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 761, "name": "mems", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 762, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 763, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl", "mro": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "loss", "logits", "mems", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "loss", "logits", "mems", "hidden_states", "attentions"], "arg_types": ["transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFTransfoXLSequenceClassifierOutputWithPast", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "loss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mems"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "mems", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "mems", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFTransfoXLSequenceClassifierOutputWithPast", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["loss", "logits", "mems", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFTransfoXLSequenceClassifierOutputWithPast", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.attentions", "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.hidden_states", "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "logits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.logits", "name": "logits", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.loss", "name": "loss", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "mems": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.mems", "name": "mems", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TFTransfoXLSequenceClassifierOutputWithPast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TRANSFO_XL_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TRANSFO_XL_INPUTS_DOCSTRING", "name": "TRANSFO_XL_INPUTS_DOCSTRING", "type": "builtins.str"}}, "TRANSFO_XL_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.TRANSFO_XL_START_DOCSTRING", "name": "TRANSFO_XL_START_DOCSTRING", "type": "builtins.str"}}, "TransfoXLConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.deprecated.transfo_xl.configuration_transfo_xl.TransfoXLConfig", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_code_sample_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_code_sample_docstrings", "kind": "Gdef"}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef"}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "get_initializer": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.get_initializer", "kind": "Gdef"}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef"}, "keras_serializable": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras_serializable", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "shape_list": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.shape_list", "kind": "Gdef"}, "stable_softmax": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.stable_softmax", "kind": "Gdef"}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl.tf", "source_any": null, "type_of_any": 3}}}, "unpack_inputs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.unpack_inputs", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\deprecated\\transfo_xl\\modeling_tf_transfo_xl.py"}