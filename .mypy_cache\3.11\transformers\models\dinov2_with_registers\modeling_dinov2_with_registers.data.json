{".class": "MypyFile", "_fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef", "module_public": false}, "ALL_ATTENTION_FUNCTIONS": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.ALL_ATTENTION_FUNCTIONS", "kind": "Gdef", "module_public": false}, "BCEWithLogitsLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.BCEWithLogitsLoss", "kind": "Gdef", "module_public": false}, "BackboneMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.backbone_utils.BackboneMixin", "kind": "Gdef", "module_public": false}, "BackboneOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BackboneOutput", "kind": "Gdef", "module_public": false}, "BaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutput", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithPooling": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithPooling", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "DINOV2_WITH_REGISTERS_BASE_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.DINOV2_WITH_REGISTERS_BASE_INPUTS_DOCSTRING", "name": "DINOV2_WITH_REGISTERS_BASE_INPUTS_DOCSTRING", "type": "builtins.str"}}, "DINOV2_WITH_REGISTERS_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.DINOV2_WITH_REGISTERS_INPUTS_DOCSTRING", "name": "DINOV2_WITH_REGISTERS_INPUTS_DOCSTRING", "type": "builtins.str"}}, "DINOV2_WITH_REGISTERS_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.DINOV2_WITH_REGISTERS_START_DOCSTRING", "name": "DINOV2_WITH_REGISTERS_START_DOCSTRING", "type": "builtins.str"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Dinov2WithRegistersAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention", "name": "Dinov2WithRegistersAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention.attention", "name": "attention", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersAttention", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention.output", "name": "output", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput"}}, "prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention.prune_heads", "name": "prune_heads", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prune_heads of Dinov2WithRegistersAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pruned_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention.pruned_heads", "name": "pruned_heads", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersBackbone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "transformers.utils.backbone_utils.BackboneMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone", "name": "Dinov2WithRegistersBackbone", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone", "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "transformers.utils.backbone_utils.BackboneMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.__init__", "name": "__init__", "type": null}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.embeddings", "name": "embeddings", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.encoder", "name": "encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "output_attentions", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "output_attentions", "return_dict"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone", "torch._tensor.Tensor", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersBackbone", "ret_type": "transformers.modeling_outputs.BackboneOutput", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.get_input_embeddings", "name": "get_input_embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_input_embeddings of Dinov2WithRegistersBackbone", "ret_type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.layernorm", "name": "layernorm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_register_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.num_register_tokens", "name": "num_register_tokens", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersBackbone", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig", "kind": "Gdef", "module_public": false}, "Dinov2WithRegistersDropPath": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath", "name": "Dinov2WithRegistersDropPath", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "drop_prob"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "drop_prob"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersDropPath", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath.drop_prob", "name": "drop_prob", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "extra_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath.extra_repr", "name": "extra_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_repr of Dinov2WithRegistersDropPath", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersDropPath", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings", "name": "Dinov2WithRegistersEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cls_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.cls_token", "name": "cls_token", "type": "torch.nn.parameter.Parameter"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.config", "name": "config", "type": "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.dropout", "name": "dropout", "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersEmbeddings", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "interpolate_pos_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "embeddings", "height", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.interpolate_pos_encoding", "name": "interpolate_pos_encoding", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "embeddings", "height", "width"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings", "torch._tensor.Tensor", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "interpolate_pos_encoding of Dinov2WithRegistersEmbeddings", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mask_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.mask_token", "name": "mask_token", "type": "torch.nn.parameter.Parameter"}}, "patch_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.patch_embeddings", "name": "patch_embeddings", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings"}}, "patch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.patch_size", "name": "patch_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.position_embeddings", "name": "position_embeddings", "type": "torch.nn.parameter.Parameter"}}, "register_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.register_tokens", "name": "register_tokens", "type": "torch.nn.parameter.Parameter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder", "name": "Dinov2WithRegistersEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder.config", "name": "config", "type": "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersEncoder", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.BaseModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "type": "builtins.bool"}}, "layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder.layer", "name": "layer", "type": "torch.nn.modules.container.ModuleList"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersForImageClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification", "name": "Dinov2WithRegistersForImageClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification", "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersForImageClassification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification.classifier", "name": "classifier", "type": {".class": "UnionType", "items": ["torch.nn.modules.linear.Linear", "torch.nn.modules.linear.Identity"], "uses_pep604_syntax": false}}}, "dinov2_with_registers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification.dinov2_with_registers", "name": "dinov2_with_registers", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "head_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "head_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersForImageClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.ImageClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification.num_labels", "name": "num_labels", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersForImageClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer", "name": "Dinov2WithRegistersLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.attention", "name": "attention", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersAttention"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.drop_path", "name": "drop_path", "type": {".class": "UnionType", "items": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersDropPath", "torch.nn.modules.linear.Identity"], "uses_pep604_syntax": false}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersLayer", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layer_scale1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.layer_scale1", "name": "layer_scale1", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale"}}, "layer_scale2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.layer_scale2", "name": "layer_scale2", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale"}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.mlp", "name": "mlp", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN"}}, "norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.norm1", "name": "norm1", "type": "torch.nn.modules.normalization.LayerNorm"}}, "norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.norm2", "name": "norm2", "type": "torch.nn.modules.normalization.LayerNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersLayerScale": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale", "name": "Dinov2WithRegistersLayerScale", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersLayerScale", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersLayerScale", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lambda1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale.lambda1", "name": "lambda1", "type": "torch.nn.parameter.Parameter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersLayerScale", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersMLP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP", "name": "Dinov2WithRegistersMLP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersMLP", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fc1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP.fc1", "name": "fc1", "type": "torch.nn.modules.linear.Linear"}}, "fc2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP.fc2", "name": "fc2", "type": "torch.nn.modules.linear.Linear"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersMLP", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersMLP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel", "name": "Dinov2WithRegistersModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel", "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads_to_prune"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel._prune_heads", "name": "_prune_heads", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "heads_to_prune"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel", {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prune_heads of Dinov2WithRegistersModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.embeddings", "name": "embeddings", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEmbeddings"}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.encoder", "name": "encoder", "type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersEncoder"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos", "head_mask", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "bool_masked_pos", "head_mask", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.BaseModelOutputWithPooling"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.get_input_embeddings", "name": "get_input_embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_input_embeddings of Dinov2WithRegistersModel", "ret_type": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.layernorm", "name": "layernorm", "type": "torch.nn.modules.normalization.LayerNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersPatchEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings", "name": "Dinov2WithRegistersPatchEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersPatchEmbeddings", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "image_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.image_size", "name": "image_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.num_channels", "name": "num_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_patches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.num_patches", "name": "num_patches", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "patch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.patch_size", "name": "patch_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.projection", "name": "projection", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPatchEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "name": "Dinov2WithRegistersPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel._init_weights", "name": "_init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "module"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", {".class": "UnionType", "items": ["torch.nn.modules.linear.Linear", "torch.nn.modules.conv.Conv2d", "torch.nn.modules.normalization.LayerNorm"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_weights of Dinov2WithRegistersPreTrainedModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel._no_split_modules", "name": "_no_split_modules", "type": null}}, "_supports_flash_attn_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel._supports_flash_attn_2", "name": "_supports_flash_attn_2", "type": "builtins.bool"}}, "_supports_sdpa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel._supports_sdpa", "name": "_supports_sdpa", "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel.config_class", "name": "config_class", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention", "name": "Dinov2WithRegistersSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersSelfAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.all_head_size", "name": "all_head_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "attention_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.attention_head_size", "name": "attention_head_size", "type": "builtins.int"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.config", "name": "config", "type": "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"}}, "dropout_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.dropout_prob", "name": "dropout_prob", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "head_mask", "output_attentions"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersSelfAttention", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.is_causal", "name": "is_causal", "type": "builtins.bool"}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.key", "name": "key", "type": "torch.nn.modules.linear.Linear"}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.num_attention_heads", "name": "num_attention_heads", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.query", "name": "query", "type": "torch.nn.modules.linear.Linear"}}, "scaling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.scaling", "name": "scaling", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "transpose_for_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.transpose_for_scores", "name": "transpose_for_scores", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "transpose_for_scores of Dinov2WithRegistersSelfAttention", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.value", "name": "value", "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersSelfOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput", "name": "Dinov2WithRegistersSelfOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput", "transformers.models.dinov2_with_registers.configuration_dinov2_with_registers.Dinov2WithRegistersConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersSelfOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput.dense", "name": "dense", "type": "torch.nn.modules.linear.Linear"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput.dropout", "name": "dropout", "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersSelfOutput", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSelfOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dinov2WithRegistersSwiGLUFFN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN", "name": "Dinov2WithRegistersSwiGLUFFN", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers", "mro": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Dinov2WithRegistersSwiGLUFFN", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Dinov2WithRegistersSwiGLUFFN", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weights_in": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN.weights_in", "name": "weights_in", "type": "torch.nn.modules.linear.Linear"}}, "weights_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN.weights_out", "name": "weights_out", "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.Dinov2WithRegistersSwiGLUFFN", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.ImageClassifierOutput", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "MSELoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.MSELoss", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "_EXPECTED_OUTPUT_SHAPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers._EXPECTED_OUTPUT_SHAPE", "name": "_EXPECTED_OUTPUT_SHAPE", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_IMAGE_CLASS_CHECKPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers._IMAGE_CLASS_CHECKPOINT", "name": "_IMAGE_CLASS_CHECKPOINT", "type": "builtins.str"}}, "_IMAGE_CLASS_EXPECTED_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers._IMAGE_CLASS_EXPECTED_OUTPUT", "name": "_IMAGE_CLASS_EXPECTED_OUTPUT", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_code_sample_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_code_sample_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "drop_path": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["input", "drop_prob", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.drop_path", "name": "drop_path", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["input", "drop_prob", "training"], "arg_types": ["torch._tensor.Tensor", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_path", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eager_attention_forward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["module", "query", "key", "value", "attention_mask", "scaling", "dropout", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.eager_attention_forward", "name": "eager_attention_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["module", "query", "key", "value", "attention_mask", "scaling", "dropout", "kwargs"], "arg_types": ["torch.nn.modules.module.Module", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eager_attention_forward", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_pruneable_heads_and_indices": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.find_pruneable_heads_and_indices", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.dinov2_with_registers.modeling_dinov2_with_registers.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "prune_linear_layer": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.prune_linear_layer", "kind": "Gdef", "module_public": false}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "torch_int": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.torch_int", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\dinov2_with_registers\\modeling_dinov2_with_registers.py"}