{"data_mtime": 1753783921, "dep_lines": [43, 27, 41, 42, 22, 27, 28, 31, 32, 38, 39, 40, 41, 22, 23, 24, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 20, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.dpt.configuration_dpt", "torch.utils.checkpoint", "transformers.utils.logging", "transformers.utils.backbone_utils", "collections.abc", "torch.utils", "torch.nn", "transformers.activations", "transformers.file_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.pytorch_utils", "transformers.utils", "collections", "dataclasses", "typing", "torch", "builtins", "paddle", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "numpy", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.batchnorm", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.upsampling", "torch.nn.parameter", "transformers.configuration_utils", "transformers.generation", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "53c217d12ed425bd6292c5f66ca6c48dff311387", "id": "transformers.models.dpt.modeling_dpt", "ignore_all": true, "interface_hash": "1c70c66c5c86bfb69441e925af284404c470d09a", "mtime": 1746815062, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\dpt\\modeling_dpt.py", "plugin_data": null, "size": 58892, "suppressed": [], "version_id": "1.15.0"}