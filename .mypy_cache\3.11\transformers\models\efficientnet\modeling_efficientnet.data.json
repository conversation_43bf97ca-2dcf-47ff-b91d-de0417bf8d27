{".class": "MypyFile", "_fullname": "transformers.models.efficientnet.modeling_efficientnet", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef", "module_public": false}, "BCEWithLogitsLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.BCEWithLogitsLoss", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithNoAttention", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithPoolingAndNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithPoolingAndNoAttention", "kind": "Gdef", "module_public": false}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "EFFICIENTNET_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EFFICIENTNET_INPUTS_DOCSTRING", "name": "EFFICIENTNET_INPUTS_DOCSTRING", "type": "builtins.str"}}, "EFFICIENTNET_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EFFICIENTNET_START_DOCSTRING", "name": "EFFICIENTNET_START_DOCSTRING", "type": "builtins.str"}}, "EfficientNetBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock", "name": "EfficientNetBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "out_dim", "stride", "expand_ratio", "kernel_size", "drop_rate", "id_skip", "adjust_padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "out_dim", "stride", "expand_ratio", "kernel_size", "drop_rate", "id_skip", "adjust_padding"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "depthwise_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.depthwise_conv", "name": "depthwise_conv", "type": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer"}}, "expand": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.expand", "name": "expand", "type": "builtins.bool"}}, "expand_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.expand_ratio", "name": "expand_ratio", "type": "builtins.int"}}, "expansion": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.expansion", "name": "expansion", "type": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetBlock", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.projection", "name": "projection", "type": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer"}}, "squeeze_excite": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.squeeze_excite", "name": "squeeze_excite", "type": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig", "kind": "Gdef", "module_public": false}, "EfficientNetDepthwiseConv2d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.conv.Conv2d"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseConv2d", "name": "EfficientNetDepthwiseConv2d", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseConv2d", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseConv2d", "torch.nn.modules.conv.Conv2d", "torch.nn.modules.conv._ConvNd", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "in_channels", "depth_multiplier", "kernel_size", "stride", "padding", "dilation", "bias", "padding_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseConv2d.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseConv2d.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseConv2d", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetDepthwiseLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer", "name": "EfficientNetDepthwiseLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "stride", "kernel_size", "adjust_padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "stride", "kernel_size", "adjust_padding"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetDepthwiseLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "depthwise_act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.depthwise_act", "name": "depthwise_act", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "depthwise_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.depthwise_conv", "name": "depthwise_conv", "type": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseConv2d"}}, "depthwise_conv_pad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.depthwise_conv_pad", "name": "depthwise_conv_pad", "type": "torch.nn.modules.padding.ZeroPad2d"}}, "depthwise_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.depthwise_norm", "name": "depthwise_norm", "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetDepthwiseLayer", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stride": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.stride", "name": "stride", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetDepthwiseLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings", "name": "EfficientNetEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "batchnorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.batchnorm", "name": "batchnorm", "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "convolution": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.convolution", "name": "convolution", "type": "torch.nn.modules.conv.Conv2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetEmbeddings", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "out_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.out_dim", "name": "out_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.padding", "name": "padding", "type": "torch.nn.modules.padding.ZeroPad2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder", "name": "EfficientNetEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.blocks", "name": "blocks", "type": "torch.nn.modules.container.ModuleList"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.config", "name": "config", "type": "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig"}}, "depth_coefficient": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.depth_coefficient", "name": "depth_coefficient", "type": "builtins.float"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder", "torch._<PERSON><PERSON>", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetEncoder", "ret_type": "transformers.modeling_outputs.BaseModelOutputWithNoAttention", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "top_activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.top_activation", "name": "top_activation", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "top_bn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.top_bn", "name": "top_bn", "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "top_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.top_conv", "name": "top_conv", "type": "torch.nn.modules.conv.Conv2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetExpansionLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer", "name": "EfficientNetExpansionLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "out_dim", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "out_dim", "stride"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetExpansionLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expand_act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer.expand_act", "name": "expand_act", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "expand_bn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer.expand_bn", "name": "expand_bn", "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "expand_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer.expand_conv", "name": "expand_conv", "type": "torch.nn.modules.conv.Conv2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetExpansionLayer", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetExpansionLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetFinalBlockLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer", "name": "EfficientNetFinalBlockLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "out_dim", "stride", "drop_rate", "id_skip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "config", "in_dim", "out_dim", "stride", "drop_rate", "id_skip"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetFinalBlockLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer.apply_dropout", "name": "apply_dropout", "type": "builtins.bool"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer.dropout", "name": "dropout", "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "embeddings", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "embeddings", "hidden_states"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer", "torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetFinalBlockLayer", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "project_bn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer.project_bn", "name": "project_bn", "type": "torch.nn.modules.batchnorm.BatchNorm2d"}}, "project_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer.project_conv", "name": "project_conv", "type": "torch.nn.modules.conv.Conv2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetFinalBlockLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetForImageClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification", "name": "EfficientNetForImageClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification", "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.classifier", "name": "classifier", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "efficientnet": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.efficientnet", "name": "efficientnet", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetForImageClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.ImageClassifierOutputWithNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.num_labels", "name": "num_labels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetForImageClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel", "name": "EfficientNetModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel", "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel.embeddings", "name": "embeddings", "type": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEmbeddings"}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel.encoder", "name": "encoder", "type": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetEncoder"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.BaseModelOutputWithPoolingAndNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "pooler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel.pooler", "name": "pooler", "type": "torch.nn.modules.pooling.AvgPool2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel", "name": "EfficientNetPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel._no_split_modules", "name": "_no_split_modules", "type": null}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel.config_class", "name": "config_class", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EfficientNetSqueezeExciteLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer", "name": "EfficientNetSqueezeExciteLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.efficientnet.modeling_efficientnet", "mro": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "in_dim", "expand_dim", "expand"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "in_dim", "expand_dim", "expand"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer", "transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig", "builtins.int", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EfficientNetSqueezeExciteLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act_expand": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.act_expand", "name": "act_expand", "type": "torch.nn.modules.activation.Sigmoid"}}, "act_reduce": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.act_reduce", "name": "act_reduce", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.dim", "name": "dim", "type": "builtins.int"}}, "dim_se": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.dim_se", "name": "dim_se", "type": "builtins.int"}}, "expand": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.expand", "name": "expand", "type": "torch.nn.modules.conv.Conv2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of EfficientNetSqueezeExciteLayer", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reduce": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.reduce", "name": "reduce", "type": "torch.nn.modules.conv.Conv2d"}}, "squeeze": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.squeeze", "name": "squeeze", "type": "torch.nn.modules.pooling.AdaptiveAvgPool2d"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.efficientnet.modeling_efficientnet.EfficientNetSqueezeExciteLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageClassifierOutputWithNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.ImageClassifierOutputWithNoAttention", "kind": "Gdef", "module_public": false}, "MSELoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.MSELoss", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "_EXPECTED_OUTPUT_SHAPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet._EXPECTED_OUTPUT_SHAPE", "name": "_EXPECTED_OUTPUT_SHAPE", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_IMAGE_CLASS_CHECKPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet._IMAGE_CLASS_CHECKPOINT", "name": "_IMAGE_CLASS_CHECKPOINT", "type": "builtins.str"}}, "_IMAGE_CLASS_EXPECTED_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet._IMAGE_CLASS_EXPECTED_OUTPUT", "name": "_IMAGE_CLASS_EXPECTED_OUTPUT", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_code_sample_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_code_sample_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "correct_pad": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["kernel_size", "adjust"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.correct_pad", "name": "correct_pad", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["kernel_size", "adjust"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "correct_pad", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.efficientnet.modeling_efficientnet.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "round_filters": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["config", "num_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.efficientnet.modeling_efficientnet.round_filters", "name": "round_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["config", "num_channels"], "arg_types": ["transformers.models.efficientnet.configuration_efficientnet.EfficientNetConfig", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "round_filters", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\efficientnet\\modeling_efficientnet.py"}