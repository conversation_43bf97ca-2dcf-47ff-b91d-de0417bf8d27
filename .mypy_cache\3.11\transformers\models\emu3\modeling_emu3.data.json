{".class": "MypyFile", "_fullname": "transformers.models.emu3.modeling_emu3", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations.ACT2FN", "kind": "Gdef", "module_public": false}, "ALL_ATTENTION_FUNCTIONS": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.ALL_ATTENTION_FUNCTIONS", "kind": "Gdef", "module_public": false}, "AttentionMaskConverter": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_attn_mask_utils.AttentionMaskConverter", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithPast": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithPast", "kind": "Gdef", "module_public": false}, "BlockMask": {".class": "SymbolTableNode", "cross_ref": "torch.nn.attention.flex_attention.BlockMask", "kind": "Gdef", "module_public": false}, "Cache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.Cache", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CausalLMOutputWithPast": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.CausalLMOutputWithPast", "kind": "Gdef", "module_public": false}, "DynamicCache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.DynamicCache", "kind": "Gdef", "module_public": false}, "EMU3_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.EMU3_INPUTS_DOCSTRING", "name": "EMU3_INPUTS_DOCSTRING", "type": "builtins.str"}}, "EMU3_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.EMU3_START_DOCSTRING", "name": "EMU3_START_DOCSTRING", "type": "builtins.str"}}, "EMU3_TEXT_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.EMU3_TEXT_INPUTS_DOCSTRING", "name": "EMU3_TEXT_INPUTS_DOCSTRING", "type": "builtins.str"}}, "EMU3_VQ_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.EMU3_VQ_START_DOCSTRING", "name": "EMU3_VQ_START_DOCSTRING", "type": "builtins.str"}}, "Emu3Attention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention", "name": "Emu3Attention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3Attention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3Attention", "transformers.models.emu3.configuration_emu3.Emu3Config", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3Attention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.attention_dropout", "name": "attention_dropout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.config", "name": "config", "type": "transformers.models.emu3.configuration_emu3.Emu3Config"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "hidden_states", "position_embeddings", "attention_mask", "past_key_value", "cache_position", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "hidden_states", "position_embeddings", "attention_mask", "past_key_value", "cache_position", "kwargs"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3Attention", "torch._tensor.Tensor", {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.cache_utils.Cache", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_flash_attention_utils.FlashAttentionKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3Attention", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.head_dim", "name": "head_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "is_causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.is_causal", "name": "is_causal", "type": "builtins.bool"}}, "k_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.k_proj", "name": "k_proj", "type": "torch.nn.modules.linear.Linear"}}, "layer_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.layer_idx", "name": "layer_idx", "type": "builtins.int"}}, "num_key_value_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.num_key_value_groups", "name": "num_key_value_groups", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "o_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.o_proj", "name": "o_proj", "type": "torch.nn.modules.linear.Linear"}}, "q_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.q_proj", "name": "q_proj", "type": "torch.nn.modules.linear.Linear"}}, "scaling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.scaling", "name": "scaling", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "v_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.v_proj", "name": "v_proj", "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3Attention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3Attention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3Config": {".class": "SymbolTableNode", "cross_ref": "transformers.models.emu3.configuration_emu3.Emu3Config", "kind": "Gdef", "module_public": false}, "Emu3DecoderLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer", "name": "Emu3DecoderLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3DecoderLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "config", "layer_idx"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3DecoderLayer", "transformers.models.emu3.configuration_emu3.Emu3Config", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3DecoderLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.dropout", "name": "dropout", "type": "torch.nn.modules.dropout.Dropout"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "hidden_states", "attention_mask", "position_ids", "past_key_value", "output_attentions", "use_cache", "cache_position", "position_embeddings", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "hidden_states", "attention_mask", "position_ids", "past_key_value", "output_attentions", "use_cache", "cache_position", "position_embeddings", "kwargs"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3DecoderLayer", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.cache_utils.Cache", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3Decoder<PERSON><PERSON>er", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.hidden_size", "name": "hidden_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "input_layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.input_layernorm", "name": "input_layernorm", "type": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm"}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.mlp", "name": "mlp", "type": "transformers.models.emu3.modeling_emu3.Emu3MLP"}}, "post_attention_layernorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.post_attention_layernorm", "name": "post_attention_layernorm", "type": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm"}}, "self_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.self_attn", "name": "self_attn", "type": "transformers.models.emu3.modeling_emu3.Emu3Attention"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3DecoderLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3ForCausalLM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "transformers.generation.utils.GenerationMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM", "name": "Emu3ForCausalLM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3ForCausalLM", "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.__init__", "name": "__init__", "type": null}}, "_pp_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM._pp_plan", "name": "_pp_plan", "type": null}}, "_tied_weights_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM._tied_weights_keys", "name": "_tied_weights_keys", "type": null}}, "_tp_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM._tp_plan", "name": "_tp_plan", "type": null}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.config_class", "name": "config_class", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "past_key_values", "inputs_embeds", "labels", "use_cache", "output_attentions", "output_hidden_states", "cache_position", "logits_to_keep", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "past_key_values", "inputs_embeds", "labels", "use_cache", "output_attentions", "output_hidden_states", "cache_position", "logits_to_keep", "kwargs"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ForCausalLM", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.cache_utils.Cache", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "torch._tensor.Tensor"], "uses_pep604_syntax": false}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.models.emu3.modeling_emu3.KwargsForCausalLM"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3ForCausalLM", "ret_type": "transformers.modeling_outputs.CausalLMOutputWithPast", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.get_decoder", "name": "get_decoder", "type": null}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "get_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.get_output_embeddings", "name": "get_output_embeddings", "type": null}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.lm_head", "name": "lm_head", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.model", "name": "model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "decoder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.set_decoder", "name": "set_decoder", "type": null}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.set_input_embeddings", "name": "set_input_embeddings", "type": null}}, "set_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.set_output_embeddings", "name": "set_output_embeddings", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3ForCausalLM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3ForConditionalGeneration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "transformers.generation.utils.GenerationMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration", "name": "Emu3ForConditionalGeneration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration", "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.__init__", "name": "__init__", "type": null}}, "_supports_static_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration._supports_static_cache", "name": "_supports_static_cache", "type": "builtins.bool"}}, "_tied_weights_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration._tied_weights_keys", "name": "_tied_weights_keys", "type": null}}, "decode_image_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "image_tokens", "height", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.decode_image_tokens", "name": "decode_image_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "image_tokens", "height", "width"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration", "torch._<PERSON><PERSON>", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode_image_tokens of Emu3ForConditionalGeneration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.decode_image_tokens", "name": "decode_image_tokens", "type": "torch.autograd.grad_mode.no_grad"}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "image_sizes", "attention_mask", "position_ids", "past_key_values", "inputs_embeds", "use_cache", "output_attentions", "output_hidden_states", "cache_position", "labels", "logits_to_keep"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "pixel_values", "image_sizes", "attention_mask", "position_ids", "past_key_values", "inputs_embeds", "use_cache", "output_attentions", "output_hidden_states", "cache_position", "labels", "logits_to_keep"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.cache_utils.Cache", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "torch._tensor.Tensor"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3ForConditionalGeneration", "ret_type": "transformers.modeling_outputs.CausalLMOutputWithPast", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_image_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pixel_values", "image_sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.get_image_tokens", "name": "get_image_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pixel_values", "image_sizes"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration", "torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_image_tokens of Emu3ForConditionalGeneration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "prepare_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_ids", "past_key_values", "attention_mask", "inputs_embeds", "cache_position", "position_ids", "use_cache", "pixel_values", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.prepare_inputs_for_generation", "name": "prepare_inputs_for_generation", "type": null}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.set_input_embeddings", "name": "set_input_embeddings", "type": null}}, "text_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.text_model", "name": "text_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocabulary_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.vocabulary_mapping", "name": "vocabulary_mapping", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vqmodel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.vqmodel", "name": "vqmodel", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3ForConditionalGeneration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3ImageVocabularyMapping": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping", "name": "Emu3ImageVocabularyMapping", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vocab_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.__init__", "name": "__init__", "type": null}}, "bpe2img": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.bpe2img", "name": "bpe2img", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.bpe2img", "name": "bpe2img", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bpe2img of Emu3ImageVocabularyMapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bpe2img_mapping_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.bpe2img_mapping_tensor", "name": "bpe2img_mapping_tensor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.bpe2img_mapping_tensor", "name": "bpe2img_mapping_tensor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bpe2img_mapping_tensor of Emu3ImageVocabularyMapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_bpe2img": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "img_batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.convert_bpe2img", "name": "convert_bpe2img", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "img_batch"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert_bpe2img of Emu3ImageVocabularyMapping", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_img2bpe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "img_batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.convert_img2bpe", "name": "convert_img2bpe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "img_batch"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "convert_img2bpe of Emu3ImageVocabularyMapping", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eol_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.eol_token_id", "name": "eol_token_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "image_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.image_token_id", "name": "image_token_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "image_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.image_tokens", "name": "image_tokens", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.image_tokens", "name": "image_tokens", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "image_tokens of Emu3ImageVocabularyMapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "image_tokens_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.image_tokens_str", "name": "image_tokens_str", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.image_tokens_str", "name": "image_tokens_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "image_tokens_str of Emu3ImageVocabularyMapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "img2bpe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.img2bpe", "name": "img2bpe", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.img2bpe", "name": "img2bpe", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "img2bpe of Emu3ImageVocabularyMapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "img2bpe_mapping_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.img2bpe_mapping_tensor", "name": "img2bpe_mapping_tensor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.img2bpe_mapping_tensor", "name": "img2bpe_mapping_tensor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "img2bpe_mapping_tensor of Emu3ImageVocabularyMapping", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vocab_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.vocab_map", "name": "vocab_map", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3ImageVocabularyMapping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3MLP": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP", "name": "Emu3MLP", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3MLP", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.__init__", "name": "__init__", "type": null}}, "act_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.act_fn", "name": "act_fn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "down_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.down_proj", "name": "down_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.forward", "name": "forward", "type": null}}, "gate_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.gate_proj", "name": "gate_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.hidden_size", "name": "hidden_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "intermediate_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.intermediate_size", "name": "intermediate_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "up_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.up_proj", "name": "up_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3MLP.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3MLP", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3PreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "name": "Emu3PreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._no_split_modules", "name": "_no_split_modules", "type": null}}, "_skip_keys_device_placement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._skip_keys_device_placement", "name": "_skip_keys_device_placement", "type": null}}, "_supports_cache_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._supports_cache_class", "name": "_supports_cache_class", "type": "builtins.bool"}}, "_supports_flash_attn_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._supports_flash_attn_2", "name": "_supports_flash_attn_2", "type": "builtins.bool"}}, "_supports_flex_attn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._supports_flex_attn", "name": "_supports_flex_attn", "type": "builtins.bool"}}, "_supports_param_buffer_assignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._supports_param_buffer_assignment", "name": "_supports_param_buffer_assignment", "type": "builtins.bool"}}, "_supports_quantized_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._supports_quantized_cache", "name": "_supports_quantized_cache", "type": "builtins.bool"}}, "_supports_sdpa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._supports_sdpa", "name": "_supports_sdpa", "type": "builtins.bool"}}, "_supports_static_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel._supports_static_cache", "name": "_supports_static_cache", "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel.config_class", "name": "config_class", "type": null}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3RMSNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm", "name": "Emu3RMSNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3RMSNorm", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_size", "eps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm.__init__", "name": "__init__", "type": null}}, "extra_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm.extra_repr", "name": "extra_repr", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm.forward", "name": "forward", "type": null}}, "variance_epsilon": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm.variance_epsilon", "name": "variance_epsilon", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm.weight", "name": "weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3RotaryEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding", "name": "Emu3RotaryEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "device"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding", "transformers.models.emu3.configuration_emu3.Emu3Config", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3RotaryEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attention_scaling": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.attention_scaling", "name": "attention_scaling", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.config", "name": "config", "type": "transformers.models.emu3.configuration_emu3.Emu3Config"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "position_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.forward", "name": "forward", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "max_seq_len_cached": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.max_seq_len_cached", "name": "max_seq_len_cached", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "original_inv_freq": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.original_inv_freq", "name": "original_inv_freq", "type": {".class": "UnionType", "items": ["torch._tensor.Tensor", "torch.nn.modules.module.Module"], "uses_pep604_syntax": false}}}, "original_max_seq_len": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.original_max_seq_len", "name": "original_max_seq_len", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rope_init_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.rope_init_fn", "name": "rope_init_fn", "type": "builtins.function"}}, "rope_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.rope_type", "name": "rope_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3TextConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.emu3.configuration_emu3.Emu3TextConfig", "kind": "Gdef", "module_public": false}, "Emu3TextModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel", "name": "Emu3TextModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3TextModel", "transformers.models.emu3.modeling_emu3.Emu3PreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3TextModel", "transformers.models.emu3.configuration_emu3.Emu3Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3TextModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_4d_causal_attention_mask_with_cache_position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["attention_mask", "sequence_length", "target_length", "dtype", "device", "cache_position", "batch_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel._prepare_4d_causal_attention_mask_with_cache_position", "name": "_prepare_4d_causal_attention_mask_with_cache_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["attention_mask", "sequence_length", "target_length", "dtype", "device", "cache_position", "batch_size", "kwargs"], "arg_types": ["torch._tensor.Tensor", "builtins.int", "builtins.int", "torch._C.dtype", "torch._C.device", "torch._tensor.Tensor", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_4d_causal_attention_mask_with_cache_position of Emu3TextModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel._prepare_4d_causal_attention_mask_with_cache_position", "name": "_prepare_4d_causal_attention_mask_with_cache_position", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["attention_mask", "sequence_length", "target_length", "dtype", "device", "cache_position", "batch_size", "kwargs"], "arg_types": ["torch._tensor.Tensor", "builtins.int", "builtins.int", "torch._C.dtype", "torch._C.device", "torch._tensor.Tensor", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_4d_causal_attention_mask_with_cache_position of Emu3TextModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_update_causal_mask": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "attention_mask", "input_tensor", "cache_position", "past_key_values", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel._update_causal_mask", "name": "_update_causal_mask", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "attention_mask", "input_tensor", "cache_position", "past_key_values", "output_attentions"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3TextModel", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", "transformers.cache_utils.Cache", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_causal_mask of Emu3TextModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.embed_tokens", "name": "embed_tokens", "type": "torch.nn.modules.sparse.Embedding"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "past_key_values", "inputs_embeds", "use_cache", "output_attentions", "output_hidden_states", "cache_position", "flash_attn_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "past_key_values", "inputs_embeds", "use_cache", "output_attentions", "output_hidden_states", "cache_position", "flash_attn_kwargs"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3TextModel", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.cache_utils.Cache", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_flash_attention_utils.FlashAttentionKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3TextModel", "ret_type": "transformers.modeling_outputs.BaseModelOutputWithPast", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.forward", "name": "forward", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.layers", "name": "layers", "type": "torch.nn.modules.container.ModuleList"}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.norm", "name": "norm", "type": "transformers.models.emu3.modeling_emu3.Emu3RMSNorm"}}, "padding_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.padding_idx", "name": "padding_idx", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rotary_emb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.rotary_emb", "name": "rotary_emb", "type": "transformers.models.emu3.modeling_emu3.Emu3RotaryEmbedding"}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.set_input_embeddings", "name": "set_input_embeddings", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3TextModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3TextModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE", "name": "Emu3VQVAE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAE", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.generation.utils.GenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAE", "transformers.models.emu3.configuration_emu3.Emu3VQVAEConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAE", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE._init_weights", "name": "_init_weights", "type": null}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE._no_split_modules", "name": "_no_split_modules", "type": null}}, "_supports_flash_attn_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE._supports_flash_attn_2", "name": "_supports_flash_attn_2", "type": "builtins.bool"}}, "_supports_flex_attn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE._supports_flex_attn", "name": "_supports_flex_attn", "type": "builtins.bool"}}, "_supports_sdpa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE._supports_sdpa", "name": "_supports_sdpa", "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.config_class", "name": "config_class", "type": null}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAE", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decode of Emu3VQVAE", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.decoder", "name": "decoder", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder"}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pixel_values", "image_sizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pixel_values", "image_sizes"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAE", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of Emu3VQVAE", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.encoder", "name": "encoder", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder"}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.main_input_name", "name": "main_input_name", "type": "builtins.str"}}, "post_quant_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.post_quant_conv", "name": "post_quant_conv", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d"}}, "quant_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.quant_conv", "name": "quant_conv", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d"}}, "quantize": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.quantize", "name": "quantize", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer"}}, "spatial_scale_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.spatial_scale_factor", "name": "spatial_scale_factor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "vision_spatial_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.vision_spatial_factor", "name": "vision_spatial_factor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEAttentionBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock", "name": "Emu3VQVAEAttentionBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock", "transformers.models.emu3.configuration_emu3.Emu3VQVAEConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAEAttentionBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.config", "name": "config", "type": "transformers.models.emu3.configuration_emu3.Emu3VQVAEConfig"}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.dropout", "name": "dropout", "type": "builtins.float"}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.embed_dim", "name": "embed_dim", "type": "builtins.int"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEAttentionBlock", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.head_dim", "name": "head_dim", "type": "builtins.int"}}, "is_causal": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.is_causal", "name": "is_causal", "type": "builtins.bool"}}, "k_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.k_proj", "name": "k_proj", "type": "torch.nn.modules.linear.Linear"}}, "num_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.num_heads", "name": "num_heads", "type": "builtins.int"}}, "num_key_value_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.num_key_value_groups", "name": "num_key_value_groups", "type": "builtins.int"}}, "out_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.out_proj", "name": "out_proj", "type": "torch.nn.modules.linear.Linear"}}, "q_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.q_proj", "name": "q_proj", "type": "torch.nn.modules.linear.Linear"}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.scale", "name": "scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "v_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.v_proj", "name": "v_proj", "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEAttentionBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.emu3.configuration_emu3.Emu3VQVAEConfig", "kind": "Gdef", "module_public": false}, "Emu3VQVAEConv3d": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d", "name": "Emu3VQVAEConv3d", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "in_channel", "out_channel", "kernel_size", "stride"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "in_channel", "out_channel", "kernel_size", "stride"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d", "builtins.int", "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAEConv3d", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d.conv", "name": "conv", "type": "torch.nn.modules.conv.Conv3d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEConv3d", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d.padding", "name": "padding", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEDecoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder", "name": "Emu3VQVAEDecoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder", "transformers.models.emu3.configuration_emu3.Emu3VQVAEConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAEDecoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv_in": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.conv_in", "name": "conv_in", "type": "torch.nn.modules.conv.Conv2d"}}, "conv_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.conv_out", "name": "conv_out", "type": "torch.nn.modules.conv.Conv2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "quant_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "quant_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEDecoder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "middle_block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.middle_block", "name": "middle_block", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock"}}, "norm_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.norm_out", "name": "norm_out", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm"}}, "time_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.time_conv", "name": "time_conv", "type": "torch.nn.modules.container.ModuleList"}}, "time_res_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.time_res_stack", "name": "time_res_stack", "type": "torch.nn.modules.container.ModuleList"}}, "up_block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.up_block", "name": "up_block", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDecoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEDownBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock", "name": "Emu3VQVAEDownBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock.__init__", "name": "__init__", "type": null}}, "down": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock.down", "name": "down", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEDownBlock", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_channel_multiplier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock.in_channel_multiplier", "name": "in_channel_multiplier", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_res_blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock.num_res_blocks", "name": "num_res_blocks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_resolutions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock.num_resolutions", "name": "num_resolutions", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEDownBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder", "name": "Emu3VQVAEEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.__init__", "name": "__init__", "type": null}}, "conv_in": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.conv_in", "name": "conv_in", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.conv_out", "name": "conv_out", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "down_block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.down_block", "name": "down_block", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEEncoder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "middle_block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.middle_block", "name": "middle_block", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "norm_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.norm_out", "name": "norm_out", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "time_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.time_conv", "name": "time_conv", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "time_res_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.time_res_stack", "name": "time_res_stack", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEEncoderConvDownsample": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample", "name": "Emu3VQVAEEncoderConvDownsample", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "in_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample.__init__", "name": "__init__", "type": null}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample.conv", "name": "conv", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvDownsample", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEEncoderConvUpsample": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample", "name": "Emu3VQVAEEncoderConvUpsample", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "in_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample.__init__", "name": "__init__", "type": null}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample.conv", "name": "conv", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEEncoderConvUpsample", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEGroupNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.normalization.GroupNorm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEGroupNorm", "name": "Emu3VQVAEGroupNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEGroupNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEGroupNorm", "torch.nn.modules.normalization.GroupNorm", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEGroupNorm.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "quant_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEGroupNorm.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEGroupNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEGroupNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEMiddleBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock", "name": "Emu3VQVAEMiddleBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "config", "in_channels", "quant_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock.__init__", "name": "__init__", "type": null}}, "attn_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock.attn_1", "name": "attn_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "attn_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock.attn_norm", "name": "attn_norm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "block_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock.block_1", "name": "block_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "block_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock.block_2", "name": "block_2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "quant_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "quant_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock", "torch._<PERSON><PERSON>", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEMiddleBlock", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEMiddleBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEResnetBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock", "name": "Emu3VQVAEResnetBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "in_channels", "out_channels", "quant_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "in_channels", "out_channels", "quant_channels"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAEResnetBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.conv1", "name": "conv1", "type": "torch.nn.modules.conv.Conv2d"}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.conv2", "name": "conv2", "type": "torch.nn.modules.conv.Conv2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "quant_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "quant_channels"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEResnetBlock", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "in_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.in_channels", "name": "in_channels", "type": "builtins.int"}}, "nin_shortcut": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.nin_shortcut", "name": "nin_shortcut", "type": "torch.nn.modules.conv.Conv2d"}}, "norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.norm1", "name": "norm1", "type": "torch.nn.modules.normalization.GroupNorm"}}, "norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.norm2", "name": "norm2", "type": "torch.nn.modules.normalization.GroupNorm"}}, "out_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.out_channels", "name": "out_channels", "type": "builtins.int"}}, "quant_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.quant_channels", "name": "quant_channels", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEResnetBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAESpatialNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm", "name": "Emu3VQVAESpatialNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "in_channels", "out_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "in_channels", "out_channels"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAESpatialNorm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv_b": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm.conv_b", "name": "conv_b", "type": "torch.nn.modules.conv.Conv2d"}}, "conv_y": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm.conv_y", "name": "conv_y", "type": "torch.nn.modules.conv.Conv2d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "quant_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "quant_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm", "torch._tensor.Tensor", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAESpatialNorm", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "norm_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm.norm_layer", "name": "norm_layer", "type": "torch.nn.modules.normalization.GroupNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAESpatialNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAETemporalDownsample": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample", "name": "Emu3VQVAETemporalDownsample", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "in_channel", "out_channel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "in_channel", "out_channel"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAETemporalDownsample", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample.conv", "name": "conv", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAETemporalDownsample", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalDownsample", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAETemporalResnetBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock", "name": "Emu3VQVAETemporalResnetBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "in_channels", "out_channels"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.__init__", "name": "__init__", "type": null}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.conv1", "name": "conv1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.conv2", "name": "conv2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.forward", "name": "forward", "type": null}}, "in_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.in_channels", "name": "in_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "nin_shortcut": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.nin_shortcut", "name": "nin_shortcut", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.norm1", "name": "norm1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.norm2", "name": "norm2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.out_channels", "name": "out_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalResnetBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAETemporalUpsample": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample", "name": "Emu3VQVAETemporalUpsample", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "in_channel", "out_channel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "in_channel", "out_channel"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAETemporalUpsample", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample.conv", "name": "conv", "type": "transformers.models.emu3.modeling_emu3.Emu3VQVAEConv3d"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAETemporalUpsample", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAETemporalUpsample", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEUpBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock", "name": "Emu3VQVAEUpBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "quant_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "quant_states"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock", "torch._<PERSON><PERSON>", "torch._<PERSON><PERSON>"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEUpBlock", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "num_res_blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock.num_res_blocks", "name": "num_res_blocks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_resolutions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock.num_resolutions", "name": "num_resolutions", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "up": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock.up", "name": "up", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEUpBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Emu3VQVAEVectorQuantizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer", "name": "Emu3VQVAEVectorQuantizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer", "transformers.models.emu3.configuration_emu3.Emu3VQVAEConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Emu3VQVAEVectorQuantizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer.embedding", "name": "embedding", "type": "torch.nn.modules.sparse.Embedding"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer", "torch._tensor.Tensor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "forward of Emu3VQVAEVectorQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.emu3.modeling_emu3.Emu3VQVAEVectorQuantizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef", "module_public": false}, "FlashAttentionKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils.FlashAttentionKwargs", "kind": "Gdef", "module_public": false}, "GenerationMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.generation.utils.GenerationMixin", "kind": "Gdef", "module_public": false}, "KwargsForCausalLM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.emu3.modeling_emu3.KwargsForCausalLM", "name": "KwargsForCausalLM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.KwargsForCausalLM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.emu3.modeling_emu3", "mro": ["transformers.models.emu3.modeling_emu3.KwargsForCausalLM", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["num_items_in_batch", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cu_seq_lens_q", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["cu_seq_lens_k", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_length_q", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["max_length_k", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LossKwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.LossKwargs", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "ROPE_INIT_FUNCTIONS": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_rope_utils.ROPE_INIT_FUNCTIONS", "kind": "Gdef", "module_public": false}, "StaticCache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.StaticCache", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.Unpack", "kind": "Gdef", "module_public": false}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.emu3.modeling_emu3.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.emu3.modeling_emu3.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.emu3.modeling_emu3.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.emu3.modeling_emu3.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.emu3.modeling_emu3.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.emu3.modeling_emu3.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "apply_rotary_pos_emb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["q", "k", "cos", "sin", "position_ids", "unsqueeze_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.apply_rotary_pos_emb", "name": "apply_rotary_pos_emb", "type": null}}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef", "module_public": false}, "can_return_tuple": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.can_return_tuple", "kind": "Gdef", "module_public": false}, "deprecate_kwarg": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.deprecation.deprecate_kwarg", "kind": "Gdef", "module_public": false}, "dynamic_rope_update": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_rope_utils.dynamic_rope_update", "kind": "Gdef", "module_public": false}, "eager_attention_forward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["module", "query", "key", "value", "attention_mask", "scaling", "dropout", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.eager_attention_forward", "name": "eager_attention_forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["module", "query", "key", "value", "attention_mask", "scaling", "dropout", "kwargs"], "arg_types": ["torch.nn.modules.module.Module", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "eager_attention_forward", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_torch_flex_attn_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_flex_attn_available", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.emu3.modeling_emu3.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "make_flex_block_causal_mask": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.flex_attention.make_flex_block_causal_mask", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "repeat_kv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["hidden_states", "n_rep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.repeat_kv", "name": "repeat_kv", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["hidden_states", "n_rep"], "arg_types": ["torch._tensor.Tensor", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repeat_kv", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "rotate_half": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.emu3.modeling_emu3.rotate_half", "name": "rotate_half", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\emu3\\modeling_emu3.py"}