{"data_mtime": 1753783921, "dep_lines": [33, 34, 35, 32, 25, 28, 29, 30, 31, 32, 17, 18, 19, 20, 21, 22, 24, 318, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 10, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.modeling_auto", "transformers.models.encoder_decoder.configuration_encoder_decoder", "transformers.utils.logging", "torch.nn", "transformers.configuration_utils", "transformers.generation", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.utils", "gc", "inspect", "os", "tempfile", "warnings", "typing", "torch", "transformers", "builtins", "paddle", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_collections_abc", "_frozen_importlib", "abc", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.utils.doc", "transformers.utils.dummy_tf_objects", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "5877d24741a15fccc64ec243b381939164a38718", "id": "transformers.models.encoder_decoder.modeling_encoder_decoder", "ignore_all": true, "interface_hash": "8d65ed2663831e8b82e63415a610197c5d2d0915", "mtime": 1746815062, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\encoder_decoder\\modeling_encoder_decoder.py", "plugin_data": null, "size": 35618, "suppressed": [], "version_id": "1.15.0"}