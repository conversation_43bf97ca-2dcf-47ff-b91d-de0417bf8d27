{"data_mtime": 1753783921, "dep_lines": [38, 22, 30, 37, 52, 22, 23, 26, 27, 28, 29, 30, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 50, 49, 44, 57], "dep_prios": [5, 10, 10, 5, 5, 20, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["transformers.models.falcon_mamba.configuration_falcon_mamba", "torch.utils.checkpoint", "transformers.utils.logging", "transformers.utils.import_utils", "transformers.kernels.falcon_mamba", "torch.utils", "torch.nn", "transformers.activations", "transformers.cache_utils", "transformers.generation", "transformers.modeling_utils", "transformers.utils", "math", "dataclasses", "typing", "torch", "builtins", "paddle", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn.init", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.sparse", "torch.nn.parameter", "torch.utils._contextlib", "transformers.configuration_utils", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.kernels", "transformers.kernels.falcon_mamba.selective_scan_with_ln_interface", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "408866fd84f817698d7b47fa351ae87a0f8af965", "id": "transformers.models.falcon_mamba.modeling_falcon_mamba", "ignore_all": true, "interface_hash": "e43fb7eb8541d4955308c2509a586d6fdafbdddc", "mtime": 1746815062, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\falcon_mamba\\modeling_falcon_mamba.py", "plugin_data": null, "size": 40717, "suppressed": ["mamba_ssm.ops.triton.selective_state_update", "mamba_ssm.ops.selective_scan_interface", "mambapy.pscan", "causal_conv1d"], "version_id": "1.15.0"}