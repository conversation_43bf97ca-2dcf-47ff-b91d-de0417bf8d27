{".class": "MypyFile", "_fullname": "transformers.models.gpt2.tokenization_gpt2_tf", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytePairTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.BytePairTokenizer", "name": "BytePairTokenizer", "type": {".class": "AnyType", "missing_import_name": "transformers.models.gpt2.tokenization_gpt2_tf.BytePairTokenizer", "source_any": null, "type_of_any": 3}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "GPT2Tokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.models.gpt2.tokenization_gpt2.GPT2Tokenizer", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "TFGPT2Tokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer", "name": "TFGPT2Tokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.gpt2.tokenization_gpt2_tf", "mro": ["transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "vocab", "merges", "max_length", "pad_token_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "vocab", "merges", "max_length", "pad_token_id"], "arg_types": ["transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFGPT2Tokenizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "max_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "max_length"], "arg_types": ["transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFGPT2Tokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.from_config", "name": "from_config", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.from_config", "name": "from_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "config"], "arg_types": [{".class": "TypeType", "item": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_config of TFGPT2Tokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "init_inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.from_pretrained", "name": "from_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "init_inputs", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_pretrained of TFGPT2Tokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.from_pretrained", "name": "from_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "init_inputs", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_pretrained of TFGPT2Tokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "tokenizer", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.from_tokenizer", "name": "from_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "tokenizer", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer"}, "transformers.models.gpt2.tokenization_gpt2.GPT2Tokenizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_tokenizer of TFGPT2Tokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.from_tokenizer", "name": "from_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "tokenizer", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer"}, "transformers.models.gpt2.tokenization_gpt2.GPT2Tokenizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_tokenizer of TFGPT2Tokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.get_config", "name": "get_config", "type": null}}, "max_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.max_length", "name": "max_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "merges": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.merges", "name": "merges", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "pad_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.pad_token_id", "name": "pad_token_id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tf_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.tf_tokenizer", "name": "tf_tokenizer", "type": {".class": "AnyType", "missing_import_name": "transformers.models.gpt2.tokenization_gpt2_tf.BytePairTokenizer", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.gpt2.tokenization_gpt2_tf.BytePairTokenizer", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "vocab": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.vocab", "name": "vocab", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.gpt2.tokenization_gpt2_tf.TFGPT2Tokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pad_model_inputs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.pad_model_inputs", "name": "pad_model_inputs", "type": {".class": "AnyType", "missing_import_name": "transformers.models.gpt2.tokenization_gpt2_tf.pad_model_inputs", "source_any": null, "type_of_any": 3}}}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.gpt2.tokenization_gpt2_tf.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.gpt2.tokenization_gpt2_tf.tf", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\gpt2\\tokenization_gpt2_tf.py"}