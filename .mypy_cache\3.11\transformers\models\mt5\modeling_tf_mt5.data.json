{".class": "MypyFile", "_fullname": "transformers.models.mt5.modeling_tf_mt5", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MT5Config": {".class": "SymbolTableNode", "cross_ref": "transformers.models.mt5.configuration_mt5.MT5Config", "kind": "Gdef", "module_public": false}, "TFMT5EncoderModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.t5.modeling_tf_t5.TFT5EncoderModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5EncoderModel", "name": "TFMT5EncoderModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5EncoderModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.mt5.modeling_tf_mt5", "mro": ["transformers.models.mt5.modeling_tf_mt5.TFMT5EncoderModel", "transformers.models.t5.modeling_tf_t5.TFT5EncoderModel", "transformers.models.t5.modeling_tf_t5.TFT5PreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5EncoderModel.config_class", "name": "config_class", "type": null}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5EncoderModel.model_type", "name": "model_type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5EncoderModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.mt5.modeling_tf_mt5.TFMT5EncoderModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFMT5ForConditionalGeneration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.t5.modeling_tf_t5.TFT5ForConditionalGeneration"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5ForConditionalGeneration", "name": "TFMT5ForConditionalGeneration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5ForConditionalGeneration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.mt5.modeling_tf_mt5", "mro": ["transformers.models.mt5.modeling_tf_mt5.TFMT5ForConditionalGeneration", "transformers.models.t5.modeling_tf_t5.TFT5ForConditionalGeneration", "transformers.models.t5.modeling_tf_t5.TFT5PreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "transformers.modeling_tf_utils.TFCausalLanguageModelingLoss", "builtins.object"], "names": {".class": "SymbolTable", "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5ForConditionalGeneration.config_class", "name": "config_class", "type": null}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5ForConditionalGeneration.model_type", "name": "model_type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5ForConditionalGeneration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.mt5.modeling_tf_mt5.TFMT5ForConditionalGeneration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFMT5Model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.t5.modeling_tf_t5.TFT5Model"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5Model", "name": "TFMT5Model", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5Model", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.mt5.modeling_tf_mt5", "mro": ["transformers.models.mt5.modeling_tf_mt5.TFMT5Model", "transformers.models.t5.modeling_tf_t5.TFT5Model", "transformers.models.t5.modeling_tf_t5.TFT5PreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5Model.config_class", "name": "config_class", "type": null}}, "model_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5Model.model_type", "name": "model_type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.mt5.modeling_tf_mt5.TFMT5Model.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.mt5.modeling_tf_mt5.TFMT5Model", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFT5EncoderModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.t5.modeling_tf_t5.TFT5EncoderModel", "kind": "Gdef", "module_public": false}, "TFT5ForConditionalGeneration": {".class": "SymbolTableNode", "cross_ref": "transformers.models.t5.modeling_tf_t5.TFT5ForConditionalGeneration", "kind": "Gdef", "module_public": false}, "TFT5Model": {".class": "SymbolTableNode", "cross_ref": "transformers.models.t5.modeling_tf_t5.TFT5Model", "kind": "Gdef", "module_public": false}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mt5.modeling_tf_mt5.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mt5.modeling_tf_mt5.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mt5.modeling_tf_mt5.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mt5.modeling_tf_mt5.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mt5.modeling_tf_mt5.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.mt5.modeling_tf_mt5.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.mt5.modeling_tf_mt5.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\mt5\\modeling_tf_mt5.py"}