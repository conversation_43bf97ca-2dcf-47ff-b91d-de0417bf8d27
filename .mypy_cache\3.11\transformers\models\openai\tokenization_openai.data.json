{".class": "MypyFile", "_fullname": "transformers.models.openai.tokenization_openai", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BasicTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer", "name": "BasicTokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.openai.tokenization_openai", "mro": ["transformers.models.openai.tokenization_openai.BasicTokenizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "do_lower_case", "never_split", "tokenize_chinese_chars", "strip_accents", "do_split_on_punc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.__init__", "name": "__init__", "type": null}}, "_clean_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer._clean_text", "name": "_clean_text", "type": null}}, "_is_chinese_char": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer._is_chinese_char", "name": "_is_chinese_char", "type": null}}, "_run_split_on_punc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "never_split"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer._run_split_on_punc", "name": "_run_split_on_punc", "type": null}}, "_run_strip_accents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer._run_strip_accents", "name": "_run_strip_accents", "type": null}}, "_tokenize_chinese_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer._tokenize_chinese_chars", "name": "_tokenize_chinese_chars", "type": null}}, "do_lower_case": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.do_lower_case", "name": "do_lower_case", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "do_split_on_punc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.do_split_on_punc", "name": "do_split_on_punc", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "never_split": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.never_split", "name": "never_split", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "strip_accents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.strip_accents", "name": "strip_accents", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "text", "never_split"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.tokenize", "name": "tokenize", "type": null}}, "tokenize_chinese_chars": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.tokenize_chinese_chars", "name": "tokenize_chinese_chars", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.openai.tokenization_openai.BasicTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.openai.tokenization_openai.BasicTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpenAIGPTTokenizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.tokenization_utils.PreTrainedTokenizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer", "name": "OpenAIGPTTokenizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.openai.tokenization_openai", "mro": ["transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer", "transformers.tokenization_utils.PreTrainedTokenizer", "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "transformers.tokenization_utils_base.SpecialTokensMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "vocab_file", "merges_file", "unk_token", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.__init__", "name": "__init__", "type": null}}, "_convert_id_to_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer._convert_id_to_token", "name": "_convert_id_to_token", "type": null}}, "_convert_token_to_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer._convert_token_to_id", "name": "_convert_token_to_id", "type": null}}, "_tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer._tokenize", "name": "_tokenize", "type": null}}, "bpe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.bpe", "name": "bpe", "type": null}}, "bpe_ranks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.bpe_ranks", "name": "bpe_ranks", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.cache", "name": "cache", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "convert_tokens_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.convert_tokens_to_string", "name": "convert_tokens_to_string", "type": null}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.decoder", "name": "decoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "do_lower_case": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.do_lower_case", "name": "do_lower_case", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.do_lower_case", "name": "do_lower_case", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_lower_case of OpenAIGPTTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.encoder", "name": "encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fix_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.fix_text", "name": "fix_text", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_vocab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.get_vocab", "name": "get_vocab", "type": null}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.model_input_names", "name": "model_input_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "nlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.nlp", "name": "nlp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "save_vocabulary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.save_vocabulary", "name": "save_vocabulary", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "save_directory", "filename_prefix"], "arg_types": ["transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_vocabulary of OpenAIGPTTokenizer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vocab_files_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.vocab_files_names", "name": "vocab_files_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "vocab_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.vocab_size", "name": "vocab_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.vocab_size", "name": "vocab_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vocab_size of OpenAIGPTTokenizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.openai.tokenization_openai.OpenAIGPTTokenizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "VOCAB_FILES_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.openai.tokenization_openai.VOCAB_FILES_NAMES", "name": "VOCAB_FILES_NAMES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.openai.tokenization_openai.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.openai.tokenization_openai.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.openai.tokenization_openai.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.openai.tokenization_openai.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.openai.tokenization_openai.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.openai.tokenization_openai.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.openai.tokenization_openai.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_is_control": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils._is_control", "kind": "Gdef", "module_public": false}, "_is_punctuation": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils._is_punctuation", "kind": "Gdef", "module_public": false}, "_is_whitespace": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils._is_whitespace", "kind": "Gdef", "module_public": false}, "get_pairs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["word"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.get_pairs", "name": "get_pairs", "type": null}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.openai.tokenization_openai.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "text_standardize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.text_standardize", "name": "text_standardize", "type": null}}, "unicodedata": {".class": "SymbolTableNode", "cross_ref": "unicodedata", "kind": "Gdef", "module_public": false}, "whitespace_tokenize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.openai.tokenization_openai.whitespace_tokenize", "name": "whitespace_tokenize", "type": null}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\openai\\tokenization_openai.py"}