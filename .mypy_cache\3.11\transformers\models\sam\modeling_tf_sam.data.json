{".class": "MypyFile", "_fullname": "transformers.models.sam.modeling_tf_sam", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.activations_tf.ACT2FN", "kind": "Gdef", "module_public": false}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "SAM_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.SAM_INPUTS_DOCSTRING", "name": "SAM_INPUTS_DOCSTRING", "type": "builtins.str"}}, "SAM_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.SAM_START_DOCSTRING", "name": "SAM_START_DOCSTRING", "type": "builtins.str"}}, "SAM_VISION_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.SAM_VISION_INPUTS_DOCSTRING", "name": "SAM_VISION_INPUTS_DOCSTRING", "type": "builtins.str"}}, "SamConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.sam.configuration_sam.SamConfig", "kind": "Gdef", "module_public": false}, "SamMaskDecoderConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.sam.configuration_sam.SamMaskDecoderConfig", "kind": "Gdef", "module_public": false}, "SamPromptEncoderConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.sam.configuration_sam.SamPromptEncoderConfig", "kind": "Gdef", "module_public": false}, "SamVisionConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.sam.configuration_sam.SamVisionConfig", "kind": "Gdef", "module_public": false}, "TFBaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFBaseModelOutput", "kind": "Gdef", "module_public": false}, "TFModelInputType": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFModelInputType", "kind": "Gdef", "module_public": false}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef", "module_public": false}, "TFSamAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention", "name": "TFSamAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "downsample_rate", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.__init__", "name": "__init__", "type": null}}, "_recombine_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "point_batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention._recombine_heads", "name": "_recombine_heads", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "point_batch_size"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamAttention", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_recombine_heads of TFSamAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_separate_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "num_attention_heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention._separate_heads", "name": "_separate_heads", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "num_attention_heads"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamAttention", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_separate_heads of TFSamAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query", "key", "value"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamAttention", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.hidden_size", "name": "hidden_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "internal_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.internal_dim", "name": "internal_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "k_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.k_proj", "name": "k_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.num_attention_heads", "name": "num_attention_heads", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "out_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.out_proj", "name": "out_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "q_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.q_proj", "name": "q_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "v_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.v_proj", "name": "v_proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamFeedForward": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward", "name": "TFSamFeedForward", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamFeedForward", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "input_dim", "hidden_dim", "output_dim", "num_layers", "sigmoid_output", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "input_dim", "hidden_dim", "output_dim", "num_layers", "sigmoid_output", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamFeedForward", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamFeedForward", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.call", "name": "call", "type": null}}, "hidden_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.hidden_dim", "name": "hidden_dim", "type": "builtins.int"}}, "input_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.input_dim", "name": "input_dim", "type": "builtins.int"}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.layers", "name": "layers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "num_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.num_layers", "name": "num_layers", "type": "builtins.int"}}, "proj_in": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.proj_in", "name": "proj_in", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "proj_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.proj_out", "name": "proj_out", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "sigmoid_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.sigmoid_output", "name": "sigmoid_output", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamImageSegmentationOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput", "name": "TFSamImageSegmentationOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 108, "name": "iou_scores", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 109, "name": "pred_masks", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 110, "name": "vision_hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 111, "name": "vision_attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 112, "name": "mask_decoder_attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "iou_scores", "pred_masks", "vision_hidden_states", "vision_attentions", "mask_decoder_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "iou_scores", "pred_masks", "vision_hidden_states", "vision_attentions", "mask_decoder_attentions"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamImageSegmentationOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iou_scores"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pred_masks"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vision_hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vision_attentions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mask_decoder_attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["iou_scores", "pred_masks", "vision_hidden_states", "vision_attentions", "mask_decoder_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["iou_scores", "pred_masks", "vision_hidden_states", "vision_attentions", "mask_decoder_attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFSamImageSegmentationOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["iou_scores", "pred_masks", "vision_hidden_states", "vision_attentions", "mask_decoder_attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFSamImageSegmentationOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "iou_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.iou_scores", "name": "iou_scores", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "mask_decoder_attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.mask_decoder_attentions", "name": "mask_decoder_attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "pred_masks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.pred_masks", "name": "pred_masks", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "vision_attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.vision_attentions", "name": "vision_attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "vision_hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.vision_hidden_states", "name": "vision_hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamLayerNorm": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm", "name": "TFSamLayerNorm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamLayerNorm", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "normalized_shape", "eps", "data_format", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.__init__", "name": "__init__", "type": null}}, "bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.bias", "name": "bias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamLayerNorm", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamLayerNorm", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.data_format", "name": "data_format", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.eps", "name": "eps", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "normalized_shape": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.normalized_shape", "name": "normalized_shape", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.weight", "name": "weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamMLPBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock", "name": "TFSamMLPBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamMLPBlock", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.__init__", "name": "__init__", "type": null}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.act", "name": "act", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamMLPBlock", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamMLPBlock", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "lin1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.lin1", "name": "lin1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "lin2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.lin2", "name": "lin2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamMaskDecoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder", "name": "TFSamMaskDecoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder", "transformers.models.sam.configuration_sam.SamMaskDecoderConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamMaskDecoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "image_embeddings", "image_positional_embeddings", "sparse_prompt_embeddings", "dense_prompt_embeddings", "multimask_output", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "image_embeddings", "image_positional_embeddings", "sparse_prompt_embeddings", "dense_prompt_embeddings", "multimask_output", "output_attentions"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamMaskDecoder", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.hidden_size", "name": "hidden_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "iou_prediction_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.iou_prediction_head", "name": "iou_prediction_head", "type": "transformers.models.sam.modeling_tf_sam.TFSamFeedForward"}}, "iou_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.iou_token", "name": "iou_token", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mask_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.mask_tokens", "name": "mask_tokens", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_mask_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.num_mask_tokens", "name": "num_mask_tokens", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "num_multimask_outputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.num_multimask_outputs", "name": "num_multimask_outputs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output_hypernetworks_mlps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.output_hypernetworks_mlps", "name": "output_hypernetworks_mlps", "type": {".class": "Instance", "args": ["transformers.models.sam.modeling_tf_sam.TFSamFeedForward"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "transformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.transformer", "name": "transformer", "type": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer"}}, "upscale_conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.upscale_conv1", "name": "upscale_conv1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "upscale_conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.upscale_conv2", "name": "upscale_conv2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "upscale_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.upscale_layer_norm", "name": "upscale_layer_norm", "type": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamMaskDecoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamMaskEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding", "name": "TFSamMaskEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding", "transformers.models.sam.configuration_sam.SamPromptEncoderConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamMaskEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.activation", "name": "activation", "type": {".class": "AnyType", "missing_import_name": "transformers.activations_tf.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.activations_tf.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.call", "name": "call", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.config", "name": "config", "type": "transformers.models.sam.configuration_sam.SamPromptEncoderConfig"}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.conv1", "name": "conv1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.conv2", "name": "conv2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "conv3": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.conv3", "name": "conv3", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layer_norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.layer_norm1", "name": "layer_norm1", "type": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm"}}, "layer_norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.layer_norm2", "name": "layer_norm2", "type": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm"}}, "mask_input_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.mask_input_channels", "name": "mask_input_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel", "name": "TFSamModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamModel", "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.__init__", "name": "__init__", "type": null}}, "_keys_to_ignore_on_load_missing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel._keys_to_ignore_on_load_missing", "name": "_keys_to_ignore_on_load_missing", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "pixel_values", "input_points", "input_labels", "input_boxes", "input_masks", "image_embeddings", "multimask_output", "output_attentions", "output_hidden_states", "return_dict", "training", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "pixel_values", "input_points", "input_labels", "input_boxes", "input_masks", "image_embeddings", "multimask_output", "output_attentions", "output_hidden_states", "return_dict", "training", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamModel", "ret_type": {".class": "UnionType", "items": ["transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_image_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.get_image_embeddings", "name": "get_image_embeddings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_image_embeddings of TFSamModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_image_wide_positional_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.get_image_wide_positional_embeddings", "name": "get_image_wide_positional_embeddings", "type": null}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "get_prompt_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "input_points", "input_labels", "input_boxes", "input_masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.get_prompt_embeddings", "name": "get_prompt_embeddings", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "input_points", "input_labels", "input_boxes", "input_masks"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamModel", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_prompt_embeddings of TFSamModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mask_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.mask_decoder", "name": "mask_decoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prompt_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.prompt_encoder", "name": "prompt_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "serving_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.serving_output", "name": "serving_output", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "output"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamModel", "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "serving_output of TFSamModel", "ret_type": "transformers.models.sam.modeling_tf_sam.TFSamImageSegmentationOutput", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shared_image_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.shared_image_embedding", "name": "shared_image_embedding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vision_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.vision_encoder", "name": "vision_encoder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamPatchEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings", "name": "TFSamPatchEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.__init__", "name": "__init__", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pixel_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.call", "name": "call", "type": null}}, "image_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.image_size", "name": "image_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.num_channels", "name": "num_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_patches": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.num_patches", "name": "num_patches", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "patch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.patch_size", "name": "patch_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "projection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.projection", "name": "projection", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamPositionalEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding", "name": "TFSamPositionalEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding.__init__", "name": "__init__", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_coords", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding.call", "name": "call", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "positional_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding.positional_embedding", "name": "positional_embedding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding.scale", "name": "scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamPositionalEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_utils.TFPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel", "name": "TFSamPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel.config_class", "name": "config_class", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamPromptEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", "name": "TFSamPromptEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "shared_patch_embedding", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "shared_patch_embedding", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", "transformers.models.sam.configuration_sam.SamPromptEncoderConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamPromptEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_embed_boxes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "boxes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder._embed_boxes", "name": "_embed_boxes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "boxes"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_embed_boxes of TFSamPromptEncoder", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_embed_points": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "points", "labels", "pad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder._embed_points", "name": "_embed_points", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "points", "labels", "pad"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_embed_points of TFSamPromptEncoder", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "batch_size", "input_points", "input_labels", "input_boxes", "input_masks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "batch_size", "input_points", "input_labels", "input_boxes", "input_masks"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamPromptEncoder", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.config", "name": "config", "type": "transformers.models.sam.configuration_sam.SamPromptEncoderConfig"}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.hidden_size", "name": "hidden_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "image_embedding_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.image_embedding_size", "name": "image_embedding_size", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "input_image_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.input_image_size", "name": "input_image_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mask_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.mask_embed", "name": "mask_embed", "type": "transformers.models.sam.modeling_tf_sam.TFSamMaskEmbedding"}}, "no_mask_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.no_mask_embed", "name": "no_mask_embed", "type": {".class": "NoneType"}}}, "not_a_point_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.not_a_point_embed", "name": "not_a_point_embed", "type": {".class": "NoneType"}}}, "point_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.point_embed", "name": "point_embed", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "shared_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.shared_embedding", "name": "shared_embedding", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamPromptEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamTwoWayAttentionBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock", "name": "TFSamTwoWayAttentionBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "config", "attention_downsample_rate", "skip_first_layer_pe", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "config", "attention_downsample_rate", "skip_first_layer_pe", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamTwoWayAttentionBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "queries", "keys", "query_point_embedding", "key_point_embedding", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "queries", "keys", "query_point_embedding", "key_point_embedding", "output_attentions"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamTwoWayAttentionBlock", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cross_attn_image_to_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.cross_attn_image_to_token", "name": "cross_attn_image_to_token", "type": "transformers.models.sam.modeling_tf_sam.TFSamAttention"}}, "cross_attn_token_to_image": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.cross_attn_token_to_image", "name": "cross_attn_token_to_image", "type": "transformers.models.sam.modeling_tf_sam.TFSamAttention"}}, "hidden_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.hidden_size", "name": "hidden_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "layer_norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.layer_norm1", "name": "layer_norm1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layer_norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.layer_norm2", "name": "layer_norm2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layer_norm3": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.layer_norm3", "name": "layer_norm3", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layer_norm4": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.layer_norm4", "name": "layer_norm4", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layer_norm_eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.layer_norm_eps", "name": "layer_norm_eps", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.mlp", "name": "mlp", "type": "transformers.models.sam.modeling_tf_sam.TFSamMLPBlock"}}, "self_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.self_attn", "name": "self_attn", "type": "transformers.models.sam.modeling_tf_sam.TFSamAttention"}}, "skip_first_layer_pe": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.skip_first_layer_pe", "name": "skip_first_layer_pe", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamTwoWayTransformer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer", "name": "TFSamTwoWayTransformer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer", "transformers.models.sam.configuration_sam.SamMaskDecoderConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamTwoWayTransformer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "point_embeddings", "image_embeddings", "image_positional_embeddings", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "point_embeddings", "image_embeddings", "image_positional_embeddings", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamTwoWayTransformer", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_tf_outputs.TFBaseModelOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.config", "name": "config", "type": "transformers.models.sam.configuration_sam.SamMaskDecoderConfig"}}, "final_attn_token_to_image": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.final_attn_token_to_image", "name": "final_attn_token_to_image", "type": "transformers.models.sam.modeling_tf_sam.TFSamAttention"}}, "layer_norm_final_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.layer_norm_final_attn", "name": "layer_norm_final_attn", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.layers", "name": "layers", "type": {".class": "Instance", "args": ["transformers.models.sam.modeling_tf_sam.TFSamTwoWayAttentionBlock"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "num_hidden_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.num_hidden_layers", "name": "num_hidden_layers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamTwoWayTransformer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamVisionAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention", "name": "TFSamVisionAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamVisionAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.__init__", "name": "__init__", "type": null}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionAttention", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamVisionAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.dropout", "name": "dropout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_decomposed_rel_pos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "query", "rel_pos_h", "rel_pos_w", "q_size", "k_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.get_decomposed_rel_pos", "name": "get_decomposed_rel_pos", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "query", "rel_pos_h", "rel_pos_w", "q_size", "k_size"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionAttention", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_decomposed_rel_pos of TFSamVisionAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_rel_pos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "q_size", "k_size", "rel_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.get_rel_pos", "name": "get_rel_pos", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "q_size", "k_size", "rel_pos"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionAttention", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_rel_pos of TFSamVisionAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.head_dim", "name": "head_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "input_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.input_size", "name": "input_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.num_attention_heads", "name": "num_attention_heads", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.proj", "name": "proj", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "qkv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.qkv", "name": "qkv", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rel_pos_h": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.rel_pos_h", "name": "rel_pos_h", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rel_pos_w": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.rel_pos_w", "name": "rel_pos_w", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.scale", "name": "scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "use_rel_pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.use_rel_pos", "name": "use_rel_pos", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamVisionAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamVisionEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder", "name": "TFSamVisionEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder", "transformers.models.sam.configuration_sam.SamVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamVisionEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamVisionEncoder", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.config", "name": "config", "type": "transformers.models.sam.configuration_sam.SamVisionConfig"}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "image_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.image_size", "name": "image_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.layers", "name": "layers", "type": {".class": "Instance", "args": ["transformers.models.sam.modeling_tf_sam.TFSamVisionLayer"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "neck": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.neck", "name": "neck", "type": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck"}}, "patch_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.patch_embed", "name": "patch_embed", "type": "transformers.models.sam.modeling_tf_sam.TFSamPatchEmbeddings"}}, "pos_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.pos_embed", "name": "pos_embed", "type": {".class": "NoneType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamVisionEncoderOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ModelOutput"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput", "name": "TFSamVisionEncoderOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "image_embeds", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 76, "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput", "transformers.utils.generic.ModelOutput", "collections.OrderedDict", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "image_embeds", "last_hidden_state", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "image_embeds", "last_hidden_state", "hidden_states", "attentions"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamVisionEncoderOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "image_embeds"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last_hidden_state"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hidden_states"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "attentions"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["image_embeds", "last_hidden_state", "hidden_states", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["image_embeds", "last_hidden_state", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFSamVisionEncoderOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["image_embeds", "last_hidden_state", "hidden_states", "attentions"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of TFSamVisionEncoderOutput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "attentions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.attentions", "name": "attentions", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "hidden_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.hidden_states", "name": "hidden_states", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "image_embeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.image_embeds", "name": "image_embeds", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "last_hidden_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.last_hidden_state", "name": "last_hidden_state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamVisionLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer", "name": "TFSamVisionLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamVisionLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "window_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.__init__", "name": "__init__", "type": null}}, "attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.attn", "name": "attn", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hidden_states", "output_attentions", "training"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionLayer", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamVisionLayer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.layer_norm1", "name": "layer_norm1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.layer_norm2", "name": "layer_norm2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mlp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.mlp", "name": "mlp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "window_partition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "window_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.window_partition", "name": "window_partition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "window_size"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionLayer", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "window_partition of TFSamVisionLayer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "window_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.window_size", "name": "window_size", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "window_unpartition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "windows", "window_size", "padding_shape", "original_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.window_unpartition", "name": "window_unpartition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "windows", "window_size", "padding_shape", "original_shape"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionLayer", {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "window_unpartition of TFSamVisionLayer", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamVisionLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamVisionModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel", "name": "TFSamVisionModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamVisionModel", "transformers.models.sam.modeling_tf_sam.TFSamPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionModel", "transformers.models.sam.configuration_sam.SamVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamVisionModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "pixel_values", "output_attentions", "output_hidden_states", "return_dict", "training", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.modeling_tf_utils.TFModelInputType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSamVisionModel", "ret_type": {".class": "UnionType", "items": ["transformers.models.sam.modeling_tf_sam.TFSamVisionEncoderOutput", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.config_class", "name": "config_class", "type": null}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}, "vision_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.vision_encoder", "name": "vision_encoder", "type": "transformers.models.sam.modeling_tf_sam.TFSamVisionEncoder"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamVisionModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSamVisionNeck": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck", "name": "TFSamVisionNeck", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.sam.modeling_tf_sam", "mro": ["transformers.models.sam.modeling_tf_sam.TFSamVisionNeck", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.sam.modeling_tf_sam.TFSamVisionNeck", "transformers.models.sam.configuration_sam.SamVisionConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSamVisionNeck", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.call", "name": "call", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.config", "name": "config", "type": "transformers.models.sam.configuration_sam.SamVisionConfig"}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.conv1", "name": "conv1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.conv2", "name": "conv2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layer_norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.layer_norm1", "name": "layer_norm1", "type": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm"}}, "layer_norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.layer_norm2", "name": "layer_norm2", "type": "transformers.models.sam.modeling_tf_sam.TFSamLayerNorm"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.sam.modeling_tf_sam.TFSamVisionNeck", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.sam.modeling_tf_sam.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "flatten": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.flatten", "kind": "Gdef", "module_public": false}, "functional_layernorm": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.functional_layernorm", "kind": "Gdef", "module_public": false}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.sam.modeling_tf_sam.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef", "module_public": false}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "shape_list": {".class": "SymbolTableNode", "cross_ref": "transformers.tf_utils.shape_list", "kind": "Gdef", "module_public": false}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.sam.modeling_tf_sam.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.sam.modeling_tf_sam.tf", "source_any": null, "type_of_any": 3}}}, "unpack_inputs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.unpack_inputs", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\sam\\modeling_tf_sam.py"}