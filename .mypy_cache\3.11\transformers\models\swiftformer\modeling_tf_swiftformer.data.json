{".class": "MypyFile", "_fullname": "transformers.models.swiftformer.modeling_tf_swiftformer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "SwiftFormerConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "kind": "Gdef", "module_public": false}, "TFBaseModelOutputWithNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFBaseModelOutputWithNoAttention", "kind": "Gdef", "module_public": false}, "TFImageClassifierOutputWithNoAttention": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_outputs.TFImageClassifierOutputWithNoAttention", "kind": "Gdef", "module_public": false}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef", "module_public": false}, "TFSWIFTFORMER_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSWIFTFORMER_INPUTS_DOCSTRING", "name": "TFSWIFTFORMER_INPUTS_DOCSTRING", "type": "builtins.str"}}, "TFSWIFTFORMER_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSWIFTFORMER_START_DOCSTRING", "name": "TFSWIFTFORMER_START_DOCSTRING", "type": "builtins.str"}}, "TFSwiftFormerConvEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder", "name": "TFSwiftFormerConvEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "dim", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "dim", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerConvEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.act", "name": "act", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerConvEncoder", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "depth_wise_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.depth_wise_conv", "name": "depth_wise_conv", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.dim", "name": "dim", "type": "builtins.int"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.drop_path", "name": "drop_path", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "hidden_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.hidden_dim", "name": "hidden_dim", "type": "builtins.int"}}, "layer_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.layer_scale", "name": "layer_scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.norm", "name": "norm", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "pad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.pad", "name": "pad", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_wise_conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.point_wise_conv1", "name": "point_wise_conv1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_wise_conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.point_wise_conv2", "name": "point_wise_conv2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerConvEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerDropPath": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath", "name": "TFSwiftFormerDropPath", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerDropPath", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "hidden_states", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerDropPath", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerEfficientAdditiveAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention", "name": "TFSwiftFormerEfficientAdditiveAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "dim", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "config", "dim", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerEfficientAdditiveAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerEfficientAdditiveAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.dim", "name": "dim", "type": "builtins.int"}}, "final": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.final", "name": "final", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.proj", "name": "proj", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "scale_factor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.scale_factor", "name": "scale_factor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "to_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.to_key", "name": "to_key", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "to_query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.to_query", "name": "to_query", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "w_g": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.w_g", "name": "w_g", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings", "name": "TFSwiftFormerEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "index", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "index", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.embed_dim", "name": "embed_dim", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "in_chans": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.in_chans", "name": "in_chans", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.norm", "name": "norm", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "pad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.pad", "name": "pad", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.proj", "name": "proj", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder", "name": "TFSwiftFormerEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "hidden_states", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerEncoder", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_tf_outputs.TFBaseModelOutputWithNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder.config", "name": "config", "type": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "type": "builtins.bool"}}, "network": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder.network", "name": "network", "type": {".class": "Instance", "args": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerEncoderBlock": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock", "name": "TFSwiftFormerEncoderBlock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "config", "dim", "drop_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "config", "dim", "drop_path", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerEncoderBlock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.attn", "name": "attn", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEfficientAdditiveAttention"}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerEncoderBlock", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.dim", "name": "dim", "type": "builtins.int"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.drop_path", "name": "drop_path", "type": {".class": "UnionType", "items": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerDropPath", {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "uses_pep604_syntax": false}}}, "layer_scale_1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.layer_scale_1", "name": "layer_scale_1", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_scale_2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.layer_scale_2", "name": "layer_scale_2", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer_scale_init_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.layer_scale_init_value", "name": "layer_scale_init_value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "linear": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.linear", "name": "linear", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp"}}, "local_representation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.local_representation", "name": "local_representation", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation"}}, "use_layer_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.use_layer_scale", "name": "use_layer_scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerForImageClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification", "name": "TFSwiftFormerForImageClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification", "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerForImageClassification", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "labels", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerForImageClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_tf_outputs.TFImageClassifierOutputWithNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "dist_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.dist_head", "name": "dist_head", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.head", "name": "head", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "hf_compute_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "labels", "logits"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.hf_compute_loss", "name": "hf_compute_loss", "type": null}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.norm", "name": "norm", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.num_labels", "name": "num_labels", "type": "builtins.int"}}, "swiftformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.swiftformer", "name": "swiftformer", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerForImageClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerLocalRepresentation": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation", "name": "TFSwiftFormerLocalRepresentation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "dim", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "dim", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerLocalRepresentation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.act", "name": "act", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerLocalRepresentation", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "depth_wise_conv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.depth_wise_conv", "name": "depth_wise_conv", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.dim", "name": "dim", "type": "builtins.int"}}, "drop_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.drop_path", "name": "drop_path", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layer_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.layer_scale", "name": "layer_scale", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.norm", "name": "norm", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "pad": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.pad", "name": "pad", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_wise_conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.point_wise_conv1", "name": "point_wise_conv1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "point_wise_conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.point_wise_conv2", "name": "point_wise_conv2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerLocalRepresentation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerMainLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer", "name": "TFSwiftFormerMainLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerMainLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerMainLayer", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_tf_outputs.TFBaseModelOutputWithNoAttention"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.config", "name": "config", "type": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.config_class", "name": "config_class", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["image_size", "num_channels", "depths", "embed_dims", "mlp_ratio", "downsamples", "hidden_act", "down_patch_size", "down_stride", "down_pad", "drop_path_rate", "drop_mlp_rate", "drop_conv_encoder_rate", "use_layer_scale", "layer_scale_init_value", "batch_norm_eps", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.encoder", "name": "encoder", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoder"}}, "patch_embed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.patch_embed", "name": "patch_embed", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerMlp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp", "name": "TFSwiftFormerMlp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "in_features", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "in_features", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerMlp", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "act": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.act", "name": "act", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerMlp", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.drop", "name": "drop", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fc1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.fc1", "name": "fc1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fc2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.fc2", "name": "fc2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "hidden_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.hidden_features", "name": "hidden_features", "type": "builtins.int"}}, "in_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.in_features", "name": "in_features", "type": "builtins.int"}}, "norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.norm1", "name": "norm1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMlp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel", "name": "TFSwiftFormerModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel", "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "config", "inputs", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "pixel_values", "output_hidden_states", "return_dict", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerModel", "ret_type": {".class": "UnionType", "items": ["transformers.modeling_tf_outputs.TFBaseModelOutputWithNoAttention", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel.call", "name": "call", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "swiftformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel.swiftformer", "name": "swiftformer", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerMainLayer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerPatchEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding", "name": "TFSwiftFormerPatchEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerPatchEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerPatchEmbedding", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "patch_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding.patch_embedding", "name": "patch_embedding", "type": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerPatchEmbeddingSequential": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential", "name": "TFSwiftFormerPatchEmbeddingSequential", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "config", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerPatchEmbeddingSequential", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_norm1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.batch_norm1", "name": "batch_norm1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "batch_norm2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.batch_norm2", "name": "batch_norm2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.build", "name": "build", "type": null}}, "built": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.built", "name": "built", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerPatchEmbeddingSequential", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.config", "name": "config", "type": "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig"}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.conv1", "name": "conv1", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.conv2", "name": "conv2", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "out_chs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.out_chs", "name": "out_chs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "zero_padding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.zero_padding", "name": "zero_padding", "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_tf_utils.keras", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPatchEmbeddingSequential", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_tf_utils.TFPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel", "name": "TFSwiftFormerPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", "transformers.modeling_tf_utils.TFModelUtilsMixin", "transformers.generation.tf_utils.TFGenerationMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel.config_class", "name": "config_class", "type": null}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel.main_input_name", "name": "main_input_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TFSwiftFormerStage": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage", "name": "TFSwiftFormerStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.swiftformer.modeling_tf_swiftformer", "mro": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "index", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "config", "index", "kwargs"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage", "transformers.models.swiftformer.configuration_swiftformer.SwiftFormerConfig", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TFSwiftFormerStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage.blocks", "name": "blocks", "type": {".class": "Instance", "args": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerEncoderBlock"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "build": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage.build", "name": "build", "type": null}}, "call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "training"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage.call", "name": "call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input", "training"], "arg_types": ["transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage", {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call of TFSwiftFormerStage", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.swiftformer.modeling_tf_swiftformer.TFSwiftFormerStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "type": "builtins.str"}}, "_EXPECTED_OUTPUT_SHAPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer._EXPECTED_OUTPUT_SHAPE", "name": "_EXPECTED_OUTPUT_SHAPE", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_IMAGE_CLASS_CHECKPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer._IMAGE_CLASS_CHECKPOINT", "name": "_IMAGE_CLASS_CHECKPOINT", "type": "builtins.str"}}, "_IMAGE_CLASS_EXPECTED_OUTPUT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer._IMAGE_CLASS_EXPECTED_OUTPUT", "name": "_IMAGE_CLASS_EXPECTED_OUTPUT", "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "get_tf_activation": {".class": "SymbolTableNode", "cross_ref": "transformers.activations_tf.get_tf_activation", "kind": "Gdef", "module_public": false}, "keras": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras", "kind": "Gdef", "module_public": false}, "keras_serializable": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.keras_serializable", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.models.swiftformer.modeling_tf_swiftformer.tf", "source_any": null, "type_of_any": 3}}}, "unpack_inputs": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.unpack_inputs", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\swiftformer\\modeling_tf_swiftformer.py"}