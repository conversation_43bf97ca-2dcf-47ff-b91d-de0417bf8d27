{"data_mtime": 1753783924, "dep_lines": [46, 38, 19, 28, 29, 37, 38, 17, 19, 20, 21, 22, 23, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 20, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.swin.configuration_swin", "transformers.utils.logging", "collections.abc", "transformers.activations_tf", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "collections", "math", "warnings", "dataclasses", "functools", "typing", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "numpy", "_frozen_importlib", "_typeshed", "abc", "transformers.configuration_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.utils.backbone_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "07abf360974f1bb18a72a64448f72f8e00849d67", "id": "transformers.models.swin.modeling_tf_swin", "ignore_all": true, "interface_hash": "1c34e2650dcdd6480e2f41c5befad9aae84ebc45", "mtime": 1746815063, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\swin\\modeling_tf_swin.py", "plugin_data": null, "size": 70967, "suppressed": ["tensorflow"], "version_id": "1.15.0"}