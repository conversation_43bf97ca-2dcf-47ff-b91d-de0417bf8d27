{"data_mtime": 1753783925, "dep_lines": [34, 35, 36, 37, 27, 24, 25, 26, 27, 17, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.modeling_tf_auto", "transformers.models.clip.modeling_tf_clip", "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder", "transformers.utils.logging", "transformers.configuration_utils", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "re", "typing", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "transformers.generation", "transformers.generation.tf_utils", "transformers.modeling_tf_outputs", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.models.clip", "transformers.models.clip.configuration_clip", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "432501d1fb72957b2706ac01cf0e44faa7e4515d", "id": "transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder", "ignore_all": true, "interface_hash": "7e88c70e7d53abb46d8f8b2d34bc6a4cbaf8d491", "mtime": 1746815063, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\modeling_tf_vision_text_dual_encoder.py", "plugin_data": null, "size": 28705, "suppressed": ["tensorflow"], "version_id": "1.15.0"}