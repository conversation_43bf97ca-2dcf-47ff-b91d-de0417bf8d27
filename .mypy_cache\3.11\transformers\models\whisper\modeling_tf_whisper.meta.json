{"data_mtime": 1753783923, "dep_lines": [45, 46, 27, 28, 44, 26, 29, 35, 43, 44, 17, 19, 20, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.whisper.configuration_whisper", "transformers.models.whisper.tokenization_whisper", "transformers.generation.configuration_utils", "transformers.generation.tf_logits_process", "transformers.utils.logging", "transformers.activations_tf", "transformers.modeling_tf_outputs", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "math", "random", "typing", "numpy", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "transformers.configuration_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "bbd37aacbf7e1af25417e571e90059e4fbdff88c", "id": "transformers.models.whisper.modeling_tf_whisper", "ignore_all": true, "interface_hash": "c9fea8a4b3493c5a95883f4aa47d1caa945bc33b", "mtime": 1746815063, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\models\\whisper\\modeling_tf_whisper.py", "plugin_data": null, "size": 84861, "suppressed": ["tensorflow"], "version_id": "1.15.0"}