{".class": "MypyFile", "_fullname": "transformers.pipelines", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AggregationStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.token_classification.AggregationStrategy", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ArgumentHandler": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.ArgumentHandler", "kind": "Gdef"}, "AudioClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.audio_classification.AudioClassificationPipeline", "kind": "Gdef"}, "AutoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.AutoConfig", "kind": "Gdef"}, "AutoFeatureExtractor": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.feature_extraction_auto.AutoFeatureExtractor", "kind": "Gdef"}, "AutoImageProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.image_processing_auto.AutoImageProcessor", "kind": "Gdef"}, "AutoModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModel", "kind": "Gdef"}, "AutoModelForAudioClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForAudioClassification", "kind": "Gdef"}, "AutoModelForCTC": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForCTC", "kind": "Gdef"}, "AutoModelForCausalLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForCausalLM", "kind": "Gdef"}, "AutoModelForDepthEstimation": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForDepthEstimation", "kind": "Gdef"}, "AutoModelForDocumentQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForDocumentQuestionAnswering", "kind": "Gdef"}, "AutoModelForImageClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForImageClassification", "kind": "Gdef"}, "AutoModelForImageSegmentation": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForImageSegmentation", "kind": "Gdef"}, "AutoModelForImageTextToText": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForImageTextToText", "kind": "Gdef"}, "AutoModelForImageToImage": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForImageToImage", "kind": "Gdef"}, "AutoModelForMaskGeneration": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForMaskGeneration", "kind": "Gdef"}, "AutoModelForMaskedLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForMaskedLM", "kind": "Gdef"}, "AutoModelForObjectDetection": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForObjectDetection", "kind": "Gdef"}, "AutoModelForQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForQuestionAnswering", "kind": "Gdef"}, "AutoModelForSemanticSegmentation": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSemanticSegmentation", "kind": "Gdef"}, "AutoModelForSeq2SeqLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSeq2SeqLM", "kind": "Gdef"}, "AutoModelForSequenceClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSequenceClassification", "kind": "Gdef"}, "AutoModelForSpeechSeq2Seq": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForSpeechSeq2Seq", "kind": "Gdef"}, "AutoModelForTableQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForTableQuestionAnswering", "kind": "Gdef"}, "AutoModelForTextToSpectrogram": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForTextToSpectrogram", "kind": "Gdef"}, "AutoModelForTextToWaveform": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForTextToWaveform", "kind": "Gdef"}, "AutoModelForTokenClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForTokenClassification", "kind": "Gdef"}, "AutoModelForVideoClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForVideoClassification", "kind": "Gdef"}, "AutoModelForVision2Seq": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForVision2Seq", "kind": "Gdef"}, "AutoModelForVisualQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForVisualQuestionAnswering", "kind": "Gdef"}, "AutoModelForZeroShotImageClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForZeroShotImageClassification", "kind": "Gdef"}, "AutoModelForZeroShotObjectDetection": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.AutoModelForZeroShotObjectDetection", "kind": "Gdef"}, "AutoProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.processing_auto.AutoProcessor", "kind": "Gdef"}, "AutoTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.tokenization_auto.AutoTokenizer", "kind": "Gdef"}, "AutomaticSpeechRecognitionPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.automatic_speech_recognition.AutomaticSpeechRecognitionPipeline", "kind": "Gdef"}, "BaseImageProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_utils.BaseImageProcessor", "kind": "Gdef"}, "CONFIG_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.CONFIG_NAME", "kind": "Gdef"}, "CsvPipelineDataFormat": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.CsvPipelineDataFormat", "kind": "Gdef"}, "DepthEstimationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.depth_estimation.DepthEstimationPipeline", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocumentQuestionAnsweringPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", "kind": "Gdef"}, "FEATURE_EXTRACTOR_MAPPING": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.feature_extraction_auto.FEATURE_EXTRACTOR_MAPPING", "kind": "Gdef"}, "FeatureExtractionPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.feature_extraction.FeatureExtractionPipeline", "kind": "Gdef"}, "FillMaskPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.fill_mask.FillMaskPipeline", "kind": "Gdef"}, "HUGGINGFACE_CO_RESOLVE_ENDPOINT": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.HUGGINGFACE_CO_RESOLVE_ENDPOINT", "kind": "Gdef"}, "IMAGE_PROCESSOR_MAPPING": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.image_processing_auto.IMAGE_PROCESSOR_MAPPING", "kind": "Gdef"}, "ImageClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.image_classification.ImageClassificationPipeline", "kind": "Gdef"}, "ImageFeatureExtractionPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.image_feature_extraction.ImageFeatureExtractionPipeline", "kind": "Gdef"}, "ImageSegmentationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.image_segmentation.ImageSegmentationPipeline", "kind": "Gdef"}, "ImageTextToTextPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.image_text_to_text.ImageTextToTextPipeline", "kind": "Gdef"}, "ImageToImagePipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.image_to_image.ImageToImagePipeline", "kind": "Gdef"}, "ImageToTextPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.image_to_text.ImageToTextPipeline", "kind": "Gdef"}, "JsonPipelineDataFormat": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.JsonPipelineDataFormat", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MULTI_MODEL_AUDIO_CONFIGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.MULTI_MODEL_AUDIO_CONFIGS", "name": "MULTI_MODEL_AUDIO_CONFIGS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "MULTI_MODEL_VISION_CONFIGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.MULTI_MODEL_VISION_CONFIGS", "name": "MULTI_MODEL_VISION_CONFIGS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "MaskGenerationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.mask_generation.MaskGenerationPipeline", "kind": "Gdef"}, "NO_FEATURE_EXTRACTOR_TASKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.NO_FEATURE_EXTRACTOR_TASKS", "name": "NO_FEATURE_EXTRACTOR_TASKS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "NO_IMAGE_PROCESSOR_TASKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.NO_IMAGE_PROCESSOR_TASKS", "name": "NO_IMAGE_PROCESSOR_TASKS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "NO_TOKENIZER_TASKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.NO_TOKENIZER_TASKS", "name": "NO_TOKENIZER_TASKS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "NerPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.token_classification.NerPipeline", "kind": "Gdef"}, "ObjectDetectionPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.object_detection.ObjectDetectionPipeline", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PIPELINE_REGISTRY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.PIPELINE_REGISTRY", "name": "PIPELINE_REGISTRY", "type": "transformers.pipelines.base.PipelineRegistry"}}, "PROCESSOR_MAPPING": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.processing_auto.PROCESSOR_MAPPING", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PipedPipelineDataFormat": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.PipedPipelineDataFormat", "kind": "Gdef"}, "Pipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.Pipeline", "kind": "Gdef"}, "PipelineDataFormat": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.PipelineDataFormat", "kind": "Gdef"}, "PipelineException": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.PipelineException", "kind": "Gdef"}, "PipelineRegistry": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.PipelineRegistry", "kind": "Gdef"}, "PreTrainedFeatureExtractor": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.PreTrainedFeatureExtractor", "kind": "Gdef"}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef"}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef"}, "PreTrainedTokenizerFast": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "kind": "Gdef"}, "PretrainedConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.configuration_utils.PretrainedConfig", "kind": "Gdef"}, "ProcessorMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.ProcessorMixin", "kind": "Gdef"}, "QuestionAnsweringArgumentHandler": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler", "kind": "Gdef"}, "QuestionAnsweringPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.question_answering.QuestionAnsweringPipeline", "kind": "Gdef"}, "SUPPORTED_TASKS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.SUPPORTED_TASKS", "name": "SUPPORTED_TASKS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SummarizationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.text2text_generation.SummarizationPipeline", "kind": "Gdef"}, "TASK_ALIASES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.TASK_ALIASES", "name": "TASK_ALIASES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "TFAutoModel": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModel", "kind": "Gdef"}, "TFAutoModelForCausalLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForCausalLM", "kind": "Gdef"}, "TFAutoModelForImageClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForImageClassification", "kind": "Gdef"}, "TFAutoModelForMaskedLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForMaskedLM", "kind": "Gdef"}, "TFAutoModelForQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForQuestionAnswering", "kind": "Gdef"}, "TFAutoModelForSeq2SeqLM": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForSeq2SeqLM", "kind": "Gdef"}, "TFAutoModelForSequenceClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForSequenceClassification", "kind": "Gdef"}, "TFAutoModelForTableQuestionAnswering": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForTableQuestionAnswering", "kind": "Gdef"}, "TFAutoModelForTokenClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForTokenClassification", "kind": "Gdef"}, "TFAutoModelForVision2Seq": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForVision2Seq", "kind": "Gdef"}, "TFAutoModelForZeroShotImageClassification": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TFAutoModelForZeroShotImageClassification", "kind": "Gdef"}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef"}, "TOKENIZER_MAPPING": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.tokenization_auto.TOKENIZER_MAPPING", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TableQuestionAnsweringArgumentHandler": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.table_question_answering.TableQuestionAnsweringArgumentHandler", "kind": "Gdef"}, "TableQuestionAnsweringPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.table_question_answering.TableQuestionAnsweringPipeline", "kind": "Gdef"}, "Text2TextGenerationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.text2text_generation.Text2TextGenerationPipeline", "kind": "Gdef"}, "TextClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.text_classification.TextClassificationPipeline", "kind": "Gdef"}, "TextGenerationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.text_generation.TextGenerationPipeline", "kind": "Gdef"}, "TextToAudioPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.text_to_audio.TextToAudioPipeline", "kind": "Gdef"}, "TokenClassificationArgumentHandler": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.token_classification.TokenClassificationArgumentHandler", "kind": "Gdef"}, "TokenClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.token_classification.TokenClassificationPipeline", "kind": "Gdef"}, "TranslationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.text2text_generation.TranslationPipeline", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VideoClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.video_classification.VideoClassificationPipeline", "kind": "Gdef"}, "VisualQuestionAnsweringPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.visual_question_answering.VisualQuestionAnsweringPipeline", "kind": "Gdef"}, "ZeroShotAudioClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline", "kind": "Gdef"}, "ZeroShotClassificationArgumentHandler": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.zero_shot_classification.ZeroShotClassificationArgumentHandler", "kind": "Gdef"}, "ZeroShotClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.zero_shot_classification.ZeroShotClassificationPipeline", "kind": "Gdef"}, "ZeroShotImageClassificationPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.zero_shot_image_classification.ZeroShotImageClassificationPipeline", "kind": "Gdef"}, "ZeroShotObjectDetectionPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.zero_shot_object_detection.ZeroShotObjectDetectionPipeline", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cached_file": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.cached_file", "kind": "Gdef"}, "check_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["task"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.check_task", "name": "check_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["task"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_task", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clean_custom_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["task_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.clean_custom_task", "name": "clean_custom_task", "type": null}}, "extract_commit_hash": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.extract_commit_hash", "kind": "Gdef"}, "find_adapter_config_file": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.peft_utils.find_adapter_config_file", "kind": "Gdef"}, "get_class_from_dynamic_module": {".class": "SymbolTableNode", "cross_ref": "transformers.dynamic_module_utils.get_class_from_dynamic_module", "kind": "Gdef"}, "get_default_model_and_revision": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.get_default_model_and_revision", "kind": "Gdef"}, "get_supported_tasks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.get_supported_tasks", "name": "get_supported_tasks", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_supported_tasks", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["model", "token", "deprecated_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.get_task", "name": "get_task", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["model", "token", "deprecated_kwargs"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_task", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "infer_framework_load_model": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.infer_framework_load_model", "kind": "Gdef"}, "is_kenlm_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_kenlm_available", "kind": "Gdef"}, "is_offline_mode": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.is_offline_mode", "kind": "Gdef"}, "is_peft_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_peft_available", "kind": "Gdef"}, "is_pyctcdecode_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_pyctcdecode_available", "kind": "Gdef"}, "is_tf_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_tf_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "model_info": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.hf_api.model_info", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["task", "model", "config", "tokenizer", "feature_extractor", "image_processor", "processor", "framework", "revision", "use_fast", "token", "device", "device_map", "torch_dtype", "trust_remote_code", "model_kwargs", "pipeline_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.pipeline", "name": "pipeline", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["task", "model", "config", "tokenizer", "feature_extractor", "image_processor", "processor", "framework", "revision", "use_fast", "token", "device", "device_map", "torch_dtype", "trust_remote_code", "model_kwargs", "pipeline_class", "kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.modeling_utils.PreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.configuration_utils.PretrainedConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.tokenization_utils.PreTrainedTokenizer", "transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.feature_extraction_utils.PreTrainedFeatureExtractor"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.image_processing_utils.BaseImageProcessor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.processing_utils.ProcessorMixin", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pipeline", "ret_type": "transformers.pipelines.base.Pipeline", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.task", "name": "task", "type": "builtins.str"}}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.pipelines.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.pipelines.tf", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "values": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.values", "name": "values", "type": "builtins.object"}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\__init__.py"}