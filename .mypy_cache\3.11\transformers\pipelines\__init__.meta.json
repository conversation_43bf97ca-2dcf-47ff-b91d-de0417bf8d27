{"data_mtime": 1753783925, "dep_lines": [27, 28, 29, 30, 31, 32, 98, 35, 49, 50, 51, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 87, 88, 89, 90, 91, 92, 23, 24, 25, 26, 33, 34, 35, 145, 146, 147, 15, 16, 17, 18, 19, 21, 113, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 96], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.feature_extraction_auto", "transformers.models.auto.image_processing_auto", "transformers.models.auto.modeling_auto", "transformers.models.auto.processing_auto", "transformers.models.auto.tokenization_auto", "transformers.models.auto.modeling_tf_auto", "transformers.utils.logging", "transformers.pipelines.audio_classification", "transformers.pipelines.automatic_speech_recognition", "transformers.pipelines.base", "transformers.pipelines.depth_estimation", "transformers.pipelines.document_question_answering", "transformers.pipelines.feature_extraction", "transformers.pipelines.fill_mask", "transformers.pipelines.image_classification", "transformers.pipelines.image_feature_extraction", "transformers.pipelines.image_segmentation", "transformers.pipelines.image_text_to_text", "transformers.pipelines.image_to_image", "transformers.pipelines.image_to_text", "transformers.pipelines.mask_generation", "transformers.pipelines.object_detection", "transformers.pipelines.question_answering", "transformers.pipelines.table_question_answering", "transformers.pipelines.text2text_generation", "transformers.pipelines.text_classification", "transformers.pipelines.text_generation", "transformers.pipelines.text_to_audio", "transformers.pipelines.token_classification", "transformers.pipelines.video_classification", "transformers.pipelines.visual_question_answering", "transformers.pipelines.zero_shot_audio_classification", "transformers.pipelines.zero_shot_classification", "transformers.pipelines.zero_shot_image_classification", "transformers.pipelines.zero_shot_object_detection", "transformers.configuration_utils", "transformers.dynamic_module_utils", "transformers.feature_extraction_utils", "transformers.image_processing_utils", "transformers.processing_utils", "transformers.tokenization_utils", "transformers.utils", "transformers.modeling_tf_utils", "transformers.modeling_utils", "transformers.tokenization_utils_fast", "json", "os", "warnings", "pathlib", "typing", "huggingface_hub", "torch", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_collections_abc", "_frozen_importlib", "abc", "torch._C", "torch.nn.modules", "torch.nn.modules.module", "transformers.feature_extraction_sequence_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.generation.utils", "transformers.image_processing_base", "transformers.integrations", "transformers.integrations.peft", "transformers.modelcard", "transformers.models", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.tokenization_utils_base", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "c33836037f1dfa3722d91bd67d27742c4673e8c7", "id": "transformers.pipelines", "ignore_all": true, "interface_hash": "33f092f10198f708d145f72197aae5d1d47cb439", "mtime": 1746815063, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\__init__.py", "plugin_data": null, "size": 55342, "suppressed": ["tensorflow"], "version_id": "1.15.0"}