{"data_mtime": 1753783923, "dep_lines": [38, 22, 23, 24, 21, 22, 30, 31, 14, 15, 16, 18, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19, 28, 395], "dep_prios": [5, 10, 5, 5, 5, 5, 25, 25, 10, 5, 5, 10, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 25, 20], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.pipelines.audio_utils", "transformers.pipelines.base", "transformers.tokenization_utils", "transformers.utils", "transformers.feature_extraction_sequence_utils", "transformers.modeling_utils", "warnings", "collections", "typing", "numpy", "torch", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "_collections_abc", "_frozen_importlib", "abc", "torch._C", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.feature_extraction_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.generation.utils", "transformers.image_processing_base", "transformers.image_processing_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modelcard", "transformers.modeling_tf_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "8eb0794fcba28fcf30fc8cd0b95737b966e86af6", "id": "transformers.pipelines.automatic_speech_recognition", "ignore_all": true, "interface_hash": "a63d8d503153fd9022a8d2942a69406f0daec46a", "mtime": 1746815063, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\automatic_speech_recognition.py", "plugin_data": null, "size": 39330, "suppressed": ["requests", "pyctcdecode", "<PERSON><PERSON><PERSON>"], "version_id": "1.15.0"}