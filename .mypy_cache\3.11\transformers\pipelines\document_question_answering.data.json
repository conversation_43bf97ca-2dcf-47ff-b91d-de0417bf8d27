{".class": "MypyFile", "_fullname": "transformers.pipelines.document_question_answering", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChunkPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.ChunkPipeline", "kind": "Gdef"}, "DocumentQuestionAnsweringPipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.pipelines.base.ChunkPipeline"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", "name": "DocumentQuestionAnsweringPipeline", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.pipelines.document_question_answering", "mro": ["transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", "transformers.pipelines.base.ChunkPipeline", "transformers.pipelines.base.Pipeline", "transformers.pipelines.base._ScikitCompat", "abc.ABC", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "image", "question", "word_boxes", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "image", "question", "word_boxes", "kwargs"], "arg_types": ["transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", {".class": "UnionType", "items": ["PIL.Image.Image", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of DocumentQuestionAnsweringPipeline", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.__init__", "name": "__init__", "type": null}}, "_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "model_inputs", "generate_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline._forward", "name": "_forward", "type": null}}, "_sanitize_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "padding", "doc_stride", "max_question_len", "lang", "tesseract_config", "max_answer_len", "max_seq_len", "top_k", "handle_impossible_answer", "timeout", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline._sanitize_parameters", "name": "_sanitize_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "padding", "doc_stride", "max_question_len", "lang", "tesseract_config", "max_answer_len", "max_seq_len", "top_k", "handle_impossible_answer", "timeout", "kwargs"], "arg_types": ["transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sanitize_parameters of DocumentQuestionAnsweringPipeline", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.model_type", "name": "model_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "postprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "model_outputs", "top_k", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.postprocess", "name": "postprocess", "type": null}}, "postprocess_encoder_decoder_single": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "model_outputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.postprocess_encoder_decoder_single", "name": "postprocess_encoder_decoder_single", "type": null}}, "postprocess_extractive_qa": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "model_outputs", "top_k", "handle_impossible_answer", "max_answer_len", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.postprocess_extractive_qa", "name": "postprocess_extractive_qa", "type": null}}, "preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input", "padding", "doc_stride", "max_seq_len", "word_boxes", "lang", "tesseract_config", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.preprocess", "name": "preprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input", "padding", "doc_stride", "max_seq_len", "word_boxes", "lang", "tesseract_config", "timeout"], "arg_types": ["transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preprocess of DocumentQuestionAnsweringPipeline", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.document_question_answering.DocumentQuestionAnsweringPipeline", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExplicitEnum": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ExplicitEnum", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MODEL_FOR_DOCUMENT_QUESTION_ANSWERING_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.MODEL_FOR_DOCUMENT_QUESTION_ANSWERING_MAPPING_NAMES", "kind": "Gdef"}, "ModelType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ExplicitEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.document_question_answering.ModelType", "name": "ModelType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.pipelines.document_question_answering.ModelType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.pipelines.document_question_answering", "mro": ["transformers.pipelines.document_question_answering.ModelType", "transformers.utils.generic.ExplicitEnum", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "LayoutLM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.document_question_answering.ModelType.LayoutLM", "name": "LayoutLM", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "layoutlm"}, "type_ref": "builtins.str"}}}, "LayoutLMv2andv3": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.document_question_answering.ModelType.LayoutLMv2andv3", "name": "LayoutLMv2andv3", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "layoutlmv2andv3"}, "type_ref": "builtins.str"}}}, "VisionEncoderDecoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.document_question_answering.ModelType.VisionEncoderDecoder", "name": "VisionEncoderDecoder", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "vision_encoder_decoder"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.document_question_answering.ModelType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.document_question_answering.ModelType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TESSERACT_LOADED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.document_question_answering.TESSERACT_LOADED", "name": "TESSERACT_LOADED", "type": "builtins.bool"}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.document_question_answering.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.document_question_answering.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.document_question_answering.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.document_question_answering.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.document_question_answering.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.document_question_answering.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_end_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_end_docstrings", "kind": "Gdef"}, "apply_tesseract": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["image", "lang", "tesseract_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.apply_tesseract", "name": "apply_tesseract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["image", "lang", "tesseract_config"], "arg_types": ["PIL.Image.Image", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_tesseract", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "build_pipeline_init_args": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.build_pipeline_init_args", "kind": "Gdef"}, "is_pytesseract_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_pytesseract_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "is_vision_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_vision_available", "kind": "Gdef"}, "load_image": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.load_image", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.document_question_answering.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "normalize_box": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["box", "width", "height"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.document_question_answering.normalize_box", "name": "normalize_box", "type": null}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "pytesseract": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.pipelines.document_question_answering.pytesseract", "name": "pytesseract", "type": {".class": "AnyType", "missing_import_name": "transformers.pipelines.document_question_answering.pytesseract", "source_any": null, "type_of_any": 3}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "select_starts_ends": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.question_answering.select_starts_ends", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\document_question_answering.py"}