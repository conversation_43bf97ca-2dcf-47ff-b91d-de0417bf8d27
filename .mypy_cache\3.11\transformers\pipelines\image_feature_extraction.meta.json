{"data_mtime": 1753783923, "dep_lines": [4, 3, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.pipelines.base", "transformers.utils", "transformers.image_utils", "typing", "builtins", "paddle", "torch.nn", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "torch", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "warnings", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_frozen_importlib", "abc", "functools", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "transformers.generation", "transformers.generation.tf_utils", "transformers.generation.utils", "transformers.image_processing_base", "transformers.image_processing_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modelcard", "transformers.modeling_tf_utils", "transformers.modeling_utils", "transformers.processing_utils", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "b7542cf6de3ca338d2d397221dd2ed110e139c9c", "id": "transformers.pipelines.image_feature_extraction", "ignore_all": true, "interface_hash": "2f72090b682e8442db827b89b0120b3e3472631d", "mtime": 1746815063, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\image_feature_extraction.py", "plugin_data": null, "size": 4732, "suppressed": [], "version_id": "1.15.0"}