{".class": "MypyFile", "_fullname": "transformers.pipelines.text_classification", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ClassificationFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ExplicitEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.text_classification.ClassificationFunction", "name": "ClassificationFunction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.pipelines.text_classification.ClassificationFunction", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.pipelines.text_classification", "mro": ["transformers.pipelines.text_classification.ClassificationFunction", "transformers.utils.generic.ExplicitEnum", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_classification.ClassificationFunction.NONE", "name": "NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, "type_ref": "builtins.str"}}}, "SIGMOID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_classification.ClassificationFunction.SIGMOID", "name": "SIGMOID", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sigmoid"}, "type_ref": "builtins.str"}}}, "SOFTMAX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_classification.ClassificationFunction.SOFTMAX", "name": "SOFTMAX", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "softmax"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.text_classification.ClassificationFunction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.text_classification.ClassificationFunction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ExplicitEnum": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ExplicitEnum", "kind": "Gdef"}, "GenericTensor": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.GenericTensor", "kind": "Gdef"}, "MODEL_FOR_SEQUENCE_CLASSIFICATION_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.MODEL_FOR_SEQUENCE_CLASSIFICATION_MAPPING_NAMES", "kind": "Gdef"}, "Pipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.Pipeline", "kind": "Gdef"}, "TF_MODEL_FOR_SEQUENCE_CLASSIFICATION_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TF_MODEL_FOR_SEQUENCE_CLASSIFICATION_MAPPING_NAMES", "kind": "Gdef"}, "TextClassificationPipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.pipelines.base.Pipeline"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline", "name": "TextClassificationPipeline", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.pipelines.text_classification", "mro": ["transformers.pipelines.text_classification.TextClassificationPipeline", "transformers.pipelines.base.Pipeline", "transformers.pipelines.base._ScikitCompat", "abc.ABC", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline.__init__", "name": "__init__", "type": null}}, "_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline._forward", "name": "_forward", "type": null}}, "_sanitize_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "return_all_scores", "function_to_apply", "top_k", "tokenizer_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline._sanitize_parameters", "name": "_sanitize_parameters", "type": null}}, "function_to_apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline.function_to_apply", "name": "function_to_apply", "type": "transformers.pipelines.text_classification.ClassificationFunction"}}, "postprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model_outputs", "function_to_apply", "top_k", "_legacy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline.postprocess", "name": "postprocess", "type": null}}, "preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "inputs", "tokenizer_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline.preprocess", "name": "preprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "inputs", "tokenizer_kwargs"], "arg_types": ["transformers.pipelines.text_classification.TextClassificationPipeline", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preprocess of TextClassificationPipeline", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.pipelines.base.GenericTensor"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_all_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline.return_all_scores", "name": "return_all_scores", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.text_classification.TextClassificationPipeline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.text_classification.TextClassificationPipeline", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_classification.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_classification.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_classification.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_classification.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_classification.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_classification.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_end_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_end_docstrings", "kind": "Gdef"}, "build_pipeline_init_args": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.build_pipeline_init_args", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_tf_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_tf_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "sigmoid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.sigmoid", "name": "sigmoid", "type": null}}, "softmax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_classification.softmax", "name": "softmax", "type": null}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\text_classification.py"}