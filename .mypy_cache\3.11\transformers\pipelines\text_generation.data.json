{".class": "MypyFile", "_fullname": "transformers.pipelines.text_generation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Chat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.text_generation.Chat", "name": "Cha<PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.Chat", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.pipelines.text_generation", "mro": ["transformers.pipelines.text_generation.Chat", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "messages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.Chat.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "messages"], "arg_types": ["transformers.pipelines.text_generation.Chat", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "messages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.pipelines.text_generation.Chat.messages", "name": "messages", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.text_generation.Chat.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.text_generation.Chat", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "KeyDataset": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.pt_utils.KeyDataset", "kind": "Gdef"}, "MODEL_FOR_CAUSAL_LM_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.MODEL_FOR_CAUSAL_LM_MAPPING_NAMES", "kind": "Gdef"}, "ModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ModelOutput", "kind": "Gdef"}, "Pipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.Pipeline", "kind": "Gdef"}, "ReturnType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.text_generation.ReturnType", "name": "ReturnType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.pipelines.text_generation.ReturnType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.pipelines.text_generation", "mro": ["transformers.pipelines.text_generation.ReturnType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FULL_TEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_generation.ReturnType.FULL_TEXT", "name": "FULL_TEXT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "NEW_TEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_generation.ReturnType.NEW_TEXT", "name": "NEW_TEXT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "TENSORS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_generation.ReturnType.TENSORS", "name": "TENSORS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.text_generation.ReturnType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.text_generation.ReturnType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TF_MODEL_FOR_CAUSAL_LM_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TF_MODEL_FOR_CAUSAL_LM_MAPPING_NAMES", "kind": "Gdef"}, "TextGenerationPipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.pipelines.base.Pipeline"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline", "name": "TextGenerationPipeline", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.pipelines.text_generation", "mro": ["transformers.pipelines.text_generation.TextGenerationPipeline", "transformers.pipelines.base.Pipeline", "transformers.pipelines.base._ScikitCompat", "abc.ABC", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "XL_PREFIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline.XL_PREFIX", "name": "XL_PREFIX", "type": "builtins.str"}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "text_inputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline.__init__", "name": "__init__", "type": null}}, "_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "model_inputs", "generate_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline._forward", "name": "_forward", "type": null}}, "_parse_and_tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline._parse_and_tokenize", "name": "_parse_and_tokenize", "type": null}}, "_sanitize_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "return_full_text", "return_tensors", "return_text", "return_type", "clean_up_tokenization_spaces", "prefix", "handle_long_generation", "stop_sequence", "truncation", "max_length", "continue_final_message", "generate_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline._sanitize_parameters", "name": "_sanitize_parameters", "type": null}}, "postprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model_outputs", "return_type", "clean_up_tokenization_spaces", "continue_final_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline.postprocess", "name": "postprocess", "type": null}}, "preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "prompt_text", "prefix", "handle_long_generation", "add_special_tokens", "truncation", "padding", "max_length", "continue_final_message", "generate_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline.preprocess", "name": "preprocess", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.text_generation.TextGenerationPipeline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.text_generation.TextGenerationPipeline", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_generation.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_generation.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_generation.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_generation.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_generation.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.text_generation.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_end_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_end_docstrings", "kind": "Gdef"}, "build_pipeline_init_args": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.build_pipeline_init_args", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "is_tf_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_tf_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.pipelines.text_generation.tf", "name": "tf", "type": {".class": "AnyType", "missing_import_name": "transformers.pipelines.text_generation.tf", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\text_generation.py"}