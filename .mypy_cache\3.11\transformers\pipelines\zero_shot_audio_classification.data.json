{".class": "MypyFile", "_fullname": "transformers.pipelines.zero_shot_audio_classification", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Pipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.Pipeline", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UserDict": {".class": "SymbolTableNode", "cross_ref": "collections.UserDict", "kind": "Gdef"}, "ZeroShotAudioClassificationPipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.pipelines.base.Pipeline"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline", "name": "ZeroShotAudioClassificationPipeline", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.pipelines.zero_shot_audio_classification", "mro": ["transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline", "transformers.pipelines.base.Pipeline", "transformers.pipelines.base._ScikitCompat", "abc.ABC", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "audios", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "audios", "kwargs"], "arg_types": ["transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ZeroShotAudioClassificationPipeline", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline.__init__", "name": "__init__", "type": null}}, "_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model_inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline._forward", "name": "_forward", "type": null}}, "_sanitize_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline._sanitize_parameters", "name": "_sanitize_parameters", "type": null}}, "postprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline.postprocess", "name": "postprocess", "type": null}}, "preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "audio", "candidate_labels", "hypothesis_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline.preprocess", "name": "preprocess", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.zero_shot_audio_classification.ZeroShotAudioClassificationPipeline", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.zero_shot_audio_classification.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.zero_shot_audio_classification.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.zero_shot_audio_classification.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.zero_shot_audio_classification.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.zero_shot_audio_classification.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.zero_shot_audio_classification.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_end_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_end_docstrings", "kind": "Gdef"}, "build_pipeline_init_args": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.build_pipeline_init_args", "kind": "Gdef"}, "ffmpeg_read": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.audio_classification.ffmpeg_read", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.zero_shot_audio_classification.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.pipelines.zero_shot_audio_classification.requests", "name": "requests", "type": {".class": "AnyType", "missing_import_name": "transformers.pipelines.zero_shot_audio_classification.requests", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\pipelines\\zero_shot_audio_classification.py"}