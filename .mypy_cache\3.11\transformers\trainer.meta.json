{"data_mtime": 1753783925, "dep_lines": [68, 2991, 54, 58, 64, 65, 137, 180, 181, 188, 22, 34, 41, 47, 50, 52, 53, 57, 59, 60, 61, 62, 63, 66, 67, 72, 73, 74, 78, 79, 89, 111, 136, 137, 223, 1424, 2075, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 47, 48, 49, 52, 56, 223, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2022, 199, 200, 205, 212, 1432, 1677, 2020, 2028, 5224, 199, 200, 205, 206, 212, 232, 233, 247, 523, 1440, 1448, 1472, 1537, 1855, 193, 196, 199, 212, 226, 230, 298, 1249, 1574, 1606, 1637, 1649, 1698, 1855, 1939, 2748], "dep_prios": [5, 20, 5, 5, 5, 5, 10, 5, 5, 5, 10, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 20, 10, 10, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 10, 20, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 10, 10, 10, 10, 20, 20, 20, 20, 20, 20, 20, 20, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 5, 10, 5, 20, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.models.auto.modeling_auto", "torch.nn.modules.module", "torch.utils.data", "transformers.data.data_collator", "transformers.integrations.deepspeed", "transformers.integrations.tpu", "transformers.utils.logging", "transformers.utils.deprecation", "transformers.utils.quantization_config", "transformers.utils.notebook", "importlib.metadata", "collections.abc", "transformers.integrations", "huggingface_hub.utils", "torch.distributed", "packaging.version", "torch.nn", "transformers.configuration_utils", "transformers.debug_utils", "transformers.feature_extraction_sequence_utils", "transformers.feature_extraction_utils", "transformers.hyperparameter_search", "transformers.image_processing_utils", "transformers.modelcard", "transformers.modeling_utils", "transformers.optimization", "transformers.processing_utils", "transformers.pytorch_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.trainer_pt_utils", "transformers.trainer_utils", "transformers.training_args", "transformers.utils", "safetensors.torch", "torch.optim", "transformers.modeling_outputs", "contextlib", "copy", "functools", "glob", "importlib", "inspect", "json", "math", "os", "random", "re", "shutil", "sys", "tempfile", "time", "warnings", "pathlib", "typing", "huggingface_hub", "numpy", "torch", "packaging", "transformers", "safetensors", "builtins", "paddle", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "array", "concurrent", "concurrent.futures", "concurrent.futures._base", "enum", "huggingface_hub._space_api", "huggingface_hub.hf_api", "huggingface_hub.utils.tqdm", "io", "json.decoder", "mmap", "numpy._core", "numpy._core.multiarray", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "torch._C", "torch._C._VariableFunctions", "torch._C._distributed_c10d", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.cuda", "torch.cuda.memory", "torch.distributed.algorithms", "torch.distributed.algorithms.join", "torch.jit", "torch.jit._freeze", "torch.jit._trace", "torch.nn.modules", "torch.nn.modules.sparse", "torch.nn.parallel", "torch.nn.parallel.data_parallel", "torch.nn.parallel.distributed", "torch.nn.parameter", "torch.nn.utils", "torch.optim.lr_scheduler", "torch.optim.optimizer", "torch.serialization", "torch.types", "torch.utils", "torch.utils._contextlib", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "transformers.data", "transformers.generation", "transformers.generation.utils", "transformers.image_processing_base", "transformers.integrations.integration_utils", "transformers.integrations.peft", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "transformers.utils.peft_utils", "types", "typing_extensions"], "hash": "dac8453355bbfeaa29dd98a8b04f90c4b497d83e", "id": "transformers.trainer", "ignore_all": true, "interface_hash": "c1313032a32388d22121fe1ea00980293ee8d344", "mtime": 1746815061, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\trainer.py", "plugin_data": null, "size": 259665, "suppressed": ["torch_xla.distributed.fsdp.wrap", "torch_xla.core.xla_model", "torch_xla.debug.metrics", "torch_xla.distributed.spmd", "smdistributed.modelparallel.torch", "torch_xla.amp.syncfree", "torchao.prototype.low_bit_optim", "torch_xla.distributed.fsdp", "torch_xla.experimental.spmd_fully_sharded_data_parallel", "peft.utils.other", "torch_xla.core", "torch_xla.debug", "torch_xla.distributed", "torch_xla.runtime", "smdistributed.modelparallel", "accelerate.state", "accelerate.utils", "accelerate.data_loader", "liger_kernel.transformers", "torch_npu.optim", "apex.optimizers", "bitsandbytes.optim", "torchdistx.optimizers", "ray.train", "apex", "datasets", "torch_xla", "smdistributed", "peft", "accelerate", "optuna", "bitsandbytes", "galore_torch", "apollo_torch", "lomo_optim", "grokadamw", "schedulefree", "ray", "intel_extension_for_pytorch", "wandb"], "version_id": "1.15.0"}