{"data_mtime": 1753783925, "dep_lines": [23, 24, 26, 27, 28, 30, 31, 40, 22, 29, 30, 41, 42, 43, 44, 45, 46, 47, 48, 15, 16, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 35], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 25, 10, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 10, 10, 5, 5, 5, 10, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch.distributed.fsdp", "torch.utils.data", "transformers.generation.configuration_utils", "transformers.integrations.deepspeed", "transformers.integrations.fsdp", "transformers.utils.logging", "transformers.utils.deprecation", "transformers.data.data_collator", "torch.nn", "transformers.trainer", "transformers.utils", "transformers.feature_extraction_utils", "transformers.image_processing_utils", "transformers.modeling_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "contextlib", "warnings", "copy", "pathlib", "typing", "torch", "builtins", "paddle", "paddle._typing.libs.libpaddle.eager.ops.legacy", "logging", "os", "torch.distributed._functional_collectives", "paddle._typing.libs.libpaddle.op_proto_and_checker_maker", "dataclasses", "multiprocessing.reduction", "paddle._typing.libs.libpaddle.pir", "paddle.base", "math", "paddle._typing.libs.libpaddle.eager", "paddle._typing.libs.libpaddle.var_names", "paddle.base.core", "operator", "collections", "numpy", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.distributed", "torch.distributed._composable_state", "torch.distributed.device_mesh", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp.api", "torch.distributed.fsdp.fully_sharded_data_parallel", "torch.distributed.fsdp.wrap", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "torch.optim", "torch.optim.lr_scheduler", "torch.optim.optimizer", "torch.utils", "torch.utils._contextlib", "torch.utils.data.dataset", "transformers.data", "transformers.generation", "transformers.generation.utils", "transformers.image_processing_base", "transformers.integrations", "transformers.integrations.peft", "transformers.trainer_pt_utils", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "62d4b1d4ab3d30f4856dab3f1475a2cae64119a6", "id": "transformers.trainer_seq2seq", "ignore_all": true, "interface_hash": "8f18d94a0266d3c7f57e7d4d3286e3da739fe2e0", "mtime": 1746815061, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\trainer_seq2seq.py", "plugin_data": null, "size": 18353, "suppressed": ["datasets"], "version_id": "1.15.0"}