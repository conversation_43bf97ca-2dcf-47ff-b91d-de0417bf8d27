{".class": "MypyFile", "_fullname": "transformers.utils.notebook", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "IntervalStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.IntervalStrategy", "kind": "Gdef"}, "NotebookProgressBar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.utils.notebook.NotebookProgressBar", "name": "NotebookProgressBar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressBar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.utils.notebook", "mro": ["transformers.utils.notebook.NotebookProgressBar", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "total", "prefix", "leave", "parent", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressBar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "total", "prefix", "leave", "parent", "width"], "arg_types": ["transformers.utils.notebook.NotebookProgressBar", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["transformers.utils.notebook.NotebookTrainingTracker", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NotebookProgressBar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "average_time_per_item": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.average_time_per_item", "name": "average_time_per_item", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressBar.close", "name": "close", "type": null}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.comment", "name": "comment", "type": {".class": "NoneType"}}}, "display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressBar.display", "name": "display", "type": null}}, "elapsed_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.elapsed_time", "name": "elapsed_time", "type": {".class": "NoneType"}}}, "first_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.first_calls", "name": "first_calls", "type": "builtins.int"}}, "html_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.html_code", "name": "html_code", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "label": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.label", "name": "label", "type": {".class": "NoneType"}}}, "last_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.last_time", "name": "last_time", "type": "builtins.float"}}, "last_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.last_value", "name": "last_value", "type": {".class": "NoneType"}}}, "leave": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.leave", "name": "leave", "type": "builtins.bool"}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.output", "name": "output", "type": {".class": "NoneType"}}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.parent", "name": "parent", "type": {".class": "UnionType", "items": ["transformers.utils.notebook.NotebookTrainingTracker", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "predicted_remaining": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.predicted_remaining", "name": "predicted_remaining", "type": {".class": "NoneType"}}}, "prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.prefix", "name": "prefix", "type": "builtins.str"}}, "start_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.start_time", "name": "start_time", "type": "builtins.float"}}, "start_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.start_value", "name": "start_value", "type": "builtins.int"}}, "total": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.total", "name": "total", "type": "builtins.int"}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "value", "force_update", "comment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressBar.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "value", "force_update", "comment"], "arg_types": ["transformers.utils.notebook.NotebookProgressBar", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of NotebookProgressBar", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_bar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "value", "comment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressBar.update_bar", "name": "update_bar", "type": null}}, "update_every": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.utils.notebook.NotebookProgressBar.update_every", "name": "update_every", "type": "builtins.float"}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.value", "name": "value", "type": {".class": "NoneType"}}}, "wait_for": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.wait_for", "name": "wait_for", "type": "builtins.int"}}, "warmup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.utils.notebook.NotebookProgressBar.warmup", "name": "warmup", "type": "builtins.int"}}, "width": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressBar.width", "name": "width", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.utils.notebook.NotebookProgressBar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.utils.notebook.NotebookProgressBar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotebookProgressCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.utils.notebook.NotebookProgressCallback", "name": "NotebookProgressCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.utils.notebook", "mro": ["transformers.utils.notebook.NotebookProgressCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.__init__", "name": "__init__", "type": null}}, "_force_next_update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressCallback._force_next_update", "name": "_force_next_update", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "first_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressCallback.first_column", "name": "first_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "last_log": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressCallback.last_log", "name": "last_log", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "on_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.on_evaluate", "name": "on_evaluate", "type": null}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.on_log", "name": "on_log", "type": null}}, "on_predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.on_predict", "name": "on_predict", "type": null}}, "on_prediction_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "args", "state", "control", "eval_dataloader", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.on_prediction_step", "name": "on_prediction_step", "type": null}}, "on_step_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.on_step_end", "name": "on_step_end", "type": null}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.on_train_begin", "name": "on_train_begin", "type": null}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookProgressCallback.on_train_end", "name": "on_train_end", "type": null}}, "prediction_bar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressCallback.prediction_bar", "name": "prediction_bar", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "training_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressCallback.training_loss", "name": "training_loss", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "training_tracker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookProgressCallback.training_tracker", "name": "training_tracker", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.utils.notebook.NotebookProgressCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.utils.notebook.NotebookProgressCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotebookTrainingTracker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.notebook.NotebookProgressBar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.utils.notebook.NotebookTrainingTracker", "name": "NotebookTrainingTracker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookTrainingTracker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.utils.notebook", "mro": ["transformers.utils.notebook.NotebookTrainingTracker", "transformers.utils.notebook.NotebookProgressBar", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "num_steps", "column_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookTrainingTracker.__init__", "name": "__init__", "type": null}}, "add_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "total", "prefix", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookTrainingTracker.add_child", "name": "add_child", "type": null}}, "child_bar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookTrainingTracker.child_bar", "name": "child_bar", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "display": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookTrainingTracker.display", "name": "display", "type": null}}, "inner_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.utils.notebook.NotebookTrainingTracker.inner_table", "name": "inner_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "remove_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookTrainingTracker.remove_child", "name": "remove_child", "type": null}}, "write_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.NotebookTrainingTracker.write_line", "name": "write_line", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.utils.notebook.NotebookTrainingTracker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.utils.notebook.NotebookTrainingTracker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.utils.notebook.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.utils.notebook.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.utils.notebook.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.utils.notebook.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.utils.notebook.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.utils.notebook.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "disp": {".class": "SymbolTableNode", "cross_ref": "IPython.display", "kind": "Gdef"}, "format_time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.format_time", "name": "format_time", "type": null}}, "has_length": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.has_length", "kind": "Gdef"}, "html_progress_bar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["value", "total", "prefix", "label", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.html_progress_bar", "name": "html_progress_bar", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "text_to_html_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.utils.notebook.text_to_html_table", "name": "text_to_html_table", "type": null}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\transformers\\utils\\notebook.py"}