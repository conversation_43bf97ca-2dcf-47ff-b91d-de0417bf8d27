{"data_mtime": 1753783515, "dep_lines": [7, 8, 10, 1, 2, 3, 4, 6, 1, 1, 1, 1, 1, 1, 19], "dep_prios": [10, 10, 5, 10, 10, 10, 5, 10, 5, 20, 30, 30, 30, 30, 10], "dependencies": ["click.parser", "click.shell_completion", "typer._completion_shared", "os", "re", "sys", "typing", "click", "builtins", "dataclasses", "_frozen_importlib", "abc", "enum", "typing_extensions"], "hash": "6b0b43a93949c766ad1985b5c2a5b3ed5214efe7", "id": "typer._completion_classes", "ignore_all": true, "interface_hash": "e19bb75dfab90520d2e77c7ebd2bf873e6fe194c", "mtime": 1740598188, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\typer\\_completion_classes.py", "plugin_data": null, "size": 6759, "suppressed": ["shellingham"], "version_id": "1.15.0"}