{"data_mtime": 1753782781, "dep_lines": [6, 7, 8, 10, 11, 13, 14, 1, 1, 1, 1, 1, 9, 15, 199], "dep_prios": [10, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 10, 5, 20], "dependencies": ["os", "sys", "<PERSON><PERSON><PERSON><PERSON>", "librosa", "numpy", "typing", "logging", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "e7732cc5ddbc3a3babae3e30ff58ec73cd23f0a4", "id": "vad_cli", "ignore_all": true, "interface_hash": "e30c6aa4efd610dee99c5da6d6bad109fdbd8c6d", "mtime": 1753782559, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\augment-projects\\confinality\\vad_cli.py", "plugin_data": null, "size": 14455, "suppressed": ["pandas", "tqdm", "scipy"], "version_id": "1.15.0"}