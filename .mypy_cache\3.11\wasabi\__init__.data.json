{".class": "MypyFile", "_fullname": "wasabi", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MESSAGES": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.MESSAGES", "kind": "Gdef"}, "MarkdownRenderer": {".class": "SymbolTableNode", "cross_ref": "wasabi.markdown.<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Printer": {".class": "SymbolTableNode", "cross_ref": "wasabi.printer.Printer", "kind": "Gdef"}, "TracebackPrinter": {".class": "SymbolTableNode", "cross_ref": "wasabi.traceback_printer.TracebackPrinter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wasabi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wasabi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wasabi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wasabi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wasabi.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wasabi.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wasabi.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "color": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.color", "kind": "Gdef"}, "diff_strings": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.diff_strings", "kind": "Gdef"}, "format_repr": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.format_repr", "kind": "Gdef"}, "get_raw_input": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.get_raw_input", "kind": "Gdef"}, "msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wasabi.msg", "name": "msg", "type": "wasabi.printer.Printer"}}, "row": {".class": "SymbolTableNode", "cross_ref": "wasabi.tables.row", "kind": "Gdef"}, "table": {".class": "SymbolTableNode", "cross_ref": "wasabi.tables.table", "kind": "Gdef"}, "wrap": {".class": "SymbolTableNode", "cross_ref": "wasabi.util.wrap", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\wasabi\\__init__.py"}