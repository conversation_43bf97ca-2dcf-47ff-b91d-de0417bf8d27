#!/usr/bin/env python3
"""
Compare the old and new results to see the improvement
"""

import pandas as pd
import ast

def compare_results():
    try:
        # Load both files
        old_df = pd.read_excel('Assessment_Set_july_28_completed.xlsx')
        new_df = pd.read_excel('Assessment_Set_july_28_CORRECTED.xlsx')
        
        print("Comparison of Old vs New Results")
        print("=" * 50)
        
        # Compare first few files
        print("\nFirst 5 files comparison:")
        print("-" * 30)
        
        for i in range(5):
            # Find rows with audio files
            old_audio_rows = old_df[old_df.iloc[:, 1].astype(str).str.contains('.wav', na=False)]
            new_audio_rows = new_df[new_df.iloc[:, 1].astype(str).str.contains('.wav', na=False)]
            
            if i < len(old_audio_rows) and i < len(new_audio_rows):
                old_row = old_audio_rows.iloc[i]
                new_row = new_audio_rows.iloc[i]
                
                filename = old_row.iloc[1]
                old_timestamps = old_row.get('speech_timestamps', '')
                new_timestamps = new_row.get('speech_timestamps', '')
                
                print(f"\nFile: {filename}")
                
                # Count segments
                try:
                    old_segments = ast.literal_eval(old_timestamps) if old_timestamps and old_timestamps != '' else []
                    old_count = len(old_segments)
                    old_duration = sum(seg['end'] - seg['start'] for seg in old_segments) if old_segments else 0
                except:
                    old_count = 0
                    old_duration = 0
                
                try:
                    new_segments = ast.literal_eval(new_timestamps) if new_timestamps and new_timestamps != '' else []
                    new_count = len(new_segments)
                    new_duration = sum(seg['end'] - seg['start'] for seg in new_segments) if new_segments else 0
                except:
                    new_count = 0
                    new_duration = 0
                
                print(f"  Old: {old_count} segments, {old_duration:.1f}s total")
                print(f"  New: {new_count} segments, {new_duration:.1f}s total")
                print(f"  Change: {new_count - old_count:+d} segments, {new_duration - old_duration:+.1f}s")
        
        # Overall statistics
        print(f"\n" + "=" * 50)
        print("Overall Statistics:")
        print("-" * 20)
        
        old_audio_rows = old_df[old_df.iloc[:, 1].astype(str).str.contains('.wav', na=False)]
        new_audio_rows = new_df[new_df.iloc[:, 1].astype(str).str.contains('.wav', na=False)]
        
        old_total_segments = 0
        old_total_duration = 0
        new_total_segments = 0
        new_total_duration = 0
        
        for _, row in old_audio_rows.iterrows():
            timestamps = row.get('speech_timestamps', '')
            try:
                segments = ast.literal_eval(timestamps) if timestamps and timestamps != '' else []
                old_total_segments += len(segments)
                old_total_duration += sum(seg['end'] - seg['start'] for seg in segments) if segments else 0
            except:
                pass
        
        for _, row in new_audio_rows.iterrows():
            timestamps = row.get('speech_timestamps', '')
            try:
                segments = ast.literal_eval(timestamps) if timestamps and timestamps != '' else []
                new_total_segments += len(segments)
                new_total_duration += sum(seg['end'] - seg['start'] for seg in segments) if segments else 0
            except:
                pass
        
        print(f"Old algorithm: {old_total_segments} total segments, {old_total_duration:.1f}s total speech")
        print(f"New algorithm: {new_total_segments} total segments, {new_total_duration:.1f}s total speech")
        print(f"Change: {new_total_segments - old_total_segments:+d} segments, {new_total_duration - old_total_duration:+.1f}s")
        
        avg_old = old_total_segments / len(old_audio_rows) if len(old_audio_rows) > 0 else 0
        avg_new = new_total_segments / len(new_audio_rows) if len(new_audio_rows) > 0 else 0
        
        print(f"\nAverage segments per file:")
        print(f"  Old: {avg_old:.1f} segments/file")
        print(f"  New: {avg_new:.1f} segments/file")
        
        print(f"\n✅ The new algorithm is more conservative and should be more accurate")
        print(f"   for detecting actual human speech (not 'uh', 'umm', breathing, etc.)")
        
        return True
        
    except Exception as e:
        print(f"Error comparing results: {e}")
        return False

if __name__ == "__main__":
    success = compare_results()
    if success:
        print(f"\n🎉 Comparison completed!")
        print(f"📁 Use the CORRECTED file: Assessment_Set_july_28_CORRECTED.xlsx")
    else:
        print(f"\n❌ Comparison failed!")
