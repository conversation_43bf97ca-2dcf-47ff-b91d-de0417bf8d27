#!/usr/bin/env python3
"""
Example usage of the Voice Activity Detection Tool
This script demonstrates how to use the VAD functionality programmatically.
"""

import os
import sys
from vad_cli import VoiceActivityDetector
import pandas as pd

def example_single_file():
    """Example: Process a single audio file."""
    print("Example 1: Processing a single audio file")
    print("-" * 40)
    
    # Initialize VAD
    vad = VoiceActivityDetector(aggressiveness=2)
    
    # Example audio file (replace with actual path)
    audio_file = "example_audio.wav"
    
    if os.path.exists(audio_file):
        try:
            segments = vad.detect_speech_segments(audio_file)
            print(f"Audio file: {audio_file}")
            print(f"Speech segments found: {len(segments)}")
            print(f"Timestamps: {segments}")
            
            # Calculate total speech duration
            total_duration = sum(seg['end'] - seg['start'] for seg in segments)
            print(f"Total speech duration: {total_duration:.1f} seconds")
            
        except Exception as e:
            print(f"Error processing {audio_file}: {e}")
    else:
        print(f"Example audio file '{audio_file}' not found.")
        print("Please provide a valid audio file path to test.")
    
    print()

def example_batch_processing():
    """Example: Batch process multiple files."""
    print("Example 2: Batch processing multiple files")
    print("-" * 40)
    
    # Example folder structure
    audio_folder = "./example_audio_files"
    excel_file = "./example_tags.xlsx"
    
    if os.path.exists(audio_folder) and os.path.exists(excel_file):
        try:
            # Initialize VAD
            vad = VoiceActivityDetector(aggressiveness=2)
            
            # Load Excel file
            df = pd.read_excel(excel_file)
            print(f"Loaded Excel file with {len(df)} rows")
            
            # Find audio files
            audio_files = []
            for ext in ['.wav', '.mp3', '.m4a', '.flac']:
                audio_files.extend([f for f in os.listdir(audio_folder) 
                                  if f.lower().endswith(ext)])
            
            print(f"Found {len(audio_files)} audio files")
            
            # Process first few files as example
            for i, audio_file in enumerate(audio_files[:3]):  # Process first 3 files
                audio_path = os.path.join(audio_folder, audio_file)
                segments = vad.detect_speech_segments(audio_path)
                print(f"  {audio_file}: {len(segments)} segments")
                
        except Exception as e:
            print(f"Error in batch processing: {e}")
    else:
        print("Example files not found. This is just a demonstration.")
        print("For actual usage, provide real audio folder and Excel file paths.")
    
    print()

def example_custom_settings():
    """Example: Using different VAD settings."""
    print("Example 3: Comparing different VAD aggressiveness levels")
    print("-" * 40)
    
    audio_file = "example_audio.wav"
    
    if os.path.exists(audio_file):
        for aggressiveness in [0, 1, 2, 3]:
            try:
                vad = VoiceActivityDetector(aggressiveness=aggressiveness)
                segments = vad.detect_speech_segments(audio_file)
                total_duration = sum(seg['end'] - seg['start'] for seg in segments)
                
                print(f"Aggressiveness {aggressiveness}: {len(segments)} segments, "
                      f"{total_duration:.1f}s total speech")
                
            except Exception as e:
                print(f"Error with aggressiveness {aggressiveness}: {e}")
    else:
        print("Example audio file not found.")
        print("Results would show how different aggressiveness levels affect detection:")
        print("  Aggressiveness 0: More segments (less strict)")
        print("  Aggressiveness 3: Fewer segments (more strict)")
    
    print()

def create_sample_excel():
    """Create a sample Excel file for testing."""
    print("Creating sample Excel file...")
    
    # Sample data
    data = {
        'audio_tag': [
            'aca2_t4_10763',
            'aca2_t4_10764',
            'aca2_t4_10765',
            'example_audio_1',
            'example_audio_2'
        ],
        'description': [
            'Sample audio file 1',
            'Sample audio file 2', 
            'Sample audio file 3',
            'Example file 1',
            'Example file 2'
        ],
        'speech_timestamps': [''] * 5  # Empty column to be filled
    }
    
    df = pd.DataFrame(data)
    df.to_excel('sample_tags.xlsx', index=False)
    print("Created 'sample_tags.xlsx' with sample data")

def main():
    """Run all examples."""
    print("🎧 Voice Activity Detection Tool - Usage Examples")
    print("=" * 60)
    print()
    
    # Run examples
    example_single_file()
    example_batch_processing()
    example_custom_settings()
    
    # Offer to create sample file
    response = input("Create sample Excel file for testing? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        create_sample_excel()
    
    print("\n" + "=" * 60)
    print("For the actual Confinality assessment:")
    print("1. Download the 100 audio files from the provided Google Drive link")
    print("2. Download the Excel file with audio tags")
    print("3. Run: python vad_tool.py (for GUI) or python vad_cli.py (for CLI)")
    print("4. Select your files and process them")
    print("5. Submit the completed Excel file")
    print("\nGood luck! 🚀")

if __name__ == "__main__":
    main()
