#!/usr/bin/env python3
"""
Final test to ensure the algorithm is working correctly
"""

import os
from vad_cli import VoiceActivityDetector

def final_test():
    # Get a few different audio files to test
    wav_files = [f for f in os.listdir('.') if f.lower().endswith('.wav')]
    test_files = wav_files[:3]  # Test first 3 files
    
    print("Final Algorithm Test")
    print("=" * 50)
    print("Testing the improved speech detection algorithm")
    print("that should detect ONLY actual human speech words")
    print("(not 'uh', 'umm', breathing, background noise, etc.)")
    print()
    
    # Test with aggressiveness level 3 (most conservative)
    vad = VoiceActivityDetector(aggressiveness=3)
    
    for i, test_file in enumerate(test_files):
        print(f"File {i+1}: {test_file}")
        print("-" * 40)
        
        try:
            segments = vad.detect_speech_segments(test_file)
            
            if segments:
                total_duration = sum(seg['end'] - seg['start'] for seg in segments)
                print(f"✅ Found {len(segments)} speech segments")
                print(f"📊 Total speech duration: {total_duration:.1f} seconds")
                print(f"📝 Segments: {segments}")
                
                # Calculate average segment length
                avg_length = total_duration / len(segments)
                print(f"📏 Average segment length: {avg_length:.2f} seconds")
                
                if avg_length < 0.1:
                    print("⚠️  Warning: Very short segments detected - might include noise")
                elif avg_length > 2.0:
                    print("⚠️  Warning: Very long segments - might include pauses")
                else:
                    print("✅ Segment lengths look reasonable for speech")
                    
            else:
                print("❌ No speech detected")
                print("   This might indicate the algorithm is too strict,")
                print("   or the file truly has no clear speech")
            
        except Exception as e:
            print(f"❌ Error processing {test_file}: {e}")
        
        print()
    
    print("=" * 50)
    print("RECOMMENDATION:")
    print("The CORRECTED file (Assessment_Set_july_28_CORRECTED.xlsx)")
    print("uses aggressiveness level 2, which provides a good balance")
    print("between detecting actual speech and avoiding false positives.")
    print()
    print("Key improvements in the new algorithm:")
    print("✅ Uses multiple audio features (not just energy)")
    print("✅ Filters by frequency characteristics of human speech")
    print("✅ Requires minimum segment duration (0.15s)")
    print("✅ Applies spectral analysis to distinguish speech from noise")
    print("✅ More conservative thresholds to avoid 'uh', 'umm', breathing")
    print()
    print("📁 Submit: Assessment_Set_july_28_CORRECTED.xlsx")

if __name__ == "__main__":
    final_test()
