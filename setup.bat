@echo off
echo ========================================
echo Voice Activity Detection Tool Setup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

REM Run the Python setup script
echo Running setup script...
python setup.py

if errorlevel 1 (
    echo.
    echo Setup failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo To test the installation:
echo   python test_installation.py
echo.
echo To use the GUI tool:
echo   python vad_tool.py
echo.
echo To use the command-line tool:
echo   python vad_cli.py --help
echo.
pause
