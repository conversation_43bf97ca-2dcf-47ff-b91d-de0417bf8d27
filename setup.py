#!/usr/bin/env python3
"""
Setup script for Voice Activity Detection Tool
Installs all required dependencies and sets up the environment.
"""

import subprocess
import sys
import os
import platform

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"✓ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install all required dependencies."""
    print("📦 Installing dependencies...")
    
    # Read requirements from file
    if os.path.exists("requirements.txt"):
        with open("requirements.txt", "r") as f:
            packages = [line.strip() for line in f if line.strip() and not line.startswith("#")]
    else:
        # Fallback list if requirements.txt doesn't exist
        packages = [
            "numpy>=1.21.0",
            "librosa>=0.9.0",
            "soundfile>=0.10.0",
            "webrtcvad>=2.0.10",
            "pandas>=1.3.0",
            "openpyxl>=3.0.0",
            "matplotlib>=3.5.0",
            "scipy>=1.7.0",
            "pydub>=0.25.0",
            "tqdm>=4.60.0"
        ]
    
    failed_packages = []
    
    for package in packages:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✓ {package} installed successfully")
        else:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  Failed to install: {', '.join(failed_packages)}")
        print("Please install these packages manually using:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    
    print("\n✅ All dependencies installed successfully!")
    return True

def check_audio_support():
    """Check if audio libraries are working correctly."""
    print("\n🔊 Testing audio support...")
    
    try:
        import librosa
        import soundfile
        import webrtcvad
        print("✓ Audio libraries imported successfully")
        
        # Test WebRTC VAD
        vad = webrtcvad.Vad(2)
        print("✓ WebRTC VAD initialized successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Audio library import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Audio library test failed: {e}")
        return False

def create_test_script():
    """Create a simple test script to verify installation."""
    test_script = """#!/usr/bin/env python3
# Test script for Voice Activity Detection Tool

import sys
import os

def test_imports():
    try:
        import numpy
        import librosa
        import soundfile
        import webrtcvad
        import pandas
        import tkinter
        print("✅ All imports successful!")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_vad():
    try:
        import webrtcvad
        vad = webrtcvad.Vad(2)
        print("✅ VAD initialization successful!")
        return True
    except Exception as e:
        print(f"❌ VAD test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Voice Activity Detection Tool installation...")
    
    success = True
    success &= test_imports()
    success &= test_vad()
    
    if success:
        print("\\n🎉 Installation test passed! You're ready to use the VAD tool.")
    else:
        print("\\n❌ Installation test failed. Please check the error messages above.")
        sys.exit(1)
"""
    
    with open("test_installation.py", "w") as f:
        f.write(test_script)
    
    print("✓ Created test_installation.py")

def main():
    """Main setup function."""
    print("🚀 Voice Activity Detection Tool Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed due to dependency installation issues.")
        sys.exit(1)
    
    # Check audio support
    if not check_audio_support():
        print("\n❌ Setup failed due to audio library issues.")
        sys.exit(1)
    
    # Create test script
    create_test_script()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run 'python test_installation.py' to verify installation")
    print("2. Use 'python vad_tool.py' for GUI interface")
    print("3. Use 'python vad_cli.py --help' for command-line interface")
    print("\nFor detailed instructions, see README.md")

if __name__ == "__main__":
    main()
