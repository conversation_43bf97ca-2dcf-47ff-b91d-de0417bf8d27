#!/usr/bin/env python3
"""
Test different aggressiveness levels to find the best setting
"""

import os
from vad_cli import VoiceActivityDetector

def test_aggressiveness_levels():
    # Get the first WAV file
    wav_files = [f for f in os.listdir('.') if f.lower().endswith('.wav')]
    if not wav_files:
        print("No WAV files found!")
        return
    
    test_file = wav_files[0]
    print(f"Testing different aggressiveness levels on: {test_file}")
    print("=" * 60)
    
    for aggressiveness in [0, 1, 2, 3]:
        try:
            print(f"\nAggressiveness Level {aggressiveness}:")
            print("-" * 30)
            
            # Initialize VAD with different aggressiveness
            vad = VoiceActivityDetector(aggressiveness=aggressiveness)
            
            # Process the file
            segments = vad.detect_speech_segments(test_file)
            
            print(f"Found {len(segments)} speech segments")
            if segments:
                total_duration = sum(seg['end'] - seg['start'] for seg in segments)
                print(f"Total speech duration: {total_duration:.1f} seconds")
                print(f"Segments: {segments}")
            else:
                print("No speech detected")
                
        except Exception as e:
            print(f"Error with aggressiveness {aggressiveness}: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("Recommendation: Choose the aggressiveness level that gives")
    print("reasonable results - not too many short segments, not zero segments")

if __name__ == "__main__":
    test_aggressiveness_levels()
