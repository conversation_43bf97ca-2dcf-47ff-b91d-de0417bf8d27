#!/usr/bin/env python3
"""
Command-line Voice Activity Detection Tool for Confinality Assessment
"""

import os
import sys
import argparse
import pandas as pd
import librosa
import numpy as np
# import webrtcvad  # Commented out due to Windows build issues
from typing import List, Dict, Tuple
import logging
from tqdm import tqdm

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VoiceActivityDetector:
    """Core VAD functionality using librosa-based energy detection."""

    def __init__(self, aggressiveness=2):
        """
        Initialize VAD with specified aggressiveness level.

        Args:
            aggressiveness (int): VAD aggressiveness (0-3, higher = more aggressive)
        """
        self.aggressiveness = aggressiveness
        self.sample_rate = 22050  # Standard librosa sample rate
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)

        # Thresholds based on aggressiveness level
        self.energy_thresholds = {
            0: 0.001,  # Least aggressive (more permissive)
            1: 0.005,  # Mildly aggressive
            2: 0.01,   # Moderately aggressive (default)
            3: 0.02    # Most aggressive (strictest)
        }
        self.energy_threshold = self.energy_thresholds.get(aggressiveness, 0.01)
        
    def preprocess_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """
        Load and preprocess audio file for VAD.

        Args:
            audio_path (str): Path to audio file

        Returns:
            Tuple[np.ndarray, int]: Processed audio data and sample rate
        """
        try:
            # Load audio using librosa
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)

            # Normalize audio
            if np.max(np.abs(audio)) > 0:
                audio = audio / np.max(np.abs(audio))

            return audio, self.sample_rate

        except Exception as e:
            logger.error(f"Error preprocessing audio {audio_path}: {e}")
            raise
    
    def detect_speech_segments(self, audio_path: str) -> List[Dict[str, float]]:
        """
        Detect actual human speech segments (spoken words only) using advanced spectral analysis.

        Args:
            audio_path (str): Path to audio file

        Returns:
            List[Dict[str, float]]: List of speech segments with start/end times
        """
        try:
            audio_data, sr = self.preprocess_audio(audio_path)

            # Use smaller hop length for better precision
            hop_length = 512  # ~23ms at 22050 Hz
            frame_length = 2048  # ~93ms at 22050 Hz

            # Extract multiple features for better speech detection
            features = self._extract_speech_features(audio_data, sr, hop_length, frame_length)

            # Combine features to detect actual speech
            speech_frames = self._classify_speech_frames(features)

            # Convert frame-level decisions to time segments
            segments = self._frames_to_segments(speech_frames, hop_length, sr)

            # Merge segments that are close together (< 120ms gap)
            merged_segments = self._merge_close_segments(segments, gap_threshold=0.12)

            return merged_segments

        except Exception as e:
            logger.error(f"Error detecting speech in {audio_path}: {e}")
            return []

    def _extract_speech_features(self, audio_data: np.ndarray, sr: int, hop_length: int, frame_length: int) -> Dict[str, np.ndarray]:
        """Extract features that help distinguish speech from noise/non-speech."""

        # 1. Spectral centroid (brightness of sound)
        spectral_centroids = librosa.feature.spectral_centroid(
            y=audio_data, sr=sr, hop_length=hop_length
        )[0]

        # 2. Zero crossing rate (indicates voicing)
        zcr = librosa.feature.zero_crossing_rate(
            audio_data, frame_length=frame_length, hop_length=hop_length
        )[0]

        # 3. RMS energy
        rms_energy = librosa.feature.rms(
            y=audio_data, frame_length=frame_length, hop_length=hop_length
        )[0]

        # 4. Spectral rolloff (frequency below which 85% of energy is contained)
        spectral_rolloff = librosa.feature.spectral_rolloff(
            y=audio_data, sr=sr, hop_length=hop_length
        )[0]

        # 5. MFCC features (first few coefficients for speech characteristics)
        mfccs = librosa.feature.mfcc(
            y=audio_data, sr=sr, n_mfcc=4, hop_length=hop_length
        )

        return {
            'spectral_centroids': spectral_centroids,
            'zcr': zcr,
            'rms_energy': rms_energy,
            'spectral_rolloff': spectral_rolloff,
            'mfccs': mfccs
        }

    def _classify_speech_frames(self, features: Dict[str, np.ndarray]) -> np.ndarray:
        """Classify frames as speech or non-speech based on multiple features."""

        # Get feature arrays
        spectral_centroids = features['spectral_centroids']
        zcr = features['zcr']
        rms_energy = features['rms_energy']
        spectral_rolloff = features['spectral_rolloff']
        mfccs = features['mfccs']

        # Normalize features
        def normalize_feature(feature):
            if np.std(feature) > 0:
                return (feature - np.mean(feature)) / np.std(feature)
            return feature

        norm_centroids = normalize_feature(spectral_centroids)
        norm_zcr = normalize_feature(zcr)
        norm_energy = normalize_feature(rms_energy)
        norm_rolloff = normalize_feature(spectral_rolloff)

        # More conservative speech detection criteria
        # Calculate dynamic thresholds based on the audio content
        energy_mean = np.mean(rms_energy)
        energy_std = np.std(rms_energy)

        # Adaptive energy threshold based on aggressiveness
        energy_percentiles = {
            0: 30,  # Less strict - lower percentile
            1: 40,  # Moderate
            2: 50,  # More strict (default)
            3: 60   # Very strict
        }

        energy_percentile = energy_percentiles.get(self.aggressiveness, 50)
        energy_threshold = np.percentile(rms_energy, energy_percentile)

        # Multi-criteria speech detection - balanced for actual speech
        speech_frames = (
            # Energy criterion (basic activity)
            (rms_energy > energy_threshold) &

            # Spectral centroid criterion (human speech frequency range - more permissive)
            (spectral_centroids > 500) & (spectral_centroids < 4000) &

            # Zero crossing rate criterion (speech characteristics - more permissive)
            (zcr > 0.01) & (zcr < 0.35) &

            # Spectral rolloff criterion (speech energy distribution - more permissive)
            (spectral_rolloff > 1000) & (spectral_rolloff < 8000)
        )

        # Apply morphological operations to clean up detection
        speech_frames = self._smooth_speech_detection(speech_frames)

        return speech_frames

    def _smooth_speech_detection(self, speech_frames: np.ndarray) -> np.ndarray:
        """Apply smoothing to remove isolated detections and fill small gaps."""
        from scipy import ndimage

        # Convert to int for morphological operations
        speech_int = speech_frames.astype(int)

        # Remove isolated single-frame detections
        speech_int = ndimage.binary_opening(speech_int, structure=np.ones(3))

        # Fill small gaps (up to 3 frames)
        speech_int = ndimage.binary_closing(speech_int, structure=np.ones(5))

        return speech_int.astype(bool)
    
    def _frames_to_segments(self, speech_frames: np.ndarray, hop_length: int, sr: int) -> List[Dict[str, float]]:
        """Convert frame-level speech decisions to time segments."""
        segments = []
        in_speech = False
        start_frame = 0
        min_segment_duration = 0.15  # Minimum duration for actual words (slightly reduced)

        for i, is_speech in enumerate(speech_frames):
            if is_speech and not in_speech:
                # Start of speech segment
                in_speech = True
                start_frame = i
            elif not is_speech and in_speech:
                # End of speech segment
                in_speech = False
                start_time = start_frame * hop_length / sr
                end_time = i * hop_length / sr

                # Only add segments that are long enough to be actual words
                if end_time - start_time >= min_segment_duration:
                    segments.append({'start': round(start_time, 1), 'end': round(end_time, 1)})

        # Handle case where speech continues to end of file
        if in_speech:
            start_time = start_frame * hop_length / sr
            end_time = len(speech_frames) * hop_length / sr

            # Only add if long enough
            if end_time - start_time >= min_segment_duration:
                segments.append({'start': round(start_time, 1), 'end': round(end_time, 1)})

        return segments
    
    def _merge_close_segments(self, segments: List[Dict[str, float]], gap_threshold: float = 0.12) -> List[Dict[str, float]]:
        """Merge segments that are close together."""
        if not segments:
            return segments
        
        merged = [segments[0]]
        
        for current in segments[1:]:
            last = merged[-1]
            gap = current['start'] - last['end']
            
            if gap <= gap_threshold:
                # Merge segments
                merged[-1]['end'] = current['end']
            else:
                merged.append(current)
        
        return merged


def process_audio_files(audio_folder: str, excel_file: str, output_file: str, aggressiveness: int = 2):
    """
    Process all audio files and update Excel file with timestamps.
    
    Args:
        audio_folder (str): Path to folder containing audio files
        excel_file (str): Path to input Excel file
        output_file (str): Path to output Excel file
        aggressiveness (int): VAD aggressiveness level (0-3)
    """
    try:
        # Initialize VAD
        vad = VoiceActivityDetector(aggressiveness)
        
        # Load Excel file
        print("Loading Excel file...")
        df = pd.read_excel(excel_file)
        
        # Find audio files
        audio_files = []
        for ext in ['.wav', '.mp3', '.m4a', '.flac']:
            audio_files.extend([f for f in os.listdir(audio_folder) 
                              if f.lower().endswith(ext)])
        
        print(f"Found {len(audio_files)} audio files to process.")
        
        # Process each audio file with progress bar
        processed_count = 0
        for audio_file in tqdm(audio_files, desc="Processing audio files"):
            try:
                audio_path = os.path.join(audio_folder, audio_file)
                segments = vad.detect_speech_segments(audio_path)
                
                # Find corresponding row in Excel
                matching_rows = df[df.iloc[:, 1] == audio_file]
                
                if not matching_rows.empty:
                    row_index = matching_rows.index[0]
                    timestamps_str = str(segments) if segments else "[]"
                    
                    # Update the speech_timestamps column
                    if 'speech_timestamps' in df.columns:
                        df.at[row_index, 'speech_timestamps'] = timestamps_str
                    else:
                        # Create the column if it doesn't exist
                        df['speech_timestamps'] = ""
                        df.at[row_index, 'speech_timestamps'] = timestamps_str
                    
                    print(f"✓ {audio_file}: {len(segments)} segments found")
                    processed_count += 1
                else:
                    print(f"⚠ {audio_file}: No matching row found in Excel")
                
            except Exception as e:
                print(f"✗ {audio_file}: Error - {str(e)}")
                logger.error(f"Error processing {audio_file}: {e}")
        
        # Save updated Excel file
        print("Saving results...")
        df.to_excel(output_file, index=False)
        
        print(f"\n🎉 Processing complete!")
        print(f"Processed {processed_count}/{len(audio_files)} files.")
        print(f"Results saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error during processing: {str(e)}")
        logger.error(f"Error during processing: {e}")
        sys.exit(1)


def main():
    """Main entry point for command-line interface."""
    parser = argparse.ArgumentParser(
        description="Voice Activity Detection Tool for Confinality Assessment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python vad_cli.py -a /path/to/audio/folder -e input.xlsx -o output.xlsx
  python vad_cli.py -a ./audio -e tags.xlsx -o results.xlsx --aggressiveness 3
        """
    )
    
    parser.add_argument('-a', '--audio-folder', required=True,
                       help='Path to folder containing audio files')
    parser.add_argument('-e', '--excel-file', required=True,
                       help='Path to input Excel file with audio tags')
    parser.add_argument('-o', '--output-file', required=True,
                       help='Path to output Excel file')
    parser.add_argument('--aggressiveness', type=int, default=2, choices=[0, 1, 2, 3],
                       help='VAD aggressiveness level (0-3, default: 2)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate inputs
    if not os.path.exists(args.audio_folder):
        print(f"Error: Audio folder '{args.audio_folder}' does not exist.")
        sys.exit(1)
    
    if not os.path.exists(args.excel_file):
        print(f"Error: Excel file '{args.excel_file}' does not exist.")
        sys.exit(1)
    
    # Process audio files
    process_audio_files(args.audio_folder, args.excel_file, args.output_file, args.aggressiveness)


if __name__ == "__main__":
    main()
