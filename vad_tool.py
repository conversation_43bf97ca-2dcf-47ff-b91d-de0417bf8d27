#!/usr/bin/env python3
"""
Voice Activity Detection Tool for Confinality Assessment
Automatically detects speech segments in audio files and generates timestamps.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import librosa
import numpy as np
# import webrtcvad  # Commented out due to Windows build issues
import soundfile as sf
from pydub import AudioSegment
import threading
import json
from typing import List, Dict, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VoiceActivityDetector:
    """Core VAD functionality using librosa-based energy detection."""

    def __init__(self, aggressiveness=2):
        """
        Initialize VAD with specified aggressiveness level.

        Args:
            aggressiveness (int): VAD aggressiveness (0-3, higher = more aggressive)
        """
        self.aggressiveness = aggressiveness
        self.sample_rate = 22050  # Standard librosa sample rate
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)

        # Thresholds based on aggressiveness level
        self.energy_thresholds = {
            0: 0.001,  # Least aggressive (more permissive)
            1: 0.005,  # Mildly aggressive
            2: 0.01,   # Moderately aggressive (default)
            3: 0.02    # Most aggressive (strictest)
        }
        self.energy_threshold = self.energy_thresholds.get(aggressiveness, 0.01)
        
    def preprocess_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """
        Load and preprocess audio file for VAD.

        Args:
            audio_path (str): Path to audio file

        Returns:
            Tuple[np.ndarray, int]: Processed audio data and sample rate
        """
        try:
            # Load audio using librosa
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)

            # Normalize audio
            if np.max(np.abs(audio)) > 0:
                audio = audio / np.max(np.abs(audio))

            return audio, self.sample_rate

        except Exception as e:
            logger.error(f"Error preprocessing audio {audio_path}: {e}")
            raise
    
    def detect_speech_segments(self, audio_path: str) -> List[Dict[str, float]]:
        """
        Detect actual human speech segments (spoken words only) using advanced spectral analysis.

        Args:
            audio_path (str): Path to audio file

        Returns:
            List[Dict[str, float]]: List of speech segments with start/end times
        """
        try:
            audio_data, sr = self.preprocess_audio(audio_path)

            # Use smaller hop length for better precision
            hop_length = 512  # ~23ms at 22050 Hz
            frame_length = 2048  # ~93ms at 22050 Hz

            # Extract multiple features for better speech detection
            features = self._extract_speech_features(audio_data, sr, hop_length, frame_length)

            # Combine features to detect actual speech
            speech_frames = self._classify_speech_frames(features)

            # Convert frame-level decisions to time segments
            segments = self._frames_to_segments(speech_frames, hop_length, sr)

            # Merge segments that are close together (< 120ms gap)
            merged_segments = self._merge_close_segments(segments, gap_threshold=0.12)

            return merged_segments

        except Exception as e:
            logger.error(f"Error detecting speech in {audio_path}: {e}")
            return []

    def _extract_speech_features(self, audio_data: np.ndarray, sr: int, hop_length: int, frame_length: int) -> Dict[str, np.ndarray]:
        """Extract features that help distinguish speech from noise/non-speech."""

        # 1. Spectral centroid (brightness of sound)
        spectral_centroids = librosa.feature.spectral_centroid(
            y=audio_data, sr=sr, hop_length=hop_length
        )[0]

        # 2. Zero crossing rate (indicates voicing)
        zcr = librosa.feature.zero_crossing_rate(
            audio_data, frame_length=frame_length, hop_length=hop_length
        )[0]

        # 3. RMS energy
        rms_energy = librosa.feature.rms(
            y=audio_data, frame_length=frame_length, hop_length=hop_length
        )[0]

        # 4. Spectral rolloff (frequency below which 85% of energy is contained)
        spectral_rolloff = librosa.feature.spectral_rolloff(
            y=audio_data, sr=sr, hop_length=hop_length
        )[0]

        # 5. MFCC features (first few coefficients for speech characteristics)
        mfccs = librosa.feature.mfcc(
            y=audio_data, sr=sr, n_mfcc=4, hop_length=hop_length
        )

        return {
            'spectral_centroids': spectral_centroids,
            'zcr': zcr,
            'rms_energy': rms_energy,
            'spectral_rolloff': spectral_rolloff,
            'mfccs': mfccs
        }

    def _classify_speech_frames(self, features: Dict[str, np.ndarray]) -> np.ndarray:
        """Classify frames as speech or non-speech based on multiple features."""

        # Get feature arrays
        spectral_centroids = features['spectral_centroids']
        zcr = features['zcr']
        rms_energy = features['rms_energy']
        spectral_rolloff = features['spectral_rolloff']
        mfccs = features['mfccs']

        # Normalize features
        def normalize_feature(feature):
            if np.std(feature) > 0:
                return (feature - np.mean(feature)) / np.std(feature)
            return feature

        norm_centroids = normalize_feature(spectral_centroids)
        norm_zcr = normalize_feature(zcr)
        norm_energy = normalize_feature(rms_energy)
        norm_rolloff = normalize_feature(spectral_rolloff)

        # More conservative speech detection criteria
        # Calculate dynamic thresholds based on the audio content
        energy_mean = np.mean(rms_energy)
        energy_std = np.std(rms_energy)

        # Adaptive energy threshold based on aggressiveness
        energy_percentiles = {
            0: 30,  # Less strict - lower percentile
            1: 40,  # Moderate
            2: 50,  # More strict (default)
            3: 60   # Very strict
        }

        energy_percentile = energy_percentiles.get(self.aggressiveness, 50)
        energy_threshold = np.percentile(rms_energy, energy_percentile)

        # Multi-criteria speech detection - balanced for actual speech
        speech_frames = (
            # Energy criterion (basic activity)
            (rms_energy > energy_threshold) &

            # Spectral centroid criterion (human speech frequency range - more permissive)
            (spectral_centroids > 500) & (spectral_centroids < 4000) &

            # Zero crossing rate criterion (speech characteristics - more permissive)
            (zcr > 0.01) & (zcr < 0.35) &

            # Spectral rolloff criterion (speech energy distribution - more permissive)
            (spectral_rolloff > 1000) & (spectral_rolloff < 8000)
        )

        # Apply morphological operations to clean up detection
        speech_frames = self._smooth_speech_detection(speech_frames)

        return speech_frames

    def _smooth_speech_detection(self, speech_frames: np.ndarray) -> np.ndarray:
        """Apply smoothing to remove isolated detections and fill small gaps."""
        from scipy import ndimage

        # Convert to int for morphological operations
        speech_int = speech_frames.astype(int)

        # Remove isolated single-frame detections
        speech_int = ndimage.binary_opening(speech_int, structure=np.ones(3))

        # Fill small gaps (up to 3 frames)
        speech_int = ndimage.binary_closing(speech_int, structure=np.ones(5))

        return speech_int.astype(bool)
    
    def _frames_to_segments(self, speech_frames: np.ndarray, hop_length: int, sr: int) -> List[Dict[str, float]]:
        """Convert frame-level speech decisions to time segments."""
        segments = []
        in_speech = False
        start_frame = 0
        min_segment_duration = 0.15  # Minimum duration for actual words (slightly reduced)

        for i, is_speech in enumerate(speech_frames):
            if is_speech and not in_speech:
                # Start of speech segment
                in_speech = True
                start_frame = i
            elif not is_speech and in_speech:
                # End of speech segment
                in_speech = False
                start_time = start_frame * hop_length / sr
                end_time = i * hop_length / sr

                # Only add segments that are long enough to be actual words
                if end_time - start_time >= min_segment_duration:
                    segments.append({'start': round(start_time, 1), 'end': round(end_time, 1)})

        # Handle case where speech continues to end of file
        if in_speech:
            start_time = start_frame * hop_length / sr
            end_time = len(speech_frames) * hop_length / sr

            # Only add if long enough
            if end_time - start_time >= min_segment_duration:
                segments.append({'start': round(start_time, 1), 'end': round(end_time, 1)})

        return segments
    
    def _merge_close_segments(self, segments: List[Dict[str, float]], gap_threshold: float = 0.12) -> List[Dict[str, float]]:
        """Merge segments that are close together."""
        if not segments:
            return segments
        
        merged = [segments[0]]
        
        for current in segments[1:]:
            last = merged[-1]
            gap = current['start'] - last['end']
            
            if gap <= gap_threshold:
                # Merge segments
                merged[-1]['end'] = current['end']
            else:
                merged.append(current)
        
        return merged


class VADToolGUI:
    """GUI application for the Voice Activity Detection tool."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Voice Activity Detection Tool - Confinality Assessment")
        self.root.geometry("800x600")
        
        self.vad = VoiceActivityDetector()
        self.audio_folder = ""
        self.excel_file = ""
        self.output_file = ""
        
        self.setup_gui()
        
    def setup_gui(self):
        """Set up the GUI components."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Voice Activity Detection Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # File selection section
        ttk.Label(main_frame, text="1. Select Audio Folder:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.audio_folder_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.audio_folder_var, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(main_frame, text="Browse", command=self.select_audio_folder).grid(row=1, column=2)
        
        ttk.Label(main_frame, text="2. Select Excel File:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.excel_file_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.excel_file_var, width=50).grid(row=2, column=1, padx=5)
        ttk.Button(main_frame, text="Browse", command=self.select_excel_file).grid(row=2, column=2)
        
        ttk.Label(main_frame, text="3. Output File:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.output_file_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.output_file_var, width=50).grid(row=3, column=1, padx=5)
        ttk.Button(main_frame, text="Save As", command=self.select_output_file).grid(row=3, column=2)
        
        # Processing options
        options_frame = ttk.LabelFrame(main_frame, text="Processing Options", padding="10")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        ttk.Label(options_frame, text="VAD Aggressiveness (0-3):").grid(row=0, column=0, sticky=tk.W)
        self.aggressiveness_var = tk.IntVar(value=2)
        ttk.Scale(options_frame, from_=0, to=3, variable=self.aggressiveness_var, 
                 orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # Process button
        self.process_button = ttk.Button(main_frame, text="Process All Audio Files", 
                                       command=self.start_processing, style="Accent.TButton")
        self.process_button.grid(row=5, column=0, columnspan=3, pady=20)
        
        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        self.progress_var = tk.StringVar(value="Ready to process...")
        ttk.Label(progress_frame, textvariable=self.progress_var).grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # Results text area
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        self.results_text = tk.Text(results_frame, height=10, width=80)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)
        progress_frame.columnconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
    
    def select_audio_folder(self):
        """Select the folder containing audio files."""
        folder = filedialog.askdirectory(title="Select Audio Folder")
        if folder:
            self.audio_folder = folder
            self.audio_folder_var.set(folder)
    
    def select_excel_file(self):
        """Select the Excel file with audio tags."""
        file = filedialog.askopenfilename(
            title="Select Excel File",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if file:
            self.excel_file = file
            self.excel_file_var.set(file)
    
    def select_output_file(self):
        """Select output file location."""
        file = filedialog.asksaveasfilename(
            title="Save Output File As",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file:
            self.output_file = file
            self.output_file_var.set(file)
    
    def start_processing(self):
        """Start processing audio files in a separate thread."""
        if not self.validate_inputs():
            return
        
        # Update VAD aggressiveness
        self.vad = VoiceActivityDetector(self.aggressiveness_var.get())
        
        # Disable process button
        self.process_button.config(state='disabled')
        
        # Start processing in separate thread
        thread = threading.Thread(target=self.process_audio_files)
        thread.daemon = True
        thread.start()
    
    def validate_inputs(self):
        """Validate user inputs."""
        if not self.audio_folder or not os.path.exists(self.audio_folder):
            messagebox.showerror("Error", "Please select a valid audio folder.")
            return False
        
        if not self.excel_file or not os.path.exists(self.excel_file):
            messagebox.showerror("Error", "Please select a valid Excel file.")
            return False
        
        if not self.output_file:
            messagebox.showerror("Error", "Please specify an output file location.")
            return False
        
        return True
    
    def process_audio_files(self):
        """Process all audio files and update Excel file."""
        try:
            # Load Excel file
            self.update_progress("Loading Excel file...")
            df = pd.read_excel(self.excel_file)
            
            # Find audio files
            audio_files = []
            for ext in ['.wav', '.mp3', '.m4a', '.flac']:
                audio_files.extend([f for f in os.listdir(self.audio_folder) 
                                  if f.lower().endswith(ext)])
            
            total_files = len(audio_files)
            self.progress_bar.config(maximum=total_files)
            
            self.update_results(f"Found {total_files} audio files to process.\n")
            
            # Process each audio file
            processed_count = 0
            for i, audio_file in enumerate(audio_files):
                try:
                    self.update_progress(f"Processing {audio_file}... ({i+1}/{total_files})")
                    
                    audio_path = os.path.join(self.audio_folder, audio_file)
                    segments = self.vad.detect_speech_segments(audio_path)
                    
                    # Find corresponding row in Excel
                    matching_rows = df[df.iloc[:, 1] == audio_file]
                    
                    if not matching_rows.empty:
                        row_index = matching_rows.index[0]
                        timestamps_str = str(segments) if segments else "[]"
                        
                        # Update the speech_timestamps column
                        if 'speech_timestamps' in df.columns:
                            df.at[row_index, 'speech_timestamps'] = timestamps_str
                        else:
                            # Create the column if it doesn't exist
                            df['speech_timestamps'] = ""
                            df.at[row_index, 'speech_timestamps'] = timestamps_str
                        
                        self.update_results(f"✓ {audio_file}: {len(segments)} segments found\n")
                        processed_count += 1
                    else:
                        self.update_results(f"⚠ {audio_file}: No matching row found in Excel\n")
                    
                    self.progress_bar.config(value=i+1)
                    self.root.update_idletasks()
                    
                except Exception as e:
                    self.update_results(f"✗ {audio_file}: Error - {str(e)}\n")
                    logger.error(f"Error processing {audio_file}: {e}")
            
            # Save updated Excel file
            self.update_progress("Saving results...")
            df.to_excel(self.output_file, index=False)
            
            self.update_progress(f"Complete! Processed {processed_count}/{total_files} files.")
            self.update_results(f"\n🎉 Processing complete! Results saved to: {self.output_file}\n")
            
            messagebox.showinfo("Success", f"Processing complete!\nProcessed {processed_count}/{total_files} files.\nResults saved to: {self.output_file}")
            
        except Exception as e:
            error_msg = f"Error during processing: {str(e)}"
            self.update_results(f"\n❌ {error_msg}\n")
            messagebox.showerror("Error", error_msg)
            logger.error(error_msg)
        
        finally:
            # Re-enable process button
            self.process_button.config(state='normal')
    
    def update_progress(self, message):
        """Update progress message."""
        self.progress_var.set(message)
        self.root.update_idletasks()
    
    def update_results(self, message):
        """Update results text area."""
        self.results_text.insert(tk.END, message)
        self.results_text.see(tk.END)
        self.root.update_idletasks()
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()


def main():
    """Main entry point."""
    try:
        app = VADToolGUI()
        app.run()
    except Exception as e:
        logger.error(f"Application error: {e}")
        messagebox.showerror("Error", f"Application error: {e}")


if __name__ == "__main__":
    main()
