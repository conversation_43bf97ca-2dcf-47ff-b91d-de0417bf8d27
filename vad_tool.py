#!/usr/bin/env python3
"""
Voice Activity Detection Tool for Confinality Assessment
Automatically detects speech segments in audio files and generates timestamps.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import librosa
import numpy as np
import webrtcvad
import soundfile as sf
from pydub import AudioSegment
import threading
import json
from typing import List, Dict, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VoiceActivityDetector:
    """Core VAD functionality using WebRTC VAD and librosa."""
    
    def __init__(self, aggressiveness=2):
        """
        Initialize VAD with specified aggressiveness level.
        
        Args:
            aggressiveness (int): VAD aggressiveness (0-3, higher = more aggressive)
        """
        self.vad = webrtcvad.Vad(aggressiveness)
        self.sample_rate = 16000  # WebRTC VAD requires 16kHz
        self.frame_duration = 30  # ms
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)
        
    def preprocess_audio(self, audio_path: str) -> Tuple[np.ndarray, int]:
        """
        Load and preprocess audio file for VAD.
        
        Args:
            audio_path (str): Path to audio file
            
        Returns:
            Tuple[np.ndarray, int]: Processed audio data and sample rate
        """
        try:
            # Load audio using librosa
            audio, sr = librosa.load(audio_path, sr=None)
            
            # Convert to 16kHz mono if needed
            if sr != self.sample_rate:
                audio = librosa.resample(audio, orig_sr=sr, target_sr=self.sample_rate)
            
            # Normalize audio
            audio = audio / np.max(np.abs(audio)) if np.max(np.abs(audio)) > 0 else audio
            
            # Convert to int16 for WebRTC VAD
            audio_int16 = (audio * 32767).astype(np.int16)
            
            return audio_int16, self.sample_rate
            
        except Exception as e:
            logger.error(f"Error preprocessing audio {audio_path}: {e}")
            raise
    
    def detect_speech_segments(self, audio_path: str) -> List[Dict[str, float]]:
        """
        Detect speech segments in audio file.
        
        Args:
            audio_path (str): Path to audio file
            
        Returns:
            List[Dict[str, float]]: List of speech segments with start/end times
        """
        try:
            audio_data, sr = self.preprocess_audio(audio_path)
            
            # Process audio in frames
            speech_frames = []
            for i in range(0, len(audio_data) - self.frame_size + 1, self.frame_size):
                frame = audio_data[i:i + self.frame_size]
                
                # Ensure frame is exactly the right size
                if len(frame) == self.frame_size:
                    frame_bytes = frame.tobytes()
                    is_speech = self.vad.is_speech(frame_bytes, self.sample_rate)
                    speech_frames.append(is_speech)
                else:
                    speech_frames.append(False)
            
            # Convert frame-level decisions to time segments
            segments = self._frames_to_segments(speech_frames)
            
            # Merge segments that are close together (< 120ms gap)
            merged_segments = self._merge_close_segments(segments, gap_threshold=0.12)
            
            return merged_segments
            
        except Exception as e:
            logger.error(f"Error detecting speech in {audio_path}: {e}")
            return []
    
    def _frames_to_segments(self, speech_frames: List[bool]) -> List[Dict[str, float]]:
        """Convert frame-level speech decisions to time segments."""
        segments = []
        in_speech = False
        start_frame = 0
        
        for i, is_speech in enumerate(speech_frames):
            if is_speech and not in_speech:
                # Start of speech segment
                in_speech = True
                start_frame = i
            elif not is_speech and in_speech:
                # End of speech segment
                in_speech = False
                start_time = start_frame * self.frame_duration / 1000.0
                end_time = i * self.frame_duration / 1000.0
                segments.append({'start': round(start_time, 1), 'end': round(end_time, 1)})
        
        # Handle case where speech continues to end of file
        if in_speech:
            start_time = start_frame * self.frame_duration / 1000.0
            end_time = len(speech_frames) * self.frame_duration / 1000.0
            segments.append({'start': round(start_time, 1), 'end': round(end_time, 1)})
        
        return segments
    
    def _merge_close_segments(self, segments: List[Dict[str, float]], gap_threshold: float = 0.12) -> List[Dict[str, float]]:
        """Merge segments that are close together."""
        if not segments:
            return segments
        
        merged = [segments[0]]
        
        for current in segments[1:]:
            last = merged[-1]
            gap = current['start'] - last['end']
            
            if gap <= gap_threshold:
                # Merge segments
                merged[-1]['end'] = current['end']
            else:
                merged.append(current)
        
        return merged


class VADToolGUI:
    """GUI application for the Voice Activity Detection tool."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Voice Activity Detection Tool - Confinality Assessment")
        self.root.geometry("800x600")
        
        self.vad = VoiceActivityDetector()
        self.audio_folder = ""
        self.excel_file = ""
        self.output_file = ""
        
        self.setup_gui()
        
    def setup_gui(self):
        """Set up the GUI components."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Voice Activity Detection Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # File selection section
        ttk.Label(main_frame, text="1. Select Audio Folder:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.audio_folder_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.audio_folder_var, width=50).grid(row=1, column=1, padx=5)
        ttk.Button(main_frame, text="Browse", command=self.select_audio_folder).grid(row=1, column=2)
        
        ttk.Label(main_frame, text="2. Select Excel File:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.excel_file_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.excel_file_var, width=50).grid(row=2, column=1, padx=5)
        ttk.Button(main_frame, text="Browse", command=self.select_excel_file).grid(row=2, column=2)
        
        ttk.Label(main_frame, text="3. Output File:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.output_file_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.output_file_var, width=50).grid(row=3, column=1, padx=5)
        ttk.Button(main_frame, text="Save As", command=self.select_output_file).grid(row=3, column=2)
        
        # Processing options
        options_frame = ttk.LabelFrame(main_frame, text="Processing Options", padding="10")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        ttk.Label(options_frame, text="VAD Aggressiveness (0-3):").grid(row=0, column=0, sticky=tk.W)
        self.aggressiveness_var = tk.IntVar(value=2)
        ttk.Scale(options_frame, from_=0, to=3, variable=self.aggressiveness_var, 
                 orient=tk.HORIZONTAL).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # Process button
        self.process_button = ttk.Button(main_frame, text="Process All Audio Files", 
                                       command=self.start_processing, style="Accent.TButton")
        self.process_button.grid(row=5, column=0, columnspan=3, pady=20)
        
        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="10")
        progress_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        self.progress_var = tk.StringVar(value="Ready to process...")
        ttk.Label(progress_frame, textvariable=self.progress_var).grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # Results text area
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        self.results_text = tk.Text(results_frame, height=10, width=80)
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(7, weight=1)
        progress_frame.columnconfigure(0, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
    
    def select_audio_folder(self):
        """Select the folder containing audio files."""
        folder = filedialog.askdirectory(title="Select Audio Folder")
        if folder:
            self.audio_folder = folder
            self.audio_folder_var.set(folder)
    
    def select_excel_file(self):
        """Select the Excel file with audio tags."""
        file = filedialog.askopenfilename(
            title="Select Excel File",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if file:
            self.excel_file = file
            self.excel_file_var.set(file)
    
    def select_output_file(self):
        """Select output file location."""
        file = filedialog.asksaveasfilename(
            title="Save Output File As",
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file:
            self.output_file = file
            self.output_file_var.set(file)
    
    def start_processing(self):
        """Start processing audio files in a separate thread."""
        if not self.validate_inputs():
            return
        
        # Update VAD aggressiveness
        self.vad = VoiceActivityDetector(self.aggressiveness_var.get())
        
        # Disable process button
        self.process_button.config(state='disabled')
        
        # Start processing in separate thread
        thread = threading.Thread(target=self.process_audio_files)
        thread.daemon = True
        thread.start()
    
    def validate_inputs(self):
        """Validate user inputs."""
        if not self.audio_folder or not os.path.exists(self.audio_folder):
            messagebox.showerror("Error", "Please select a valid audio folder.")
            return False
        
        if not self.excel_file or not os.path.exists(self.excel_file):
            messagebox.showerror("Error", "Please select a valid Excel file.")
            return False
        
        if not self.output_file:
            messagebox.showerror("Error", "Please specify an output file location.")
            return False
        
        return True
    
    def process_audio_files(self):
        """Process all audio files and update Excel file."""
        try:
            # Load Excel file
            self.update_progress("Loading Excel file...")
            df = pd.read_excel(self.excel_file)
            
            # Find audio files
            audio_files = []
            for ext in ['.wav', '.mp3', '.m4a', '.flac']:
                audio_files.extend([f for f in os.listdir(self.audio_folder) 
                                  if f.lower().endswith(ext)])
            
            total_files = len(audio_files)
            self.progress_bar.config(maximum=total_files)
            
            self.update_results(f"Found {total_files} audio files to process.\n")
            
            # Process each audio file
            processed_count = 0
            for i, audio_file in enumerate(audio_files):
                try:
                    self.update_progress(f"Processing {audio_file}... ({i+1}/{total_files})")
                    
                    audio_path = os.path.join(self.audio_folder, audio_file)
                    segments = self.vad.detect_speech_segments(audio_path)
                    
                    # Find corresponding row in Excel
                    filename_without_ext = os.path.splitext(audio_file)[0]
                    matching_rows = df[df.iloc[:, 0].astype(str).str.contains(filename_without_ext, na=False)]
                    
                    if not matching_rows.empty:
                        row_index = matching_rows.index[0]
                        timestamps_str = str(segments) if segments else "[]"
                        
                        # Update the speech_timestamps column
                        if 'speech_timestamps' in df.columns:
                            df.at[row_index, 'speech_timestamps'] = timestamps_str
                        else:
                            # Create the column if it doesn't exist
                            df['speech_timestamps'] = ""
                            df.at[row_index, 'speech_timestamps'] = timestamps_str
                        
                        self.update_results(f"✓ {audio_file}: {len(segments)} segments found\n")
                        processed_count += 1
                    else:
                        self.update_results(f"⚠ {audio_file}: No matching row found in Excel\n")
                    
                    self.progress_bar.config(value=i+1)
                    self.root.update_idletasks()
                    
                except Exception as e:
                    self.update_results(f"✗ {audio_file}: Error - {str(e)}\n")
                    logger.error(f"Error processing {audio_file}: {e}")
            
            # Save updated Excel file
            self.update_progress("Saving results...")
            df.to_excel(self.output_file, index=False)
            
            self.update_progress(f"Complete! Processed {processed_count}/{total_files} files.")
            self.update_results(f"\n🎉 Processing complete! Results saved to: {self.output_file}\n")
            
            messagebox.showinfo("Success", f"Processing complete!\nProcessed {processed_count}/{total_files} files.\nResults saved to: {self.output_file}")
            
        except Exception as e:
            error_msg = f"Error during processing: {str(e)}"
            self.update_results(f"\n❌ {error_msg}\n")
            messagebox.showerror("Error", error_msg)
            logger.error(error_msg)
        
        finally:
            # Re-enable process button
            self.process_button.config(state='normal')
    
    def update_progress(self, message):
        """Update progress message."""
        self.progress_var.set(message)
        self.root.update_idletasks()
    
    def update_results(self, message):
        """Update results text area."""
        self.results_text.insert(tk.END, message)
        self.results_text.see(tk.END)
        self.root.update_idletasks()
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()


def main():
    """Main entry point."""
    try:
        app = VADToolGUI()
        app.run()
    except Exception as e:
        logger.error(f"Application error: {e}")
        messagebox.showerror("Error", f"Application error: {e}")


if __name__ == "__main__":
    main()
