#!/usr/bin/env python3
"""
Verify the results in the completed Excel file
"""

import pandas as pd
import ast

def verify_results():
    try:
        # Load the corrected Excel file
        df = pd.read_excel('Assessment_Set_july_28_CORRECTED.xlsx')
        
        print(f"Completed Excel file has {len(df)} rows and {len(df.columns)} columns")
        print(f"Columns: {df.columns.tolist()}")
        
        # Check if speech_timestamps column exists
        if 'speech_timestamps' in df.columns:
            print("✅ 'speech_timestamps' column exists")
            
            # Count non-empty timestamps
            timestamps_col = df['speech_timestamps']
            non_empty = timestamps_col.dropna()
            non_empty = non_empty[non_empty != '']
            non_empty = non_empty[non_empty != '[]']
            
            print(f"✅ {len(non_empty)} files have speech timestamps")
            print(f"📊 {len(timestamps_col) - len(non_empty)} files have no speech detected")
            
            # Show some examples
            print("\nFirst 5 files with timestamps:")
            for i, (idx, row) in enumerate(df.iterrows()):
                if pd.notna(row.get('speech_timestamps')) and row.get('speech_timestamps') != '' and row.get('speech_timestamps') != '[]':
                    filename = row.iloc[1] if len(row) > 1 else 'Unknown'
                    timestamps = row['speech_timestamps']
                    print(f"  {filename}: {timestamps}")
                    if i >= 4:  # Show first 5
                        break
            
            # Validate timestamp format
            print("\nValidating timestamp format...")
            valid_count = 0
            for idx, row in df.iterrows():
                timestamps_str = row.get('speech_timestamps')
                if pd.notna(timestamps_str) and timestamps_str != '' and timestamps_str != '[]':
                    try:
                        # Try to parse the timestamps
                        timestamps = ast.literal_eval(timestamps_str)
                        if isinstance(timestamps, list):
                            # Check if all segments have start and end
                            for segment in timestamps:
                                if isinstance(segment, dict) and 'start' in segment and 'end' in segment:
                                    if segment['start'] < segment['end']:
                                        valid_count += 1
                                        break
                    except:
                        pass
            
            print(f"✅ {valid_count} files have valid timestamp format")
            
        else:
            print("❌ 'speech_timestamps' column not found")
        
        return True
        
    except Exception as e:
        print(f"Error verifying results: {e}")
        return False

if __name__ == "__main__":
    success = verify_results()
    if success:
        print("\n🎉 Results verification completed!")
    else:
        print("\n❌ Results verification failed!")
